{"name": "temp", "private": true, "version": "1.0", "scripts": {"start": "webpack serve --config webpack/webpack.config.js --env env=dev", "start-server": "http-server dist", "dev": "webpack serve --config webpack/webpack.config.js --env env=dev", "build": "webpack --config webpack/webpack.config.js --env env=prod", "test": "react-scripts test", "eject": "react-scripts eject", "prepare": "husky install", "pre-commit": "lint-staged", "lint": "yarn run eslint --ext .ts,.tsx ."}, "dependencies": {"@babel/plugin-proposal-decorators": "^7.16.4", "@esbuild-plugins/node-globals-polyfill": "^0.1.1", "@esbuild-plugins/node-modules-polyfill": "^0.1.4", "@react-google-maps/api": "^2.19.3", "@reduxjs/toolkit": "^1.8.0", "@stripe/react-stripe-js": "^2.8.0", "@stripe/stripe-js": "^4.3.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/googlemaps": "^3.43.3", "@types/jest": "^26.0.15", "@types/jquery": "^3.5.32", "@types/node": "^12.0.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/react-table": "^7.7.10", "@types/styled-components": "^5.1.17", "apexcharts": "^4.7.0", "axios": "^1.1.3", "babel-plugin-styled-components": "^2.0.1", "browser-image-compression": "^2.0.2", "css-loader": "^6.5.0", "dayjs": "^1.11.7", "diff-match-patch": "^1.0.5", "dotenv": "^16.0.1", "exifr": "^7.1.3", "formBuilder": "^3.19.13", "formik": "^2.2.9", "heic-convert": "^2.1.0", "html2pdf.js": "^0.10.3", "jodit-react": "^5.2.19", "jquery": "^3.7.1", "jquery-ui-dist": "^1.13.3", "jquery-ui-sortable": "^1.0.0", "json-loader": "^0.5.7", "jszip": "^3.10.1", "path": "^0.12.7", "polished": "^4.1.4", "react": "^17.0.2", "react-apexcharts": "^1.7.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^17.0.2", "react-indiana-drag-scroll": "^3.0.3-alpha", "react-markdown": "^8.0.7", "react-places-autocomplete": "^7.3.0", "react-redux": "^7.2.6", "react-router-dom": "6", "react-scripts": "4.0.3", "react-table": "^7.8.0", "react-toastify": "^9.1.1", "react-zoom-pan-pinch": "^3.7.0", "redux": "^4.2.0", "regenerator-runtime": "^0.13.11", "remark-gfm": "^4.0.0", "style-loader": "^3.3.1", "styled-components": "^5.3.0", "tui-image-editor": "^3.15.3", "typescript": "^4.5.4", "web-vitals": "^1.0.1", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-transform-runtime": "^7.16.0", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@commitlint/cli": "^17.0.3", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-places-autocomplete": "^7.2.9", "@types/react-table": "^7.7.10", "@types/styled-components": "^5.1.17", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "babel-loader": "^8.2.3", "commitlint-config-jira": "^1.6.4", "commitlint-plugin-jira-rules": "^1.6.4", "eslint": "^8.18.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-tsdoc": "^0.2.16", "html-webpack-plugin": "^5.5.0", "http-server": "^14.1.1", "husky": "^8.0.0", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "webpack": "^5.64.4", "webpack-cli": "^4.9.1", "webpack-dev-server": "^4.4.0"}, "lint-staged": {"**/*.{ts,tsx}": ["npx prettier --write"]}}