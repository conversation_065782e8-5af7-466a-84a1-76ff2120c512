const webpack = require('webpack')
// const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin')

module.exports = {
  mode: 'development',
  devtool: 'cheap-module-source-map',
  devServer: {
    hot: true,
    open: true,
    historyApiFallback: true,
  },
  // plugins: [
  //   // new ReactRefreshWebpackPlugin(),
  //   new webpack.DefinePlugin({
  //     'process.env.name': JSON.stringify('Aditya'),
  //   }),
  // ],
  plugins: [
    new webpack.ProvidePlugin({
      React: 'react',
    }),
  ],

  ignoreWarnings: [
    {
      module: /node_modules\/exifr\/dist\/full.esm.js/,
      message: /Critical dependency: the request of a dependency is an expression/,
    },
    {
      module: /node_modules\/libheif-js\/libheif-wasm\/libheif-bundle.js/,
      message:
        /Critical dependency: require function is used in a way in which dependencies cannot be statically extracted/,
    },
  ],
}
