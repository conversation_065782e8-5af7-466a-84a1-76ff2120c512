<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Navigation Test</title>
    <style>
        .material-table {
            border: 1px solid #ccc;
            margin: 20px;
            padding: 10px;
        }
        .material-row {
            display: flex;
            margin: 5px 0;
        }
        .material-input {
            margin: 0 10px;
        }
        .unit-select select {
            padding: 5px;
        }
        .focused {
            background-color: yellow;
        }
    </style>
</head>
<body>
    <h1>Tab Navigation Test</h1>
    <p>This test simulates the issue where pressing tab on a unit in the second table highlights the first occurrence.</p>
    
    <!-- First Table -->
    <div class="material-table" data-droppable-id="material1-project1">
        <h3>Table 1 (material1-project1)</h3>
        <div class="material-row" data-item-id="item1">
            <div class="material-input">
                <input type="text" placeholder="Name" />
            </div>
            <div class="material-input">
                <input type="number" placeholder="Amount" />
            </div>
            <div class="material-input unit-select">
                <select>
                    <option>Unit 1</option>
                    <option>Unit 2</option>
                </select>
            </div>
        </div>
        <div class="material-row" data-item-id="item2">
            <div class="material-input">
                <input type="text" placeholder="Name" />
            </div>
            <div class="material-input">
                <input type="number" placeholder="Amount" />
            </div>
            <div class="material-input unit-select">
                <select>
                    <option>Unit 1</option>
                    <option>Unit 2</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Second Table -->
    <div class="material-table" data-droppable-id="material2-project1">
        <h3>Table 2 (material2-project1)</h3>
        <div class="material-row" data-item-id="item3">
            <div class="material-input">
                <input type="text" placeholder="Name" />
            </div>
            <div class="material-input">
                <input type="number" placeholder="Amount" />
            </div>
            <div class="material-input unit-select">
                <select>
                    <option>Unit 1</option>
                    <option>Unit 2</option>
                </select>
            </div>
        </div>
        <div class="material-row" data-item-id="item4">
            <div class="material-input">
                <input type="text" placeholder="Name" />
            </div>
            <div class="material-input">
                <input type="number" placeholder="Amount" />
            </div>
            <div class="material-input unit-select">
                <select>
                    <option>Unit 1</option>
                    <option>Unit 2</option>
                </select>
            </div>
        </div>
    </div>

    <script>
        // Simulate the old behavior (problematic)
        function focusFirstOccurrence() {
            const selectElement = document.querySelector('.unit-select select');
            if (selectElement) {
                selectElement.focus();
                selectElement.classList.add('focused');
                setTimeout(() => selectElement.classList.remove('focused'), 2000);
            }
        }

        // Simulate the new behavior (fixed)
        function focusSpecificElement(droppableId, itemId) {
            const selectElement = document.querySelector(
                `[data-droppable-id="${droppableId}"] [data-item-id="${itemId}"] .unit-select select`
            );
            if (selectElement) {
                selectElement.focus();
                selectElement.classList.add('focused');
                setTimeout(() => selectElement.classList.remove('focused'), 2000);
            }
        }

        // Add test buttons
        const testContainer = document.createElement('div');
        testContainer.innerHTML = `
            <h3>Test Buttons:</h3>
            <button onclick="focusFirstOccurrence()">Old Behavior: Focus First Unit Select (Always Table 1)</button><br><br>
            <button onclick="focusSpecificElement('material2-project1', 'item3')">New Behavior: Focus Table 2, Item 3</button><br><br>
            <button onclick="focusSpecificElement('material2-project1', 'item4')">New Behavior: Focus Table 2, Item 4</button><br><br>
            <button onclick="focusSpecificElement('material1-project1', 'item2')">New Behavior: Focus Table 1, Item 2</button>
        `;
        document.body.appendChild(testContainer);
    </script>
</body>
</html>
