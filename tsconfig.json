{"compilerOptions": {"target": "es6", "module": "ESNext", "moduleResolution": "node", "lib": ["DOM", "ESNext"], "jsx": "react-jsx", "noEmit": true, "isolatedModules": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "allowSyntheticDefaultImports": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*"]}