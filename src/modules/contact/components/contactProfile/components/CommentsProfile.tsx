import React, { useEffect, useState } from 'react'
import * as Styled from '../style'
import * as SharedStyled from '../../../../../styles/styled'
import { Field, Form, Formik } from 'formik'
import {
  dayjsFormat,
  generateUUID,
  getDataFromLocalStorage,
  getEnumValue,
  isSuccess,
  isWithinHour,
  notify,
} from '../../../../../shared/helpers/util'
import * as Yup from 'yup'
import { useSelector } from 'react-redux'
import { colors } from '../../../../../styles/theme'
import ReactMarkdown from 'react-markdown'
import Button from '../../../../../shared/components/button/Button'
import remarkGfm from 'remark-gfm'
import { I_Comment } from '../../../../newLead/assessmentForm/LeadAssessmentForm'
import { CommentsContainer, MarginBox } from '../../../../newLead/comments/style'
import { createContactComment, deleteContactComment, updateContactComment } from '../../../../../logic/apis/contact'
import { useParams } from 'react-router-dom'
import Pill from './Pill'
import Toggle from '../../../../../shared/toggle/Toggle'
import { Nue } from '../../../../../shared/helpers/constants'
import { EditContainer } from '../../../../opportunity/components/comments/style'
import TextDiffViewer from '../../../../opportunity/components/comments/TextDiffViewer'

const commentSchema = Yup.object().shape({
  comment: Yup.string().required("Can't be empty!"),
})

const CommentsProfile: React.FC<{
  comments: I_Comment[]
  initFetchContact: any
}> = ({ comments, initFetchContact }) => {
  const [commentFlag, setCommentFlag] = useState(false)
  const [commentFlag1, setCommentFlag1] = useState(false)
  const [toggleEdit, setToggleEdit] = useState<{ [key: string]: boolean }>({})
  const [isToggleOn, setIsToggleOn] = useState(false)
  const globalSelector = useSelector((state: any) => state)
  const { currentMember, positionDetails } = globalSelector.company
  const { contactId } = useParams()

  const initComment = {
    comment: getDataFromLocalStorage(`comment-${contactId}`) || '',
  }
  const initUpdateComment = {
    comment: '',
    _id: '',
  }
  const toggleTableData = (type: string) => {
    setToggleEdit((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const submitComment = async (values: typeof initComment) => {
    try {
      setCommentFlag(true)
      setCommentFlag1(true)

      const newComment = {
        createdBy: currentMember._id,
        body: values.comment,
        createdAt: new Date().toISOString(),
        userName: '',
        _id: '',
      }
      const newComments = [newComment, ...comments]
      const response = await createContactComment(
        {
          body: values.comment,
          currDate: new Date().toISOString(),
          memberId: currentMember._id!,
          id: generateUUID()!,
        },
        contactId!
      )
      if (isSuccess(response)) {
        initFetchContact()
        localStorage.removeItem(`comment-${contactId}`)
        notify('Added comment', 'success')
        setCommentFlag(false)
        setCommentFlag1(false)
        localStorage.removeItem(`comment-${contactId}`)
      } else {
        notify('Failed to add comment', 'error')
      }
    } catch (err) {
      console.log('COMMENTING ERR', err)
      setCommentFlag(false)
      setCommentFlag1(false)
    }
  }

  const editCommentComplete = async (values: typeof initUpdateComment) => {
    try {
      setCommentFlag(true)
      console.log({ values }, comments)
      const newComment = {
        createdBy: currentMember._id!,
        body: values.comment,
        createdAt: new Date().toISOString(),
        _id: '',
      }

      const response = await updateContactComment(
        {
          id: values?._id ?? '',
          memberId: currentMember._id!, //UserId##
          body: values?.comment,
          currDate: new Date().toISOString(),
        },
        contactId!,
        values?._id!
      )
      if (isSuccess(response)) {
        initFetchContact()
        setToggleEdit({})
        notify('Edited comment', 'success')
      } else {
        console.log('COMMENT ERR', response?.data?.message)
        notify('Failed to edit comment', 'error')
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setCommentFlag(false)
      setCommentFlag(false)
    }
  }

  const deleteComment = async (index: number) => {
    setCommentFlag(true)
    const res = await deleteContactComment(contactId!, comments[index]._id!)
    if (isSuccess(res)) {
      notify('Deleted comment', 'success')
      setCommentFlag(false)
      setCommentFlag1(true)
    } else {
      console.log('DELETE COMMENT ERR', res?.data)
      notify('Failed to delete comment', 'error')
      setCommentFlag(false)
      setCommentFlag1(true)
    }
  }

  function isLoading(flag1: boolean, flag2: boolean) {
    return flag1 === false && flag2 === false
  }

  return !isLoading(commentFlag, commentFlag1) ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} custMarginTop={'50px'} />
    </>
  ) : (
    <SharedStyled.FlexBox flexDirection="column" gap="12px" width="100%" marginTop="20px">
      <div>
        <Formik
          initialValues={initComment}
          onSubmit={submitComment}
          validationSchema={commentSchema}
          validateOnChange={true}
          validateOnBlur={false}
          enableReinitialize={true}
        >
          {({ touched, errors, resetForm, values, setFieldValue, handleSubmit }) => {
            useEffect(() => {
              if (values?.comment) {
                localStorage.setItem(`comment-${contactId}`, JSON.stringify(values?.comment))
              }
            }, [values?.comment])
            return (
              <Form onSubmit={handleSubmit}>
                <SharedStyled.FlexBox flexDirection="column" alignItems="flex-start">
                  <div style={{ width: '100%' }}>
                    <SharedStyled.FlexRow>
                      <SharedStyled.ContentHeader textAlign="start" as={'h3'}>
                        Comments
                      </SharedStyled.ContentHeader>
                      {!comments?.length ? null : (
                        <Toggle
                          title="Hide Opportunity Comments"
                          className="text bold"
                          customStyles={{
                            margin: '0px',
                            justifyContent: 'flex-end',
                          }}
                          titleStyle={{
                            fontFamily: Nue.regular,
                          }}
                          isToggled={isToggleOn}
                          onToggle={() => {
                            setIsToggleOn((prev) => !prev)
                          }}
                        />
                      )}
                    </SharedStyled.FlexRow>
                    <SharedStyled.TextArea
                      component="textarea"
                      as={Field}
                      name="comment"
                      marginTop="8px"
                      height="52px"
                      stateName="comment"
                      labelName="Comment"
                      placeholder="Add comment"
                      error={touched.comment && errors.comment ? true : false}
                    />
                  </div>
                  <SharedStyled.FlexRow margin="10px 0 0 0">
                    <Button
                      type="button"
                      className="fit"
                      onClick={() => handleSubmit()}
                      disabled={!values.comment?.trim()}
                    >
                      Save Comment
                    </Button>
                  </SharedStyled.FlexRow>
                </SharedStyled.FlexBox>
              </Form>
            )
          }}
        </Formik>
      </div>
      <SharedStyled.FlexBox overflow="auto" flexDirection="column" width="100%" maxHeight="380px">
        {comments
          ?.filter((comment) => (isToggleOn ? !comment?.oppId : true))
          ?.sort((a: any, b: any) => new Date(b?.createdAt)?.getTime() - new Date(a?.createdAt)?.getTime())
          .map((comment: any, idx) => (
            <CommentsContainer key={idx}>
              {toggleEdit[`${idx}`] ? (
                <Formik
                  initialValues={{ comment: comment.body, _id: comment?._id }}
                  onSubmit={editCommentComplete}
                  validationSchema={commentSchema}
                  validateOnChange={true}
                  validateOnBlur={false}
                  enableReinitialize={true}
                >
                  {({ touched, errors, resetForm, values, setFieldValue, handleSubmit }) => (
                    <Form onSubmit={handleSubmit}>
                      <SharedStyled.FlexBox flexDirection="column" alignItems="flex-start">
                        <div style={{ width: '100%' }}>
                          <SharedStyled.TextArea
                            component="textarea"
                            as={Field}
                            name="comment"
                            marginTop="8px"
                            height="52px"
                            stateName="comment"
                            labelName="Comment"
                            placehold="Enter new comment"
                            error={touched.comment && errors.comment ? true : false}
                          />
                        </div>

                        <SharedStyled.FlexBox width="100%" gap="8px" marginTop="10px">
                          <Button type="submit" className="fit">
                            Update
                          </Button>
                          <Button type="button" className="fit" onClick={() => toggleTableData(`${idx}`)}>
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>
                      </SharedStyled.FlexBox>
                    </Form>
                  )}
                </Formik>
              ) : (
                <>
                  <SharedStyled.FlexBox flexDirection="column">
                    <SharedStyled.FlexBox justifyContent="flex-start" margin="0 0 4px 0">
                      <SharedStyled.Text fontSize="14px" fontWeight="bold">
                        {' '}
                        <span style={{ textTransform: 'capitalize' }} className="bold">
                          {comment?.name || comment?.createdBy || 'System'}
                        </span>
                        <SharedStyled.Text fontSize="12px" color={colors.lightGrey}>
                          &emsp;{dayjsFormat(comment?.createdAt, 'M/D/YY hh:mm A')}
                        </SharedStyled.Text>
                      </SharedStyled.Text>
                      <Pill
                        margin="0 0 0 10px"
                        numVal={comment?.oppId ? comment?.num : undefined}
                        path={
                          comment?.oppId ? `/${getEnumValue(comment?.stageGroup)}/opportunity/${comment?.oppId}` : ''
                        }
                        text={comment?.oppId ? `${comment?.PO}-${comment?.num}` : 'Contact'}
                      />

                      <div>
                        <SharedStyled.FlexBox alignItems="center" margin="0 0 0 10px" gap="10px">
                          {comment?.createdBy === currentMember._id &&
                          isWithinHour(comment?.createdAt) &&
                          comment?.edits?.length < 5 ? (
                            <>
                              {!comment?.oppId ? (
                                <EditContainer onClick={() => toggleTableData(`${idx}`)}>
                                  {/* <EditIcon /> */}
                                  <SharedStyled.Text
                                    textDecoration="underline"
                                    fontSize="12px"
                                    color={colors.blueLight}
                                  >
                                    edit
                                  </SharedStyled.Text>
                                </EditContainer>
                              ) : null}

                              {/* <SharedStyled.Text margin="0 0 0 15px" fontSize="12px">
                                                  {comment?.edits?.length < 5
                                                    ? `${5 - Number(comment?.edits?.length || 0)} ${
                                                      5 - Number(comment?.edits?.length || 0) === 1 ? 'edit' : 'edits'
                                                    } left`
                                                    : null}
                                                  </SharedStyled.Text> */}
                            </>
                          ) : null}
                          {comment?.edits?.length > 0 ? (
                            <SharedStyled.Text textAlign="right" fontSize="12px" color={colors.lightGrey}>
                              edited
                            </SharedStyled.Text>
                          ) : null}
                        </SharedStyled.FlexBox>
                      </div>
                    </SharedStyled.FlexBox>

                    <span className="comment">
                      <ReactMarkdown>
                        {comment?.body?.replace(/\n/g, '  \n') || comment?.oppNotes?.replace(/\n/g, '  \n')}
                      </ReactMarkdown>
                    </span>
                  </SharedStyled.FlexBox>

                  <MarginBox>
                    {(positionDetails?.symbol === 'Owner' || positionDetails?.symbol === 'Admin') &&
                    comment?.edits?.length > 0 ? (
                      <div onClick={() => toggleTableData(`${idx} history`)}>
                        {!toggleEdit[`${idx} history`] ? (
                          <EditContainer>
                            <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                              show edits
                            </SharedStyled.Text>
                          </EditContainer>
                        ) : (
                          <EditContainer>
                            <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                              hide edits
                            </SharedStyled.Text>
                          </EditContainer>
                        )}
                      </div>
                    ) : (
                      comment?.createdBy === currentMember._id &&
                      comment?.edits?.length > 0 && (
                        <div onClick={() => toggleTableData(`${idx} history`)}>
                          {/* <SharedStyled.Text fontSize="12px" color={colors.lightGrey}>
                                                    edited -{' '}
                                                  </SharedStyled.Text> */}
                          {!toggleEdit[`${idx} history`] ? (
                            <EditContainer>
                              <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                                show edits
                              </SharedStyled.Text>
                            </EditContainer>
                          ) : (
                            <EditContainer>
                              <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                                hide edits
                              </SharedStyled.Text>
                            </EditContainer>
                          )}
                        </div>
                      )
                    )}
                  </MarginBox>

                  {toggleEdit[`${idx} history`] ? (
                    <MarginBox>
                      {comment?.edits?.length > 0
                        ? comment?.edits?.map((v: any, editIdx: number) => (
                            <SharedStyled.FlexBox justifyContent="space-between">
                              <span className="comment">
                                {/* <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                  {v?.edit?.replace(/\n/g, '  \n')}
                                </ReactMarkdown> */}

                                <TextDiffViewer
                                  original={v?.edit}
                                  edited={editIdx === 0 ? comment?.body : comment?.edits[editIdx - 1]?.edit}
                                />
                              </span>

                              <SharedStyled.Text fontSize="12px" color={colors.lightGrey}>
                                {dayjsFormat(v?.editedAt, 'M/D/YY hh:mm A')}
                              </SharedStyled.Text>
                            </SharedStyled.FlexBox>
                          ))
                        : null}
                    </MarginBox>
                  ) : null}
                </>
              )}
            </CommentsContainer>
          ))}{' '}
        {comments.length === 0 ? <SharedStyled.Text>No comments found!</SharedStyled.Text> : null}
      </SharedStyled.FlexBox>
    </SharedStyled.FlexBox>
  )
}

export default React.memo(CommentsProfile)
