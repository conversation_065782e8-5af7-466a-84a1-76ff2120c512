import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../../styles/styled'

import {
  dayjsFormat,
  extractPermissionByName,
  getEnumValue,
  hasValues,
  isSuccess,
  notify,
} from '../../../../../shared/helpers/util'
import { CustomModal } from '../../../../../shared/customModal/CustomModal'
import { useSelector } from 'react-redux'
import { getActionMembers, getSalesActionByMemberId, updateActivity } from '../../../../../logic/apis/sales'
import * as Yup from 'yup'
import ActionModal from '../../../../opportunity/components/actionModal/ActionModal'
import { getActionsForContactOpp } from '../../../../../logic/apis/contact'

import Pill from './Pill'

import { ActionHistoryCont, TodoNextCont } from '../style'
import ActionItem from './ActionItem'

export interface I_Action {
  type: string
  body: string
  completedBy: string
  assignTo?: string
  due: string
  _id?: string
  oppId?: string
  PO?: string
  num?: string
  contactId?: string
  stageGroup?: string
}

interface I_FormProps {
  contactData: any
  contactOrOppId: string | undefined
  setContactData: React.Dispatch<React.SetStateAction<any | undefined>>
  fetchActivity: () => Promise<void>
  isContact?: boolean
}

export interface I_NextAction {
  _id: string
  type: string
  body: string
  due: string
  createdBy: string
  assignTo?: string
  createdAt: string
}

const todoSchema = Yup.object().shape({
  body: Yup.string().required('Required'),
  due: Yup.string().required('Required'),
  type: Yup.string().required('Required'),
})

const ToDoNextProfile: React.FC<I_FormProps> = (props) => {
  const [actionsForToDoNext, setActionsForToDoNext] = useState<any>([])
  const [allActionData, setAllActionData] = useState<{
    nextAction: I_Action[]
    history: I_Action[]
  }>()
  const [actionModal, setActionModal] = useState(false)
  const [autoFillValues, setAutoFillValues] = useState({ type: '', name: '' })
  const [autoFillValuesFromChild, setAutoFillValuesFromChild] = useState({ type: '', name: '' })
  const [salesPersonDrop, setSalesPersonDrop] = useState<any[]>([])
  const [showHistory, setShowHistory] = useState(false)
  const [actionLoading, setActionLoading] = useState(false)

  const { contactData, contactOrOppId, setContactData, isContact = false, fetchActivity } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, positionDetails } = globalSelector.company

  const hasOppManagedFullPermission =
    hasValues(positionDetails) && extractPermissionByName(positionDetails, 'actions')?.permissions < 3

  useEffect(() => {
    if (currentMember?._id) {
      fetchActions()
      getActivePositionMembers()

      getAllActionsForContactOpp()
    }
  }, [currentMember])

  const getAllActionsForContactOpp = async () => {
    try {
      if (!allActionData?.nextAction?.length) {
        setActionLoading(true)
      }
      const response = await getActionsForContactOpp({ contactOppId: contactOrOppId!, isContact })
      if (isSuccess(response)) {
        setAllActionData(response?.data?.data)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setActionLoading(false)
    }
  }

  const fetchActions = async () => {
    try {
      const res = await getSalesActionByMemberId(currentMember?._id, false)
      if (isSuccess(res)) {
        const { actions } = res.data.data.salesAction
        setActionsForToDoNext(actions)
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const getActivePositionMembers = async () => {
    try {
      const response = await getActionMembers({}, false)
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  return (
    <TodoNextCont>
      <div>
        <SharedStyled.ContentHeader textAlign="left" as="h3">
          To Do Next
        </SharedStyled.ContentHeader>
        {actionLoading ? (
          <SharedStyled.CardSkeleton height="200px"></SharedStyled.CardSkeleton>
        ) : (
          <>
            <>
              {!allActionData?.nextAction?.length ? (
                <ActionItem
                  action={{}}
                  allActionData={allActionData}
                  salesPersonDrop={salesPersonDrop}
                  todoSchema={todoSchema}
                  // onTodoComplete={onTodoComplete}
                  contactData={contactData}
                  hasOppManagedFullPermission={hasOppManagedFullPermission}
                  currentMember={currentMember}
                  actionsForToDoNext={actionsForToDoNext}
                  autoFillValuesFromChild={autoFillValuesFromChild}
                  setAutoFillValues={setAutoFillValues}
                  setActionModal={setActionModal}
                  getAllActionsForContactOpp={getAllActionsForContactOpp}
                  isContact={isContact}
                  contactOrOppId={contactOrOppId}
                  fetchActivity={fetchActivity}
                  idx={0}
                />
              ) : null}

              {allActionData?.nextAction?.map((action: any, idx: number) => (
                <ActionItem
                  action={action}
                  key={action?._id}
                  idx={idx}
                  allActionData={allActionData}
                  salesPersonDrop={salesPersonDrop}
                  todoSchema={todoSchema}
                  // onTodoComplete={onTodoComplete}
                  contactData={contactData}
                  hasOppManagedFullPermission={hasOppManagedFullPermission}
                  currentMember={currentMember}
                  actionsForToDoNext={actionsForToDoNext}
                  autoFillValuesFromChild={autoFillValuesFromChild}
                  setAutoFillValues={setAutoFillValues}
                  setActionModal={setActionModal}
                  getAllActionsForContactOpp={getAllActionsForContactOpp}
                  isContact={isContact}
                  contactOrOppId={contactOrOppId}
                  fetchActivity={fetchActivity}
                />
              ))}

              <SharedStyled.FlexRow justifyContent="flex-end" margin="10px 0">
                {allActionData?.history?.length ? (
                  <span onClick={() => setShowHistory((prev) => !prev)} style={{ cursor: 'pointer', fontSize: '14px' }}>
                    {showHistory ? 'Hide' : 'Show'} action history
                  </span>
                ) : null}
              </SharedStyled.FlexRow>

              {!showHistory ? null : (
                <ActionHistoryCont>
                  {allActionData?.history?.map((action) => (
                    <div className="todo-container">
                      <SharedStyled.FlexRow margin="10px 0 0 0" justifyContent="space-between">
                        <SharedStyled.FlexRow>
                          <div
                            style={{
                              width: '32px',
                            }}
                          />

                          <div className="checkbox-item">
                            <div>
                              <SharedStyled.FlexCol gap="2px">
                                <SharedStyled.Text fontSize="14px" fontWeight="bold">
                                  <>{action?.body}</>
                                </SharedStyled.Text>

                                <div>
                                  <SharedStyled.Text fontSize="12px">
                                    <>{action?.type} on </>
                                  </SharedStyled.Text>
                                  {action?.due ? (
                                    <SharedStyled.Text fontSize="12px">
                                      <>
                                        {dayjsFormat(action?.due, 'M/D/YY')} @ {dayjsFormat(action?.due, 'h:mm a')}
                                      </>
                                    </SharedStyled.Text>
                                  ) : (
                                    ''
                                  )}
                                </div>

                                <div>
                                  <SharedStyled.Text color="grey" fontSize="12px">
                                    <>
                                      {salesPersonDrop?.find((person: any) => person._id === action.assignTo)?.name ||
                                        '--'}
                                    </>
                                    &emsp;
                                  </SharedStyled.Text>

                                  <Pill
                                    margin="0 0 0 10px"
                                    numVal={action?.oppId ? action?.num : undefined}
                                    path={
                                      action?.oppId
                                        ? `/${getEnumValue(action?.stageGroup!)}/opportunity/${action?.oppId}`
                                        : `/contact/profile/${action?.contactId}`
                                    }
                                    text={action?.oppId ? `${action?.PO}-${action?.num}` : 'Contact'}
                                  />
                                </div>
                              </SharedStyled.FlexCol>
                            </div>
                          </div>
                        </SharedStyled.FlexRow>
                      </SharedStyled.FlexRow>
                    </div>
                  ))}
                </ActionHistoryCont>
              )}
            </>
          </>
        )}
      </div>
      <CustomModal show={actionModal}>
        <ActionModal
          onClose={() => setActionModal(false)}
          onComplete={fetchActions}
          values={autoFillValues}
          setAutoFillValuesFromChild={setAutoFillValuesFromChild}
        />
      </CustomModal>
    </TodoNextCont>
  )
}

export default ToDoNextProfile
