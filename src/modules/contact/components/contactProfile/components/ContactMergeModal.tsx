import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import {
  dayjsFormat,
  formatPhoneNumber,
  getDigitsFromPhone,
  getOrdinal,
  notify,
} from '../../../../../shared/helpers/util'
import Button from '../../../../../shared/components/button/Button'
import { colors } from '../../../../../styles/theme'
import { Types } from '../../../constant'
import { FlexRow, Text } from '../../../../../styles/styled'

const ModalContainer = styled.div`
  background: ${colors.white};
  border-radius: 10px;
  padding: 30px;
  width: 95vw;
  max-width: 1200px;
`

const ModalTitle = styled.h2`
  margin: 0 0 30px 0;
  color: ${colors.darkGrey};
  text-align: center;
  font-size: 24px;
  font-weight: bold;
`
const TableWrapper = styled.div`
  overflow-x: auto;
  width: 100%;
`

const ComparisonTable = styled.table`
  width: max-content;
  border-collapse: collapse;
  margin-bottom: 30px;
  border: 1px solid ${colors.lightGrey3};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
`

const TableHeader = styled.th`
  padding: 12px 15px;
  text-align: center;
  background-color: ${colors.lightGrey2};
  color: ${colors.darkGrey};
  font-weight: 600;
  min-width: 150px;
  border: 1px solid ${colors.lightGrey3};
  border-bottom: 2px solid ${colors.lightGrey3};
`

const TableCell = styled.td`
  padding: 12px 15px;
  border: 1px solid ${colors.lightGrey3};
  text-align: left;
  vertical-align: middle;
  max-width: 280px;
  input[type='radio'] {
    margin-right: 8px;
    vertical-align: middle;
  }
`

const FieldLabel = styled.td`
  padding: 12px 15px;
  font-weight: 600;
  background-color: ${colors.lightGrey1};
  width: 80px;
  border: 1px solid ${colors.lightGrey3};
  vertical-align: middle;
`

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
`

interface ContactMatchModalProps {
  existingContacts: any[]
  isCancel: boolean
  newContactData: any
  onClose: () => void
  onMerge: (data: { mergedFields: Record<string, any>; toContact: string }) => void
  isNewContact?: boolean
}

const ContactMergeModal: React.FC<ContactMatchModalProps> = ({
  existingContacts = [],
  newContactData = {},
  isCancel,
  onClose,
  onMerge,
  isNewContact,
}) => {
  const [selectedFields, setSelectedFields] = useState<Record<string, any>>({})
  const [selectedFieldSources, setSelectedFieldSources] = useState<Record<string, string>>({})
  const [selectedContactIndex, setSelectedContactIndex] = useState<number>(-1)

  const isBusiness = newContactData?.isBusiness
  const allFields = [
    ...(isBusiness ? ['businessName'] : []),
    'firstName',
    'lastName',
    'phone',
    'email',
    'street',
    'city',
    'state',
    'zip',
    'nextAction',
    'type',
    'dateReceived',
  ]
  console.log({ existingContacts, newContactData })

  useEffect(() => {
    handleSelectContact(-1) // Default: prefill with user-entered data
  }, [newContactData])

  const handleFieldSelect = (field: string, value: any, sourceKey: string) => {
    if (field === 'nextAction') {
      setSelectedFields((prev) => ({ ...prev, nextAction: value }))
    } else {
      setSelectedFields((prev) => ({ ...prev, [field]: value }))
    }
    setSelectedFieldSources((prev) => ({ ...prev, [field]: sourceKey }))
  }

  const renderRadio = (field: string, value: any, sourceKey: string) => {
    console.log({ value })
    const isEmptyValue = () => {
      if (value === '--' || value === '' || value === undefined || value === null) return true

      if (typeof value === 'object' && value !== null) {
        return Object.values(value).every((v) => v === '' || v === undefined || v === null)
      }

      return false
    }

    if (isEmptyValue()) {
      return <span style={{ width: '20px', display: 'inline-block' }} />
    }

    return (
      <input
        type="radio"
        name={`radio-${field}`}
        checked={selectedFieldSources[field] === sourceKey}
        onChange={() => handleFieldSelect(field, value, sourceKey)}
      />
    )
  }

  const handleSelectContact = (index: number) => {
    setSelectedContactIndex(index)
    const contact = index === -1 ? newContactData : existingContacts[index]
    const prefix = index === -1 ? 'you-entered' : `existing-${index}`

    const fields: Record<string, any> = {}
    const sources: Record<string, string> = {}

    allFields.forEach((field) => {
      const value = contact[field]
      if (field === 'nextAction' && contact.nextAction) {
        fields.nextAction = contact.nextAction
        sources.nextAction = prefix
      } else if (value) {
        fields[field] = value
        sources[field] = prefix
      }
    })

    setSelectedFields(fields)
    setSelectedFieldSources(sources)
  }

  useEffect(() => {
    handleSelectContactWithFallback(-1) // Use fallback logic on first load
  }, [newContactData])

  const isEmptyValue = (value: any): boolean => {
    if (value === '' || value === undefined || value === null) return true

    if (typeof value === 'object' && value !== null) {
      return Object.values(value).every((v) => v === '' || v === undefined || v === null)
    }

    return false
  }

  const handleSelectContactWithFallback = (index: number) => {
    setSelectedContactIndex(index)
    const contact = index === -1 ? newContactData : existingContacts[index]
    const prefix = index === -1 ? 'you-entered' : `existing-${index}`

    const fields: Record<string, any> = {}
    const sources: Record<string, string> = {}

    allFields.forEach((field) => {
      let selectedValue = null
      let selectedSource = ''

      // First try the primary contact (index -1)
      const primaryContact = newContactData
      let primaryValue = primaryContact[field]

      // If primary has value, use it
      if (primaryValue && !isEmptyValue(primaryValue)) {
        selectedValue = primaryValue
        selectedSource = 'you-entered'
      } else {
        // Otherwise, find first existing contact that has this field
        for (let i = 0; i < existingContacts.length; i++) {
          const existingContact = existingContacts[i]
          let existingValue = existingContact[field]

          if (existingValue && !isEmptyValue(existingValue)) {
            selectedValue = existingValue
            selectedSource = `existing-${i}`
            break
          }
        }
      }

      // Set the selected value and source
      if (selectedValue) {
        fields[field] = selectedValue
        sources[field] = selectedSource
      }
    })

    setSelectedFields(fields)
    setSelectedFieldSources(sources)
  }

  const handleMergeClick = () => {
    const toContact = selectedContactIndex >= 0 ? existingContacts[selectedContactIndex]._id : undefined
    const { dateReceived, ...fieldsWithoutDate } = selectedFields
    const { street, city, state, zip } = fieldsWithoutDate
    let fullAddress = ''
    if (street && city && state && zip) {
      fullAddress = `${street}, ${city}, ${state}, ${zip}`
    }
    // console.log({
    //   mergedFields: {
    //     ...fieldsWithoutDate,
    //     ...(fullAddress ? { fullAddress } : {}),
    //     fullName: `${fieldsWithoutDate.firstName || ''} ${fieldsWithoutDate.lastName || ''}`.trim(),
    //     phone: getDigitsFromPhone(fieldsWithoutDate.phone),
    //     fromContact: existingContacts.map((contact) => contact._id).filter((id) => id && id !== ''),
    //   },
    //   toContact: newContactData._id,
    // })
    onMerge({
      mergedFields: {
        ...fieldsWithoutDate,
        ...(fullAddress ? { fullAddress } : {}),
        fullName: `${fieldsWithoutDate.firstName || ''} ${fieldsWithoutDate.lastName || ''}`.trim(),
        phone: getDigitsFromPhone(fieldsWithoutDate.phone),
        fromContact: existingContacts.map((contact) => contact._id).filter((id) => id && id !== ''),
      },
      toContact: newContactData._id,
    })
  }

  const isMatch = (field: string, contactValue: any, primaryValue: any): boolean => {
    if (typeof contactValue === 'object' && contactValue !== null) {
      return JSON.stringify(contactValue) === JSON.stringify(primaryValue)
    }
    return contactValue === primaryValue
  }

  const isMatchWithAny = (field: string, primaryValue: any): boolean => {
    return existingContacts.some((contact) => {
      const contactValue = contact[field]
      if (typeof contactValue === 'object' && contactValue !== null) {
        return JSON.stringify(contactValue) === JSON.stringify(primaryValue)
      }
      return contactValue === primaryValue
    })
  }

  return (
    <ModalContainer>
      <ModalTitle>Contact Match Found</ModalTitle>

      <TableWrapper>
        <ComparisonTable>
          <thead>
            <tr>
              <TableHeader>Field</TableHeader>
              <TableHeader>
                <input
                  type="radio"
                  name="rowSelect"
                  checked={selectedContactIndex === -1}
                  onChange={() => handleSelectContact(-1)}
                />
                {isNewContact ? 'Existing Contact' : 'Primary Contact'}
              </TableHeader>
              {existingContacts.map((contact, index) => (
                <TableHeader key={contact._id}>
                  <input
                    type="radio"
                    name="rowSelect"
                    checked={selectedContactIndex === index}
                    onChange={() => handleSelectContact(index)}
                  />
                  {isNewContact
                    ? contact._id
                      ? 'Existing Contact'
                      : 'You Entered'
                    : `${getOrdinal(index + 1)} Contact To Merge`}
                </TableHeader>
              ))}
            </tr>
          </thead>
          <tbody>
            {allFields.map((field) => (
              <tr key={field}>
                <FieldLabel>
                  {{
                    firstName: 'First Name',
                    lastName: 'Last Name',
                    businessName: 'Business Name',
                    phone: 'Phone',
                    email: 'Email',
                    street: 'Street',
                    city: 'City',
                    state: 'State',
                    zip: 'Zip',
                    nextAction: 'Action',
                    type: 'Type',
                    dateReceived: 'Date Received',
                  }[field] || field}
                </FieldLabel>
                <TableCell
                  style={{
                    color: isMatchWithAny(field, newContactData[field])
                      ? '#1b9e1b' // light green
                      : undefined,
                  }}
                >
                  {field !== 'dateReceived' && renderRadio(field, newContactData[field], 'you-entered')}
                  {field === 'phone'
                    ? formatPhoneNumber(newContactData[field], '')
                    : field === 'nextAction'
                    ? `${newContactData[field]?.body || ''} ${newContactData[field]?.body ? '-' : ''} ${dayjsFormat(
                        newContactData[field]?.createdAt,
                        'M/D/YY'
                      )}`
                    : field === 'dateReceived'
                    ? dayjsFormat(newContactData[field], 'M/D/YY h:mm A')
                    : field === 'type'
                    ? Object.entries(Types).find(([_, v]) => v === newContactData[field])?.[0] || ''
                    : newContactData[field]}
                </TableCell>
                {existingContacts.map((contact, idx) => (
                  <TableCell
                    key={contact._id}
                    style={{
                      color: isMatch(field, contact[field], newContactData[field])
                        ? '#1b9e1b' // light green background
                        : undefined,
                    }}
                  >
                    {field !== 'dateReceived' && renderRadio(field, contact[field], `existing-${idx}`)}
                    {field === 'phone'
                      ? formatPhoneNumber(contact[field], '')
                      : field === 'nextAction'
                      ? `${contact[field]?.body || ''} ${contact[field]?.body ? '-' : ''} ${dayjsFormat(
                          contact[field]?.createdAt,
                          'M/D/YY'
                        )}`
                      : field === 'dateReceived'
                      ? dayjsFormat(contact[field], 'M/D/YY h:mm A')
                      : field === 'type'
                      ? Object.entries(Types).find(([_, v]) => v === contact[field])?.[0] || ''
                      : contact[field]}
                  </TableCell>
                ))}
              </tr>
            ))}
          </tbody>
        </ComparisonTable>
      </TableWrapper>

      <ButtonContainer>
        <Button onClick={onClose} width="48%">
          {isNewContact ? (isCancel ? 'Cancel' : 'Create New Contact') : 'Cancel'}
        </Button>
        <Button onClick={handleMergeClick} className="yellow" width="48%">
          Merge With Existing
        </Button>
      </ButtonContainer>
    </ModalContainer>
  )
}

export default ContactMergeModal
