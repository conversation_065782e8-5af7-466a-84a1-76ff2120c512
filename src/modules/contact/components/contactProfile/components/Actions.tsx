import React, { useState, useEffect } from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import { Divider } from '../../../../subscription/components/currentPlan/style'
import AutoComplete from '../../../../../shared/autoComplete/AutoComplete'
import { removeField } from '../../../../companySettings/CompanySettings'
import { CrossIcon } from '../../../../../assets/icons/CrossIcon'

const Actions = ({ values, touched, errors, setFieldValue }: any) => {
  // State to track checkbox values
  const [dndAllChannels, setDndAllChannels] = useState(false)
  const [dndEmails, setDndEmails] = useState(false)
  const [dndTextMessages, setDndTextMessages] = useState(false)
  const [dndCalls, setDndCalls] = useState(false)
  const [dndGBP, setDndGBP] = useState(false)
  const [dndInbound, setDndInbound] = useState(false)
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  // Handler for "DND all channels" checkbox
  const handleDndAllChannelsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked
    setDndAllChannels(isChecked)

    // Update all other checkboxes to match
    setDndEmails(isChecked)
    setDndTextMessages(isChecked)
    setDndCalls(isChecked)
    setDndGBP(isChecked)
  }

  // Effect to update "DND all channels" when individual channels change
  useEffect(() => {
    // If all channels are checked, check "DND all channels"
    if (dndEmails && dndTextMessages && dndCalls && dndGBP) {
      setDndAllChannels(true)
    }
    // If any channel is unchecked, uncheck "DND all channels"
    else if (dndAllChannels) {
      setDndAllChannels(false)
    }
  }, [dndEmails, dndTextMessages, dndCalls, dndGBP])

  // Custom OptionRow component with checkbox state and handler
  const OptionRow = ({
    emoji,
    label,
    checked,
    onChange,
  }: {
    emoji: string
    label: string
    checked: boolean
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  }) => (
    <SharedStyled.FlexRow justifyContent="space-between" margin="0 0 5px 0">
      <SharedStyled.FlexRow gap="10px">
        <SharedStyled.Text>{emoji}</SharedStyled.Text>
        <SharedStyled.Text>{label}</SharedStyled.Text>
      </SharedStyled.FlexRow>
      <input type="checkbox" checked={checked} onChange={onChange} />
    </SharedStyled.FlexRow>
  )

  return (
    <SharedStyled.FlexCol>
      <SharedStyled.FlexCol gap="20px" margin="12px 0 10px 0">
        <SharedStyled.FlexCol gap="6px">
          <SharedStyled.Text>Add your tags?</SharedStyled.Text>
          <SharedStyled.FlexBox width="100%" gap="10px" alignItems="center">
            <AutoComplete
              value={values?.selectedTag}
              labelName="Tags"
              stateName="selectedTag"
              options={['Automated Follow Up', 'Automated NA']}
              // setValueOnClick={(val: string) => {
              //   setTask(val)
              // }}
              dropdownHeight="300px"
              borderRadius="0px"
              // selectedValue={task}
              validate
              setFieldValue={setFieldValue}
            />

            <SharedStyled.Button
              maxWidth="50px"
              marginTop="7px"
              mediaHeight="52px"
              type="button"
              disabled={selectedTags?.includes(values.selectedTag)}
              onClick={() => {
                let val = values.selectedTag
                setSelectedTags((prev) => [...prev, val])
                setFieldValue('selectedTag', '')
              }}
            >
              <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
            </SharedStyled.Button>
          </SharedStyled.FlexBox>
        </SharedStyled.FlexCol>
        <SharedStyled.FlexCol>
          <SharedStyled.Text fontWeight="500" fontSize="14px" width="100%" textAlign="flex-start">
            {selectedTags.length > 0 && 'Selected tags :'}
          </SharedStyled.Text>
          <SharedStyled.FlexRow width="fit-content">
            {selectedTags.map((field, idx) => (
              <div
                key={idx}
                className="tag-option"
                onClick={() => removeField((values: string[]) => setSelectedTags(values), idx, selectedTags)}
              >
                {field} <CrossIcon />
              </div>
            ))}
          </SharedStyled.FlexRow>
        </SharedStyled.FlexCol>
      </SharedStyled.FlexCol>

      <SharedStyled.FlexRow gap="8px" margin="10px 0 10px 0">
        <SharedStyled.Text fontSize="20px">💤</SharedStyled.Text>
        <SharedStyled.Text fontSize="20px" familyTypeMedium>
          DND
        </SharedStyled.Text>
      </SharedStyled.FlexRow>

      <SharedStyled.Text fontSize="12px" color="#5b8def" margin="0 0 16px 0">
        If any of these are 'true' then texts/emails, etc won't be sent to them.
      </SharedStyled.Text>

      <SharedStyled.FlexRow justifyContent="space-between" padding="10px 0">
        <SharedStyled.Text fontSize="14px" familyTypeMedium>
          DND all channels
        </SharedStyled.Text>
        <input type="checkbox" checked={dndAllChannels} onChange={handleDndAllChannelsChange} />
      </SharedStyled.FlexRow>

      <SharedStyled.FlexBox width="100%" alignItems="center">
        {' '}
        <Divider />
        OR
        <Divider />
      </SharedStyled.FlexBox>

      <OptionRow emoji="✉️" label="Emails" checked={dndEmails} onChange={(e) => setDndEmails(e.target.checked)} />
      <OptionRow
        emoji="💬"
        label="Text Messages"
        checked={dndTextMessages}
        onChange={(e) => setDndTextMessages(e.target.checked)}
      />
      <OptionRow
        emoji="📞"
        label="Calls & Voicemails"
        checked={dndCalls}
        onChange={(e) => setDndCalls(e.target.checked)}
      />
      <OptionRow emoji="💷" label="GBP" checked={dndGBP} onChange={(e) => setDndGBP(e.target.checked)} />

      <Divider />

      <SharedStyled.FlexRow justifyContent="space-between" margin="10px 0 0 0">
        <SharedStyled.FlexRow gap="5px">
          <SharedStyled.Text fontSize="14px" familyTypeMedium>
            DND Inbound Calls and SMS
          </SharedStyled.Text>

          <SharedStyled.TooltipContainer
            width="250px"
            positionLeft="4px"
            positionBottom="0px"
            positionLeftDecs="0px"
            positionBottomDecs="20px"
          >
            <span className="tooltip-content">
              Inbound Calls from this number will be directly blocked whereas Inbound SMS will be blocked at system
              level so charges will incur
            </span>
            <SharedStyled.IButton>i</SharedStyled.IButton>
          </SharedStyled.TooltipContainer>
        </SharedStyled.FlexRow>

        <input type="checkbox" checked={dndInbound} onChange={(e) => setDndInbound(e.target.checked)} />
      </SharedStyled.FlexRow>
    </SharedStyled.FlexCol>
  )
}

export default Actions
