import React from 'react'
import styled from 'styled-components'
import { FlexCol } from '../../../../../styles/styled'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import { getValueByKeyAndMatch } from '../../../../../shared/helpers/util'

const TeamMembersContainer = styled(FlexCol)`
  margin-top: 20px;
  width: 100%;
`

const TeamMembersHeader = styled.div`
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 15px;

  svg {
    margin-left: 5px;
    cursor: pointer;
  }
`

interface TeamMembersProps {
  values: any
  errors: any
  touched: any
  setFieldValue: (field: string, value: any) => void
  salesPersonDrop: any
  officeDrop: any
  projectManagerDrop: any
  handleInputBlurValue: (data: any, activityString: string, resetCallback?: () => void) => void
  contactDetails: any
}

const TeamMembers: React.FC<TeamMembersProps> = ({
  values,
  errors,
  salesPersonDrop,
  officeDrop,
  projectManagerDrop,
  touched,
  setFieldValue,
  handleInputBlurValue,
  contactDetails,
}) => {
  const hasValueChanged = (initialValue: string | number, finalValue: string | number) => {
    return initialValue !== finalValue
  }

  return (
    <TeamMembersContainer>
      <CustomSelect
        labelName="Appointment Setter"
        stateName="appointmentSetter"
        error={touched.appointmentSetter && errors.appointmentSetter ? true : false}
        setFieldValue={setFieldValue}
        setValue={() => {}}
        value={values.appointmentSetter || ''}
        dropDownData={officeDrop?.map((item: any) => item.name) || []}
        innerHeight="52px"
        margin="10px 0"
        onBlur={() => {
          const initialValue = getValueByKeyAndMatch('name', contactDetails?.csrId, '_id', officeDrop)
          if (hasValueChanged(initialValue, values.appointmentSetter)) {
            handleInputBlurValue(
              {
                csrId: getValueByKeyAndMatch('_id', values.appointmentSetter, 'name', officeDrop),
              },
              `changed Appointment Setter from ${initialValue || 'None'} to ${values.appointmentSetter}`,
              () => {}
            )
          }
        }}
      />

      <CustomSelect
        labelName="Sales Rep"
        stateName="salesRep"
        error={touched.salesRep && errors.salesRep ? true : false}
        setFieldValue={setFieldValue}
        setValue={() => {}}
        value={values.salesRep || ''}
        dropDownData={salesPersonDrop?.map((item: any) => item.name) || []}
        innerHeight="52px"
        margin="10px 0"
        onBlur={() => {
          const initialValue = getValueByKeyAndMatch('name', contactDetails?.salesPersonId, '_id', salesPersonDrop)
          if (hasValueChanged(initialValue, values.salesRep)) {
            handleInputBlurValue(
              {
                salesPersonId: getValueByKeyAndMatch('_id', values.salesRep, 'name', salesPersonDrop),
              },
              `changed Sales Rep from ${initialValue || 'None'} to ${values.salesRep}`,
              () => {}
            )
          }
        }}
      />

      <CustomSelect
        labelName="Project Manager"
        stateName="projectManager"
        error={touched.projectManager && errors.projectManager ? true : false}
        setFieldValue={setFieldValue}
        setValue={() => {}}
        value={values.projectManager || ''}
        dropDownData={projectManagerDrop?.map((item: any) => item.name) || []}
        innerHeight="52px"
        margin="10px 0"
        onBlur={() => {
          const initialValue = getValueByKeyAndMatch(
            'name',
            contactDetails?.projectManagerId,
            '_id',
            projectManagerDrop
          )

          if (hasValueChanged(initialValue, values.projectManager)) {
            handleInputBlurValue(
              {
                projectManagerId: getValueByKeyAndMatch('_id', values.projectManager, 'name', projectManagerDrop),
              },
              `changed Project Manager from ${initialValue || 'None'} to ${values.projectManager}`,
              () => {}
            )
          }
        }}
      />
    </TeamMembersContainer>
  )
}

export default TeamMembers
