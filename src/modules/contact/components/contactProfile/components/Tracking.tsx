import React, { useState } from 'react'
import styled from 'styled-components'
import { dayjsFormat } from '../../../../../shared/helpers/util'

interface TrackingEntry {
  [key: string]: any
  createdAt: string
}

interface Props {
  tracking: TrackingEntry[]
}

const Container = styled.div`
  margin-right: auto;
`

const Section = styled.div`
  margin: 30px 0;
`

const SubHeading = styled.h3`
  margin: 10px 0 5px 10px;
  font-weight: 700;
  cursor: pointer;
`

const EntryWrapper = styled.div<{ indent?: boolean }>`
  display: flex;
  flex-direction: column;
  padding-left: ${(props) => (props.indent ? '20px' : '0')};
  gap: 4px;
`

const EntryItem = styled.span`
  font-size: 16px;
  color: #333;

  strong {
    margin-right: 6px;
    color: #111;
  }
`

const Tracking: React.FC<Props> = ({ tracking }) => {
  const [toggle, setToggle] = useState<{ [key: number]: boolean }>({})
  const toggleCount = (index: number) => {
    setToggle((prevState) => ({
      ...prevState,
      [index]: !prevState[index],
    }))
  }
  if (!tracking || tracking.length === 0) return <div>No tracking data available.</div>
  const [firstEntry, ...otherEntries] = tracking
  const renderKeyValueList = (entry: TrackingEntry, indent: boolean = false) => {
    const filtered = Object.entries(entry).filter(([key, value]) => value !== null && value !== '')

    const sorted = [
      ['createdAt', dayjsFormat(entry.createdAt, 'M/D/YY h:mm A')],
      ...filtered.filter(([key]) => key !== 'createdAt'),
    ]

    return (
      <EntryWrapper indent={indent}>
        {sorted.map(([key, value]) => (
          <EntryItem key={key}>
            <strong>{key}:</strong> {value}
          </EntryItem>
        ))}
      </EntryWrapper>
    )
  }

  return (
    <Container>
      <Section>{renderKeyValueList(firstEntry)}</Section>

      {otherEntries.map((entry, index) => (
        <Section key={index}>
          <SubHeading onClick={() => toggleCount(index)}>
            {!toggle[index] ? <>&#9654;</> : <>&#9660;</>}
            &nbsp;
            {dayjsFormat(entry.createdAt, 'M/D/YY h:mm A')}
          </SubHeading>
          {toggle[index] && renderKeyValueList(entry, true)}
        </Section>
      ))}
    </Container>
  )
}

export default Tracking
