import { Link, useLocation } from 'react-router-dom'
import { Text } from '../../../../../styles/styled'
import { commentColorType } from '../../../constant'

const Pill = ({ numVal, text, margin, path }: { numVal?: string; text?: string; margin?: string; path: string }) => {
  return (
    <Link
      to={path}
      target="_blank"
      style={{
        pointerEvents: !path ? 'none' : 'auto',
      }}
    >
      <Text
        padding="3px 6px"
        style={{
          textTransform: 'uppercase',
        }}
        backgroundColor={
          numVal
            ? Number(commentColorType[numVal]) > 10
              ? commentColorType['10']
              : commentColorType[numVal?.replace(/\D/g, '')]
            : '#bcbcbc'
        }
        color="white"
        fontSize="10px"
        margin={margin}
        fontWeight="bold"
        borderRadius="5px"
      >
        {text}
      </Text>
    </Link>
  )
}

export default Pill
