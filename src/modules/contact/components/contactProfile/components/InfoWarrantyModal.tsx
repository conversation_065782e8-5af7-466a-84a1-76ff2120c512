import React from 'react'

import * as SharedStyled from '../../../../../styles/styled'
import Button from '../../../../../shared/components/button/Button'

interface I_ChangeLeadTypeModal {
  onClose: () => void
  onConfirm: () => void
  leadSource: string
  campaign: string
  daysAgo: string | number
  currentType: string
  newType: string
}

const InfoWarrantyModal: React.FC<I_ChangeLeadTypeModal> = ({
  onClose,
  onConfirm,
  leadSource,
  campaign,
  daysAgo,
  currentType,
  newType,
}) => {
  return (
    <div style={{ width: '450px' }}>
      <SharedStyled.SettingModalContentContainer>
        <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
          <SharedStyled.FlexCol gap="16px">
            <SharedStyled.Text width="100%" fontSize="20px" textAlign="center" fontWeight="700">
              This contact was imported as a "{currentType}" {daysAgo} day(s) ago from
            </SharedStyled.Text>

            <SharedStyled.Text width="100%" fontSize="16px" textAlign="center" fontWeight="700">
              {leadSource} {campaign ? `: ${campaign}` : ''}
            </SharedStyled.Text>

            <SharedStyled.Text width="100%" fontSize=" 20px" textAlign="center" fontWeight="700">
              Change this "{currentType}" to "{newType}"?
            </SharedStyled.Text>

            <SharedStyled.FlexRow justifyContent="space-between">
              <Button type="button" onClick={onClose} className="gray">
                No
              </Button>
              <Button type="button" onClick={onConfirm}>
                Yes
              </Button>
            </SharedStyled.FlexRow>
          </SharedStyled.FlexCol>
        </SharedStyled.Content>
      </SharedStyled.SettingModalContentContainer>
    </div>
  )
}

export default InfoWarrantyModal
