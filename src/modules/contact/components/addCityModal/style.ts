import styled from 'styled-components'
import { colors } from '../../../../styles/theme'

export const AddCityModalContainer = styled.div`
  background: ${colors.white};
  width: 500px;
  height: 100%;
  border-radius: 10px;
  @media (max-width: 768px) {
    width: 314px;
  }
`
export const ModalHeaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  border-bottom: 1px solid ${colors.lightGrey3};
  padding: 20px;
`

export const ModalHeader = styled.h5`
  margin: 0;
  line-height: 1.5;
  font-size: 20px;
  font-weight: 600;
  color: ${colors.darkGrey};
`

export const CrossContainer = styled.div`
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
    svg path {
      stroke: ${colors.darkGrey};
    }
    :hover {
      svg path {
        stroke: ${colors.grey};
      }
      transform: scale(1.03);
    }
    transition: all 0.01s linear;
  }
`
