import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { createCity } from '../../../../logic/apis/city'
import { getDataFromLocalStorage, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_AddCityModal {
  setShowAddCityModal: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  action: string
}

export const AddCityModal = (props: I_AddCityModal) => {
  const { setShowAddCityModal, action, setDetailsUpdate } = props

  /**
   * InitialValues is an interface declared here so that it can be used as a type for the useState hook
   */
  interface InitialValues {
    city: string
    state: string
  }

  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    city: '',
    state: '',
  })

  const [loading, setLoading] = useState<boolean>(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])
  /**
   * AddCityModalSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const AddCityModalSchema = Yup.object().shape({
    city: Yup.string().required('Required'),
    state: Yup.string().required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)
      let dataObj = {
        city: submittedValues.city,
        state: submittedValues.state,
        createdBy: currentMember._id,
      }

      let response = await createCity(dataObj)
      if (response?.data?.statusCode === 201) {
        notify('City Added Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowAddCityModal(false)
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
      // }
    } catch (error) {
      setLoading(false)
      console.error('City Adding error', error)
    }
  }

  return (
    <Styled.AddCityModalContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={AddCityModalSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <Styled.ModalHeader>{action}</Styled.ModalHeader>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setShowAddCityModal(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer padding="0px 20px 20px 20px">
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="City"
                      stateName="city"
                      error={touched.city && errors.city ? true : false}
                      passRef={inputRef}
                    />
                    <InputWithValidation
                      labelName="State"
                      stateName="state"
                      error={touched.state && errors.state ? true : false}
                    />
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <SharedStyled.Button type="submit">
                        {loading ? (
                          <>
                            Adding City..
                            <SharedStyled.Loader />
                          </>
                        ) : (
                          'Add'
                        )}
                      </SharedStyled.Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.AddCityModalContainer>
  )
}
