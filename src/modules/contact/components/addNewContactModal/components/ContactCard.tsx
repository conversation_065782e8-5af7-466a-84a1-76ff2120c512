import React from 'react'
import styled from 'styled-components'
import { screenSizes } from '../../../../../styles/theme'
import { SLoader } from '../../../../../shared/components/loader/Loader'
import { formatPhoneNumber } from '../../../../../shared/helpers/util'

const Container = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 0.25fr;
  gap: 1rem;
  padding: 1rem;
  max-width: 900px;
  font-size: 0.5rem;

  @media (min-width: ${screenSizes.M}px) {
    font-size: 0.75rem;
  }

  @media (min-width: ${screenSizes.XL}px) {
    font-size: 0.95rem;
  }
`

const Section = styled.div`
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
`

const Row = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
`

const Name = styled.div`
  font-weight: 600;

  font-size: 0.75rem;
  @media (min-width: ${screenSizes.M}px) {
    font-size: 0.95rem;
  }

  @media (min-width: ${screenSizes.XL}px) {
    font-size: 1rem;
  }
`

const Notes = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
`

const TextBlock = styled.div`
  display: flex;
  flex-direction: column;
`

const IconWrapper = styled.div`
  margin-top: 2px;
`
const CloseButton = styled.button`
  background: transparent;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;

  &:hover {
    color: #e00;
  }
`
interface ContactInfo {
  fullName: string
  street: string
  city: string
  state: string
  zip: string
  phone: string
  email: string
  notes: string
}

interface ContactCardProps {
  contact: { id: ContactInfo; relationship: string }
  onRemove: () => void
  key: number
  isLoading: boolean
  onClick: () => void
}
const ContactCard: React.FC<ContactCardProps> = ({ contact, isLoading, onClick, key, onRemove }) => {
  return (
    <Container key={key} onClick={onClick}>
      {isLoading ? (
        <>
          <SLoader height={65} width={100} isPercent />
          <SLoader height={65} width={100} isPercent />
          <SLoader height={65} width={100} isPercent />
          <SLoader height={65} width={100} isPercent />
        </>
      ) : (
        <>
          {/* Left section */}
          <Section>
            <Name>{contact?.id?.fullName || ''}</Name>
            <Row>
              {contact?.id?.street ? (
                <>
                  <IconWrapper>📍</IconWrapper>
                  <TextBlock>
                    <span>{contact?.id?.street || ''}</span>
                    <span>
                      {contact?.id?.city || ''} {contact?.id?.state || ''} {contact?.id?.zip || ''}
                    </span>
                  </TextBlock>
                </>
              ) : null}
            </Row>
          </Section>

          {/* Middle section */}
          <Section>
            <Row>
              {contact?.id?.phone ? (
                <>
                  <IconWrapper>📞</IconWrapper>
                  <span>{formatPhoneNumber(contact?.id?.phone, '') || ''}</span>
                </>
              ) : null}
            </Row>
            <Row>
              {contact?.relationship ? (
                <>
                  <IconWrapper>🔗</IconWrapper>
                  <span>{contact?.relationship || ''}</span>
                </>
              ) : null}
            </Row>

            <Row>
              {contact?.id?.notes ? (
                <>
                  <IconWrapper>📝</IconWrapper>
                  <Notes>{contact?.id?.notes || ''}</Notes>
                </>
              ) : null}
            </Row>
          </Section>

          {/* Right section */}
          <Section>
            <Row>
              {contact?.id?.email ? (
                <>
                  <IconWrapper>📧</IconWrapper>
                  <span>{contact?.id?.email || ''}</span>
                </>
              ) : null}
            </Row>
          </Section>

          <Section>
            <CloseButton
              onClick={(e) => {
                e.stopPropagation()
                onRemove()
              }}
              title="Remove Contact"
            >
              ×
            </CloseButton>
          </Section>
        </>
      )}
    </Container>
  )
}

export default ContactCard
