import React from 'react'
import styled from 'styled-components'
import { screenSizes } from '../../../../../styles/theme'
import { formatPhoneNumber } from '../../../../../shared/helpers/util'

const Container = styled.div`
  width: 100%;
  display: grid;
  background-color: #c2ffc2;
  grid-template-columns: 1fr 1fr 1fr 0.5fr;
  gap: 1rem;
  padding: 1rem;
  max-width: 900px;

  font-size: 0.75rem;

  /* @media (min-width: ${screenSizes.XL}px) {
    font-size: 1rem;
  } */
`

const Section = styled.div`
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
`

const Row = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
`

const Name = styled.div`
  font-weight: 600;
  font-size: 1rem;
`

const Notes = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
`

const TextBlock = styled.div`
  display: flex;
  flex-direction: column;
`

const IconWrapper = styled.div`
  margin-top: 2px;
`
const CloseButton = styled.button`
  background: transparent;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;

  &:hover {
    color: #e00;
  }
`
interface ContactInfo {
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zip: string
  phone: string
  email: string
  relationship: string
  notes: string
}

interface ContactCardProps {
  contact: ContactInfo
  onRemove: () => void
  key: number
  onClick: () => void
}
const ContactCardToBeAdded: React.FC<ContactCardProps> = ({ contact, onClick, key, onRemove }) => {
  return (
    <Container key={key} onClick={onClick}>
      {/* Left section */}
      <Section>
        <Name>
          {contact?.firstName || '--'} {contact?.lastName || '--'}
        </Name>
        <Row>
          <IconWrapper>📍</IconWrapper>
          <TextBlock>
            <span>{contact?.street || '--'}</span>
            <span>
              {contact?.city || '--'}, {contact?.state || '--'} {contact?.zip || '--'}
            </span>
          </TextBlock>
        </Row>
      </Section>

      {/* Middle section */}
      <Section>
        <Row>
          <IconWrapper>📞</IconWrapper>
          <span>{formatPhoneNumber(contact?.phone, '') || '--'}</span>
        </Row>
        <Row>
          <IconWrapper>🔗</IconWrapper>
          <span>{contact?.relationship || '--'}</span>
        </Row>
        <Row>
          <IconWrapper>📝</IconWrapper>
          <Notes>{contact?.notes || '--'}</Notes>
        </Row>
      </Section>

      {/* Right section */}
      <Section>
        <Row>
          <IconWrapper>📧</IconWrapper>
          <span>{contact?.email || '--'}</span>
        </Row>
      </Section>

      <Section>
        <CloseButton
          onClick={(e) => {
            e.stopPropagation()
            onRemove()
          }}
          title="Remove Contact"
        >
          ×
        </CloseButton>
      </Section>
    </Container>
  )
}

export default ContactCardToBeAdded
