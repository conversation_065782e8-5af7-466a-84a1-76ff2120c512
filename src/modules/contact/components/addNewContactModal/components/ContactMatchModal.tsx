import React from 'react'
import styled from 'styled-components'
import { formatPhoneNumber, generateUUID, isSuccess, notify } from '../../../../../shared/helpers/util'
import Button from '../../../../../shared/components/button/Button'
import { createContactComment } from '../../../../../logic/apis/contact'
import { colors } from '../../../../../styles/theme'
import { useSelector } from 'react-redux'

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`

const ModalContent = styled.div`
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
`

const ModalTitle = styled.h2`
  margin: 0 0 20px 0;
  color: #333;
  text-align: center;
  font-size: 24px;
  font-weight: bold;
`

const ComparisonContainer = styled.div`
  display: flex;
  margin-bottom: 30px;
`

const Column = styled.div`
  flex: 1;
  padding: 0 15px;
`

const ColumnHeader = styled.h3`
  margin: 0 0 15px 0;
  color: #555;
  font-size: 18px;
`

const ContactInfo = styled.div`
  margin-bottom: 10px;
`

const ContactName = styled.div`
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
`

const ContactDetail = styled.div<{ isMatch?: boolean }>`
  font-size: 16px;
  margin-bottom: 5px;
  color: ${(props) => (props.isMatch ? colors.green : 'inherit')};
  font-weight: ${(props) => (props.isMatch ? 'bold' : 'normal')};
`

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 20px;
`

interface ContactMatchModalProps {
  existingContact: any
  newContactData: any
  onClose: () => void
  onUseExisting: () => void
  setShowAddNewClientModal: React.Dispatch<React.SetStateAction<boolean>>
}

const ContactMatchModal: React.FC<ContactMatchModalProps> = ({
  existingContact,
  newContactData,
  onClose,
  onUseExisting,
  setShowAddNewClientModal,
}) => {
  const [isLoading, setIsLoading] = React.useState(false)
  const globalSelector = useSelector((state: any) => state)
  const { currentMember } = globalSelector.company
  const isMatch = (field: string) => {
    if (!existingContact || !newContactData) return false

    // Special case for phone numbers - normalize before comparing
    if (field === 'phone') {
      const existingPhone = existingContact.phone?.replace(/\D/g, '')
      const newPhone = newContactData.phone?.replace(/\D/g, '')
      return existingPhone && newPhone && existingPhone === newPhone
    }

    // Special case for email - case insensitive comparison
    if (field === 'email') {
      return (
        existingContact.email &&
        newContactData.email &&
        existingContact.email.toLowerCase() === newContactData.email.toLowerCase()
      )
    }

    // Default comparison
    return (
      existingContact[field] &&
      newContactData[field] &&
      existingContact[field].toString() === newContactData[field].toString()
    )
  }

  const handleUseExisting = async () => {
    setIsLoading(true)
    try {
      // Create a comment with the entered data
      const commentText = `Attempted to create duplicate contact with:
      ${newContactData.firstName ? `First Name: ${newContactData.firstName}` : ''}
      ${newContactData.lastName ? `Last Name: ${newContactData.lastName}` : ''}
      ${newContactData.phone ? `Phone: ${formatPhoneNumber(newContactData.phone, '')}` : ''}
      ${newContactData.email ? `Email: ${newContactData.email}` : ''}
      ${
        newContactData.street
          ? `Address: ${newContactData.street}, ${newContactData.city || ''} ${newContactData.state || ''} ${
              newContactData.zip || ''
            }`
          : ''
      }
      ${newContactData.notes ? `Notes: ${newContactData.notes}` : ''}
      `

      const response = await createContactComment(
        {
          body: commentText.trim(),
          currDate: new Date().toISOString(),
          memberId: currentMember._id!,
          id: generateUUID()!,
        },
        existingContact._id!
      )

      if (isSuccess(response)) {
        notify('Using existing contact. Comment added with entered information.', 'success')
        onUseExisting()
        setShowAddNewClientModal(false)
      } else {
        notify(response?.data?.message || 'Failed to add comment', 'error')
      }
    } catch (error) {
      console.error('Error adding comment:', error)
      notify('Failed to add comment', 'error')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalTitle>Contact Match Found!</ModalTitle>

        <ComparisonContainer>
          <Column>
            <ColumnHeader>You entered:</ColumnHeader>
            <ContactInfo>
              <ContactName>
                {newContactData.firstName} {newContactData.lastName}
              </ContactName>
              <ContactDetail isMatch={isMatch('phone')}>{formatPhoneNumber(newContactData.phone, '')}</ContactDetail>
              {newContactData.email && <ContactDetail isMatch={isMatch('email')}>{newContactData.email}</ContactDetail>}
            </ContactInfo>
          </Column>

          <Column>
            <ColumnHeader>Existing Contact:</ColumnHeader>
            <ContactInfo>
              <ContactName>
                {existingContact.firstName} {existingContact.lastName}
              </ContactName>
              <ContactDetail>{formatPhoneNumber(existingContact.phone, '')}</ContactDetail>
              {existingContact.email && <ContactDetail>{existingContact.email}</ContactDetail>}
            </ContactInfo>
          </Column>
        </ComparisonContainer>

        <ButtonContainer>
          <Button className="gray" onClick={onClose} disabled={isLoading} width="48%">
            Create New Contact
          </Button>
          <Button onClick={handleUseExisting} isLoading={isLoading} disabled={isLoading} width="48%">
            Use Existing Contact
          </Button>
        </ButtonContainer>
      </ModalContent>
    </ModalOverlay>
  )
}

export default ContactMatchModal
