import { Field } from 'formik'
import styled from 'styled-components'
import { colors, screenSizes } from '../../../../styles/theme'
import { SettingModalHeaderContainer } from '../../../units/components/newUnitModal/style'
import { Nue } from '../../../../shared/helpers/constants'

export const AddNewClientModalContainer = styled.div`
  background: ${colors.white};
  width: 700px;
  height: 100%;
  border-radius: 10px;
  @media (max-width: 768px) {
    width: 90vw;
  }
  .link {
    cursor: pointer;
    color: ${colors.darkBlue};
  }
`
export const ClientSelectWrapper = styled.div`
  padding: 12px;
  @media (min-width: ${screenSizes.S}px) {
    padding: 24px;
  }
`
export const ModalHeaderContainer = styled(SettingModalHeaderContainer)`
  padding: 12px 12px 0 12px;

  @media (min-width: ${screenSizes.S}px) {
    padding: 32px 32px 0 32px;
  }
`

export const ModalHeader = styled.p`
  margin: 0;
  line-height: 1.5;
  font-size: 20px;
  font-family: ${Nue.medium};
  color: ${colors.darkGrey};
`

export const CrossContainer = styled.div`
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
    svg path {
      stroke: ${colors.darkGrey};
    }
    :hover {
      svg path {
        stroke: ${colors.grey};
      }
      transform: scale(1.03);
    }
    transition: all 0.01s linear;
  }
`

export const LabelDiv = styled.label<any>`
  font-size: 14px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

export const TextArea = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.marginTop};
  cursor: pointer;
  resize: vertical;
  outline: none;
  border: 1px solid ${colors.lightGray};
  border-radius: 8px;
  padding: 12px 18px;
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
  }
  margin-top: ${(props) => props.marginTop};
`
export const GoogleSearchBox = styled.div`
  width: 100%;
  .label-float {
  }
`
export const ContactBox = styled.div`
  display: flex;
  justify-content: end;
  width: 100%;
  padding-bottom: 10px;
  border-bottom: 1px solid #6a747e33;
  .contact-content {
    width: 100%;
  }

  button {
    padding: 18px;
    background-color: ${colors.errorRed};
  }
`
