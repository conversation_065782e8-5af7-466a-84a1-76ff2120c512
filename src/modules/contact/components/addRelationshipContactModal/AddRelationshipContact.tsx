import React, { useState } from 'react'
import * as SharedStyled from '../../../../styles/styled'
import { Formik, Form } from 'formik'
import * as Yup from 'yup'
import Button from '../../../../shared/components/button/Button'
import { SLoader } from '../../../../shared/components/loader/Loader'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { GoogleSearchBox } from '../addNewContactModal/style'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'
import { addLinkedContact, createContact, updateContact } from '../../../../logic/apis/contact'
import { getDigitsFromPhone, isSuccess, notify } from '../../../../shared/helpers/util'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import { AddressWrap } from '../../style'

export const relationshipOptions = [
  'Spouse',
  'Partner',
  'Parent',
  'Child',
  'Family Member',
  'Employee',
  'Employer',
  'Other',
]

interface AddRelationshipContactProps {
  onClose: () => void
  companySettingForAll: any
  contacts: any
  setFieldValue: (field: string, value: any) => void
  index: number
  relationshipContact: any
  contactId?: string
  fetchLinkedContact?: () => void
}

const validationSchema = Yup.object({
  firstName: Yup.string().required('Name is required'),
  lastName: Yup.string(),
  fullAddress: Yup.string(),
  cityStateZip: Yup.string(),
  phone: Yup.string(),
  email: Yup.string().email('Invalid email'),
  relationship: Yup.string().required('Name is required'),
  notes: Yup.string(),
})
export const AddRelationshipContact: React.FC<AddRelationshipContactProps> = ({
  onClose,
  companySettingForAll,
  contacts,
  relationshipContact,
  setFieldValue,
  index,
  contactId,
  fetchLinkedContact,
}) => {
  const [loading, setLoading] = useState<boolean>(false)
  const [showEditAddress, setShowEditAddress] = useState(false)

  const initialValues = {
    firstName: Object.keys(relationshipContact)?.length ? relationshipContact?.firstName || '' : '',
    lastName: Object.keys(relationshipContact)?.length ? relationshipContact?.lastName || '' : '',
    city: Object.keys(relationshipContact)?.length ? relationshipContact?.city || '' : '',
    street: Object.keys(relationshipContact)?.length ? relationshipContact?.street || '' : '',
    state: Object.keys(relationshipContact)?.length ? relationshipContact?.state || '' : '',
    zip: Object.keys(relationshipContact)?.length ? relationshipContact?.zip || '' : '',
    phone: Object.keys(relationshipContact)?.length ? relationshipContact?.phone || '' : '',
    email: Object.keys(relationshipContact)?.length ? relationshipContact?.email || '' : '',
    relationship: '',
    fullAddress: `${relationshipContact?.street || ''}, ${relationshipContact?.city || ''}, ${
      relationshipContact?.state || ''
    } ${relationshipContact?.zip || ''}`,
    notes: Object.keys(relationshipContact)?.length ? relationshipContact?.notes || '' : '',
    id: Object.keys(relationshipContact)?.length ? relationshipContact?._id || undefined : undefined,
  }
  console.log({ contacts })
  console.log({ initialValues })

  const handleSubmit = async (values: any) => {
    setLoading(true)
    const { relationship, id, ...rest } = values
    const dataObj = {
      ...rest,
      dateReceived: new Date(),
      fullName: `${values?.firstName?.trim()} ${values?.lastName?.trim() || ''}`?.trim(),
      type: 'linked',
      firstName: values?.firstName?.trim(),
      lastName: values?.lastName?.trim() || '',
      phone: getDigitsFromPhone(values.phone),
      fullAddress: `${values?.street}, ${values?.city}, ${values?.state} ${values?.zip}`.trim(),
    }

    try {
      if (index >= 0) {
        // Edit existing contact
        // let response = await updateContact(dataObj, values.id)
        // if (isSuccess(response)) {
        // const id = response?.data?.data?.id || ''
        const updated = [...contacts]
        updated[index] = { ...values, id }
        setFieldValue('contacts', updated)
        console.log({ responsegfddg: updated })
        if (contactId) {
          const addLinkedObj = [
            {
              relationship,
              id,
            },
          ]
          const res = await addLinkedContact(addLinkedObj, contactId!)
          if (isSuccess(res)) {
            fetchLinkedContact!()
            notify('Linked Contact Added Successfully', 'success')
          }
        }
        // }
      } else {
        // Add new contact
        let response = await createContact(dataObj)
        if (isSuccess(response)) {
          const id = response?.data?.data?.id || ''
          setFieldValue('contacts', [...contacts, { ...values, id }])
          console.log({ responsegfddg: { ...values, id } })
          if (contactId) {
            const addLinkedObj = [
              {
                relationship,
                id,
              },
            ]
            const res = await addLinkedContact(addLinkedObj, contactId!)
            if (isSuccess(res)) {
              fetchLinkedContact!()
              notify('Linked Contact Added Successfully', 'success')
            }
          }
        }
      }
      onClose()
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      {/* <SharedStyled.ModalHeaderContainer>
        <SharedStyled.FlexRow>
          <img src={UnitSvg} alt="icon" />
          <SharedStyled.FlexCol>
            <h3>Add Relationship Contact</h3>
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>

        <SharedStyled.CrossContainer onClick={onClose}>
          <CrossIcon />
        </SharedStyled.CrossContainer>
      </SharedStyled.ModalHeaderContainer> */}

      <SharedStyled.FlexBox width="100%" flexDirection="column" gap="10px" border="2px dashed #E5E5E5">
        <Formik
          initialValues={initialValues}
          validateOnBlur={false}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, touched, errors, setFieldValue, handleSubmit, handleChange }) => (
            <Form style={{ padding: '20px' }}>
              <SharedStyled.FlexBox width="100%" flexDirection="column" gap="10px">
                <SharedStyled.TwoInputDiv>
                  <InputWithValidation
                    labelName="First Name*"
                    stateName="firstName"
                    error={touched.firstName && errors.firstName ? true : false}
                    twoInput={true}
                  />
                  <InputWithValidation
                    labelName="Last Name"
                    stateName="lastName"
                    error={touched.lastName && errors.lastName ? true : false}
                    twoInput={true}
                  />
                </SharedStyled.TwoInputDiv>

                <SharedStyled.TwoInputDiv>
                  <SharedPhone
                    labelName="Phone"
                    stateName="phone"
                    value={values.phone || ''}
                    onChange={handleChange('phone')}
                    error={touched.phone && errors.phone ? true : false}
                  />
                  <InputWithValidation labelName="Email" stateName="email" value={values.email} />
                </SharedStyled.TwoInputDiv>

                <SharedStyled.FlexRow justifyContent="flex-start" alignItems="flex-start">
                  <div style={{ width: '100%' }}>
                    <SharedStyled.FlexRow>
                      <b>Address - </b>
                      <p
                        className="link"
                        onClick={() => {
                          setShowEditAddress(!showEditAddress)
                        }}
                      >
                        {showEditAddress ? 'Confirm' : 'Edit'}
                      </p>
                    </SharedStyled.FlexRow>

                    {showEditAddress ? (
                      <AutoCompleteAddress
                        setFieldValue={setFieldValue}
                        street={'street'}
                        city={'city'}
                        state={'state'}
                        zip={'zip'}
                        sourceAddress={companySettingForAll?.address}
                        companyLatLong={companySettingForAll}
                        noLoadScript={true}
                      />
                    ) : (
                      <AddressWrap className="font">
                        <p>{values?.street}</p>
                        <p>
                          {values?.city}, {values.state} {values.zip}
                        </p>
                      </AddressWrap>
                    )}
                  </div>
                </SharedStyled.FlexRow>

                <InputWithValidation labelName="Notes" stateName="notes" value={values.notes} />

                <AutoComplete
                  labelName="Relationship*"
                  dropdownHeight="300px"
                  stateName="relationship"
                  value={values.relationship}
                  options={relationshipOptions}
                  error={touched.relationship && errors.relationship ? true : false}
                  setFieldValue={setFieldValue}
                  setValueOnClick={(val: string) => {
                    setFieldValue('relationship', val)
                  }}
                />
              </SharedStyled.FlexBox>

              <SharedStyled.FlexBox gap="12px" marginTop="26px">
                <Button className="gray" type="button" onClick={onClose}>
                  Cancel
                </Button>
                <Button isLoading={loading} type="button" onClick={() => handleSubmit()}>
                  Save
                </Button>
              </SharedStyled.FlexBox>
            </Form>
          )}
        </Formik>
      </SharedStyled.FlexBox>
    </>
  )
}
