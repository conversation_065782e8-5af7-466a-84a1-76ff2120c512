// import * as Styled from './style'
// import * as SharedStyled from '../../styles/styled'
// import { Table } from '../../shared/table/Table'
// import { notify } from '../../shared/helpers/util'
// import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
// import { useSelector } from 'react-redux'
// import { CrewManagementForm } from './components/crewManagementForm/CrewManagementForm'
// import { getCompanyCrews } from '../../logic/apis/crew'
// import { useParams } from 'react-router-dom'
// import { WORK_TYPE1 } from '../../shared/helpers/constants'
// import { colors } from '../../styles/theme'
// import { CustomModal } from '../../shared/customModal/CustomModal'
// import { ConfirmationPopUp } from './components/confirmationPopup/ConfirmationPopUp'

// export const Crew = () => {
//   const [, setDummyData] = useState<any>(
//     Array(1000).fill({
//       crewName: 'Rileys Crew',
//       type: 'roofing',
//       manager: '<PERSON> <PERSON>',
//       foreman: '<PERSON> <PERSON>pier',
//     })
//   )
//   const [loading, setLoading] = useState<boolean>(false)
//   const [showAddCrewForm, setShowAddCrewForm] = useState<boolean>(false)
//   const [showRetiredCrew, setShowRetiredCrew] = useState<boolean>(false)
//   const [showRetiredCrewButton, setShowRetiredCrewButton] = useState<boolean>(false)
//   const [crewDetails, setCrewDetails] = useState<any>({})
//   const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
//   const [update, setUpdate] = useState<boolean>(false)
//   const [pageCount, setPageCount] = useState<number>(10)
//   const [data, setData] = useState<any>([])
//   const [noData, setNoData] = useState(false)
//   const fetchIdRef = useRef(0)

//   const globalSelector = useSelector((state: any) => state)
//   const { currentCompany, position } = globalSelector.company
//   const { justInvited } = globalSelector.invitation

//   const fetchData = useCallback(
//     async ({ pageSize, pageIndex }: { pageSize: number; pageIndex: number }) => {
//       try {
//         // This will get called when the table needs new data

//         let receivedData: any = []
//         let receivedData1: any = []

//         console.log('currentCompany', currentCompany)
//         let currentCompanyData: any = localStorage.getItem('currentCompany')

//         const statusResponse = await getCompanyCrews(
//           { retired: false, deleted: false, companyId: JSON.parse(currentCompanyData)._id },
//           id
//         )

//         const statusResponse1 = await getCompanyCrews(
//           { retired: true, deleted: false, companyId: JSON.parse(currentCompanyData)._id },
//           id
//         )
//         console.log('statusResponse', statusResponse, statusResponse1)
//         if (statusResponse?.data?.statusCode === 200 && statusResponse1?.data?.statusCode === 200) {
//           let statusRes = statusResponse?.data?.data?.crew
//           let statusRes1 = statusResponse1?.data?.data?.crew
//           statusRes.forEach((res: any, index: number) => {
//             receivedData.push({
//               crewName: res.name,
//               type: WORK_TYPE1[res.workType],
//               manager: res.managerName,
//               foreman: res.foremanName ? res.foremanName : '-',
//               managerId: res.managerId,
//               crewId: res._id,
//               link: true,
//             })
//           })
//           if (statusRes1.length > 0) {
//             setShowRetiredCrewButton(true)
//           }
//           statusRes1.forEach((res: any, index: number) => {
//             receivedData1.push({
//               crewName: res.name,
//               type: WORK_TYPE1[res.workType],
//               manager: res.managerName,
//               foreman: res.retireDate ? (
//                 <SharedStyled.FlexBox width="100%" alignItems="center" gap="5px">
//                   <>
//                     Retired on:
//                     {new Date(res.retireDate.slice(0, 10)).toLocaleDateString('en-us', {
//                       weekday: 'long',
//                       year: 'numeric',
//                       month: 'short',
//                       day: 'numeric',
//                     })}
//                   </>
//                   <SharedStyled.Button
//                     maxWidth="90px"
//                     mediaFontSize="12px"
//                     maxHeight="28px"
//                     bgColor={colors.warning}
//                     onClick={() => {
//                       setCrewDetails({
//                         name: res?.name,
//                         id: res?._id,
//                       })
//                       setShowConfirmationPopUp(true)
//                     }}
//                   >
//                     Reactivate
//                   </SharedStyled.Button>
//                 </SharedStyled.FlexBox>
//               ) : (
//                 '-'
//               ),
//               managerId: res.managerId,
//               crewId: res._id,
//               retired: true,
//               link: false,
//             })
//           })
//           console.log('receivedData', receivedData)
//         } else {
//           notify(statusResponse?.data?.message, 'error')
//           notify(statusResponse1?.data?.message, 'error')
//         }

//         if (showRetiredCrew) {
//           receivedData = [...receivedData, ...receivedData1]
//           if (noData) {
//             setNoData(false)
//           }
//         } else {
//           if (receivedData.length === 0) {
//             setNoData(true)
//           }
//         }

//         console.log('pageSize, pageIndex', pageSize, pageIndex)
//         // Give this fetch an ID
//         const fetchId = ++fetchIdRef.current

//         // Set the loading state
//         setLoading(true)

//         // We'll even set a delay to simulate a server here
//         setTimeout(() => {
//           // Only update the data if this is the latest fetch
//           if (fetchId === fetchIdRef.current) {
//             const startRow = pageSize * pageIndex
//             const endRow = startRow + pageSize
//             setData(receivedData.slice(startRow, endRow))

//             // Your server could send back total page count.
//             // For now we'll just fake it, too
//             setPageCount(Math.ceil(receivedData.length / pageSize))
//             console.log('pageCount', pageCount)
//             setLoading(false)
//           }
//         }, 1000)
//       } catch (error) {
//         console.error('TeamTable fetchData error', error)
//       }
//     },
//     [showRetiredCrew, update]
//   )

//   const columns: any = useMemo(
//     () => [
//       {
//         Header: 'Crew Name',
//         accessor: 'crewName', // accessor is the "key" in the data
//       },
//       {
//         Header: 'Type',
//         accessor: 'type',
//       },
//       {
//         Header: 'Manager',
//         accessor: 'manager',
//       },
//       {
//         Header: 'Foreman',
//         accessor: 'foreman',
//       },
//     ],
//     []
//   )

//   const getDetails = async () => {
//     try {
//       if (Object.keys(currentCompany).length > 0) {
//         // const statusResponse = await getCompanyCrews(
//         //   { retired: false, deleted: false, companyId: currentCompany._id },
//         //   id
//         // )
//         // const statusResponse1 = await getCompanyCrews(
//         //   { retired: true, deleted: false, companyId: currentCompany._id },
//         //   id
//         // )
//         const apiCalls = [
//           getCompanyCrews({ retired: false, deleted: false, companyId: currentCompany._id }, id),
//           getCompanyCrews({ retired: true, deleted: false, companyId: currentCompany._id }, id),
//         ]
//         const [statusResponse, statusResponse1] = await Promise.all(apiCalls)
//         if (statusResponse?.data?.statusCode === 200 && statusResponse1?.data?.statusCode === 200) {
//           let statusRes = statusResponse?.data?.data?.crew
//           let statusRes1 = statusResponse1?.data?.data?.crew
//           if (statusRes1.length > 0) {
//             setShowRetiredCrewButton(true)
//           }
//           if (statusRes.length > 0) {
//             setNoData(false)
//           } else {
//             setNoData(true)
//           }
//         } else {
//           notify(statusResponse?.data?.message, 'error')
//         }
//       }
//     } catch (error) {
//       console.error('getDetails error', error)
//     }
//   }

//   useEffect(() => {
//     getDetails()
//   }, [currentCompany])

//   useEffect(() => {
//     fetchData({ pageSize: 10, pageIndex: 0 })
//   }, [showRetiredCrew])

//   return (
//     <>
//       <Styled.CrewContainer>
//         <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px" wrap="wrap">
//           <SharedStyled.ContentHeader textAlign="left">Crew Management</SharedStyled.ContentHeader>
//           {position !== 'ProjectManager' && (
//             <>
//               <SharedStyled.Button type="submit" maxWidth="200px" onClick={() => setShowAddCrewForm((prev) => !prev)}>
//                 {showAddCrewForm ? 'Hide Add Crew' : 'Add Crew'}
//               </SharedStyled.Button>

//               {(!noData || showRetiredCrewButton) && (
//                 <SharedStyled.Button type="submit" maxWidth="200px" onClick={() => setShowRetiredCrew((prev) => !prev)}>
//                   {showRetiredCrew ? 'Hide' : 'Show'} Retired Crew
//                 </SharedStyled.Button>
//               )}
//             </>
//           )}
//         </SharedStyled.FlexBox>

//         {/* {showAddCrewForm ? ( */}
//         <CustomModal show={showAddCrewForm}>
//           <CrewManagementForm setShowAddCrewForm={setShowAddCrewForm} setUpdate={setUpdate} setNoData={setNoData} />
//         </CustomModal>
//         {/* ) : ( */}
//         {/* <></>
//         )} */}
//         {noData ? (
//           <SharedStyled.FlexBox width="100%" alignItems="center" justifyContent="center" marginTop="20px">
//             No Crew Created yet, Please Create one
//           </SharedStyled.FlexBox>
//         ) : (
//           <Table
//             columns={columns}
//             data={data}
//             loading={loading}
//             pageCount={pageCount}
//             fetchData={fetchData}
//             crew={true}
//           />
//         )}
//       </Styled.CrewContainer>
//       <CustomModal show={showConfirmationPopUp}>
//         <ConfirmationPopUp
//           setShowConfirmationPopUp={setShowConfirmationPopUp}
//           setDetailsUpdate={setUpdate}
//           header="Reactivate Crew"
//           crewData={crewDetails}
//         />
//       </CustomModal>
//     </>
//   )
// }
