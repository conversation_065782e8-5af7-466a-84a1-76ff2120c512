import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { AddCrewMemberForm } from './components/addCrewMemberForm/AddCrewMemberForm'
import { Table } from '../../../../shared/table/Table'
import { EditIcon } from '../../../../assets/icons/EditIcon'
import { DoubleArrowUpIcon } from '../../../../assets/icons/DoubleArrowUpIcon'
import { RemoveIcon } from '../../../../assets/icons/RemoveIcon'
import { colors } from '../../../../styles/theme'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { CrewPopup } from './components/crewPopup/CrewPopup'
import { EditCrewForm } from './components/editCrewForm/EditCrewForm'
import { getCrewById, getCrewMembers } from '../../../../logic/apis/crew'
import { useNavigate, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { getDataFromLocalStorage, getFormattedDate, notify } from '../../../../shared/helpers/util'
import dayjs from 'dayjs'
import 'dayjs/locale/en'
import { StorageKey } from '../../../../shared/helpers/constants'

const CrewMember = () => {
  const [showAddCrewMemberForm, setShowAddCrewMemberForm] = useState<boolean>(false)
  const [showEditCrewMemberForm, setShowEditCrewMemberForm] = useState<boolean>(false)
  const [showPromoteCrewMemberForm, setShowPromoteCrewMemberForm] = useState<boolean>(false)
  const [showRemoveCrewMemberForm, setShowRemoveCrewMemberForm] = useState<boolean>(false)
  const [showEditCrewForm, setShowEditCrewForm] = useState<boolean>(false)
  const [showRetireCrewForm, setShowRetireCrewForm] = useState<boolean>(false)
  const [startDate, setStartDate] = useState<any>('')
  const [update, setUpdate] = useState<boolean>(false)
  const [detailsUpdate, setDetailsUpdate] = useState<boolean>(false)
  const [noData, setNoData] = useState(true)
  const [crewName, setCrewName] = useState('')
  const [currentCrewMemberId, setCurrentCrewMemberId] = useState('')
  const [currentMemberId, setCurrentMemberId] = useState('')
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)

  const { crewId } = useParams()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company

  const [loading, setLoading] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const fetchIdRef = useRef(0)
  const navigate = useNavigate()
  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []

        let crId: any = crewId
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const statusResponse = await getCrewMembers({ crewId: crId, deleted: false })

        if (statusResponse?.data?.statusCode === 200) {
          let statusRes = statusResponse?.data?.data?.crewMembers
          if (statusRes.length > 0) {
            setNoData(false)
          }

          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              name: res.promoted ? <Styled.PromotedName>{res.memberName}</Styled.PromotedName> : res.memberName,
              dateJoined: new Date(res.startDate.slice(0, 10)).toLocaleDateString('en-us', {
                weekday: 'long',
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              }),
              actions: (
                <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                  <>
                    <SharedStyled.TooltipContainer width={'100px'} className="crew">
                      <span className="tooltip-content">Edit</span>

                      <Styled.IconContainer
                        className="edit"
                        onClick={() => {
                          setShowEditCrewMemberForm(true)
                          setCurrentCrewMemberId(res._id)
                          setCurrentMemberId(res.memberId)
                        }}
                      >
                        <EditIcon />
                      </Styled.IconContainer>
                    </SharedStyled.TooltipContainer>
                  </>
                  {!res.promoted && (
                    <>
                      <SharedStyled.TooltipContainer width={'100px'}>
                        <span className="tooltip-content">Promote</span>
                        <Styled.IconContainer
                          className="promote"
                          onClick={() => {
                            setShowPromoteCrewMemberForm(true)
                            setCurrentCrewMemberId(res._id)
                            setCurrentMemberId(res.memberId)
                          }}
                        >
                          <DoubleArrowUpIcon />
                        </Styled.IconContainer>
                      </SharedStyled.TooltipContainer>
                    </>
                  )}
                  {!res.promoted && (
                    <>
                      <SharedStyled.TooltipContainer width={'100px'}>
                        <span className="tooltip-content">Retire</span>
                        <Styled.IconContainer
                          className="remove"
                          onClick={() => {
                            setShowRemoveCrewMemberForm(true)
                            setCurrentCrewMemberId(res._id)
                            setCurrentMemberId(res.memberId)
                          }}
                        >
                          <RemoveIcon />
                        </Styled.IconContainer>
                      </SharedStyled.TooltipContainer>
                    </>
                  )}
                </SharedStyled.FlexBox>
              ),
            })
          })
        } else {
          notify(statusResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          setPageCount(Math.ceil(receivedData.length / pageSize))
          setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('CrewMemberTable fetchData error', error)
      }
    },
    [update]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
      },
      {
        Header: 'Date Joined',
        accessor: 'dateJoined',
      },
      {
        Header: 'Actions',
        accessor: 'actions',
      },
    ],
    []
  )

  const getDetails = async () => {
    try {
      let crId: any = crewId

      // if (Object.keys(currentCompany).length > 0) {
      const statusResponse = await getCrewMembers({ crewId: crId, deleted: false })
      const statusResponse1 = await getCrewById({ _id: crId })
      if (statusResponse?.data?.statusCode === 200 && statusResponse1?.data?.statusCode === 200) {
        let statusRes = statusResponse?.data?.data?.crewMembers
        let statusRes1 = statusResponse1?.data?.data?.crew[0]
        setCrewName(statusRes1.name)
        setStartDate(getFormattedDate(dayjs(statusRes1?.startDate).format('YYYY-MM-DD'), 'YYYY-MM-DD'))

        if (statusRes.length > 0) {
          setNoData(false)
          setShimmerLoading(false)
        } else {
          setNoData(true)
          setShimmerLoading(false)
        }
      } else {
        notify(statusResponse?.data?.message, 'error')
        setShimmerLoading(false)
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
      setShimmerLoading(false)
    }
  }

  useEffect(() => {
    getDetails()
  }, [detailsUpdate])

  return (
    <>
      {shimmerLoading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <>
          <Styled.CrewMemberContainer>
            <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px" wrap="wrap">
              <SharedStyled.ContentHeader width="100%" textAlign="left">
                Members of {crewName}
              </SharedStyled.ContentHeader>
              <SharedStyled.FlexBox width="100%" alignItems="center" justifyContent="flex-end" gap="20px">
                <SharedStyled.Button
                  type="button"
                  maxWidth="200px"
                  onClick={() => setShowAddCrewMemberForm((prev) => !prev)}
                >
                  {showAddCrewMemberForm ? 'Hide' : ''} Add Crew Member
                </SharedStyled.Button>
                <SharedStyled.Button type="button" maxWidth="200px" onClick={() => setShowEditCrewForm(true)}>
                  Edit Crew
                </SharedStyled.Button>
              </SharedStyled.FlexBox>
            </SharedStyled.FlexBox>
            <SharedStyled.HorizontalDivider />
            {/* {showAddCrewMemberForm ? ( */}
            <CustomModal show={showAddCrewMemberForm}>
              <AddCrewMemberForm
                setShowAddCrewMemberForm={setShowAddCrewMemberForm}
                setUpdate={setUpdate}
                setNoData={setNoData}
                startDate={startDate}
              />
            </CustomModal>
            {/* ) : (
              ''
            )} */}
            {noData ? (
              <>
                <SharedStyled.FlexBox width="100%" alignItems="center" justifyContent="center" marginTop="20px">
                  No Crew Member added yet, Please Add one
                </SharedStyled.FlexBox>
                <SharedStyled.Button
                  type="submit"
                  onClick={() => setShowRetireCrewForm(true)}
                  bgColor={colors.error}
                  color={colors.white}
                  maxWidth="200px"
                >
                  Retire Crew
                </SharedStyled.Button>
              </>
            ) : (
              <Table
                columns={columns}
                data={data}
                loading={loading}
                pageCount={pageCount}
                fetchData={fetchData}
                noLink={true}
              />
            )}
            {!noData && (
              <SharedStyled.FlexBox
                width="100%"
                alignItems="center"
                gap="20px"
                wrap="wrap"
                marginTop="15px"
                justifyContent="space-between"
              >
                <SharedStyled.Button type="button" maxWidth="200px" onClick={() => navigate(`/team`)}>
                  Back
                </SharedStyled.Button>

                <SharedStyled.Button
                  type="submit"
                  onClick={() => setShowRetireCrewForm(true)}
                  bgColor={colors.error}
                  color={colors.white}
                  maxWidth="200px"
                >
                  Retire Crew
                </SharedStyled.Button>
              </SharedStyled.FlexBox>
            )}
          </Styled.CrewMemberContainer>
          <CustomModal show={showEditCrewMemberForm}>
            <CrewPopup
              setShowCrewPopup={setShowEditCrewMemberForm}
              header="Edit Crew Member"
              inputLabel="Date Joined"
              submitButtonColor={colors.darkGrey}
              buttonText="Change Date"
              loaderText="Changing.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              setUpdate={setUpdate}
              startDate={startDate}
            />
          </CustomModal>
          <CustomModal show={showPromoteCrewMemberForm}>
            <CrewPopup
              setShowCrewPopup={setShowPromoteCrewMemberForm}
              header="Promote Crew Member"
              inputLabel="Date Promoted To Foreman"
              submitButtonColor={colors.darkGrey}
              buttonText="Promote"
              loaderText="Promoting.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              setUpdate={setUpdate}
              startDate={startDate}
            />
          </CustomModal>
          <CustomModal show={showRemoveCrewMemberForm}>
            <CrewPopup
              setShowCrewPopup={setShowRemoveCrewMemberForm}
              header="Remove Crew Member"
              inputLabel="Last Day On Crew"
              submitButtonColor={colors.error}
              buttonText="Remove Member"
              loaderText="Removing.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              setUpdate={setUpdate}
              startDate={startDate}
            />
          </CustomModal>
          <CustomModal show={showRetireCrewForm}>
            <CrewPopup
              setShowCrewPopup={setShowRetireCrewForm}
              header="Retire Crew"
              inputLabel="Retire Crew Date"
              submitButtonColor={colors.error}
              buttonText="Remove Crew"
              loaderText="Retiring Crew.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              startDate={startDate}
            />
          </CustomModal>
          <CustomModal show={showEditCrewForm}>
            <EditCrewForm
              setShowEditCrewFormPopup={setShowEditCrewForm}
              setDetailsUpdate={setDetailsUpdate}
              startDate={startDate}
            />
          </CustomModal>
        </>
      )}
    </>
  )
}
export default CrewMember
