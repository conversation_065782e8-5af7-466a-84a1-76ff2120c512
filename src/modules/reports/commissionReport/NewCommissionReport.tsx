import * as Styled from './commissionStyle'
import { FlexCol, FlexRow, HorizontalDivider } from '../../../styles/styled'
import { CommissonData } from './commissionTypes'
import { dayjsFormat, formatDollarAmount, formatNumberToCommaS, renderClientName } from '../../../shared/helpers/util'
import Button from '../../../shared/components/button/Button'

const NewCommissionReport = ({ data, onPrintClick }: { data: CommissonData; onPrintClick: (idx: number) => void }) => {
  return (
    <Styled.ReportContainer>
      <Styled.ReportHeader>
        <FlexRow alignItems="flex-end" gap="32px" margin="0 0 4px 0">
          <Styled.ReportTitle>
            {data?.name} <span>Commission Report </span>
          </Styled.ReportTitle>

          <Button
            width="max-content"
            type="button"
            onClick={() => {
              onPrintClick(data?.idx)
            }}
            padding="6px 12px"
            className="noPrint"
          >
            Print
          </Button>
        </FlexRow>
        <Styled.PayPeriod className="title">
          {data?.periodStart} - {data?.periodEnd}
        </Styled.PayPeriod>
        <Styled.PayPeriod>PAY PERIOD</Styled.PayPeriod>
      </Styled.ReportHeader>
      {/* ========================================= Sales ========================================= */}
      <Styled.Section>
        <Styled.SectionHeader>
          <Styled.SectionTitle>SALES</Styled.SectionTitle>
          <Styled.SummaryNumbers>
            <FlexCol>
              <div>NUM</div>
              <span>{data?.sold?.num}</span>
            </FlexCol>
            <FlexCol>
              <div>VOLUME</div>
              <span>${formatNumberToCommaS(data?.sold?.volume)}</span>
            </FlexCol>
            <FlexCol>
              <div>EARNED</div>
              <span>${formatNumberToCommaS(data?.sold?.commission)}</span>
            </FlexCol>
          </Styled.SummaryNumbers>
        </Styled.SectionHeader>
        {data?.sold?.num ? (
          <Styled.Table>
            <thead>
              <tr>
                <Styled.Th>CLIENT</Styled.Th>
                <Styled.Th>SIGNED</Styled.Th>
                <Styled.Th>EARNED</Styled.Th>
                <Styled.Th>PRICE</Styled.Th>
                <Styled.Th>TYPE</Styled.Th>
              </tr>
            </thead>
            <tbody>
              {data?.sold?.opps?.map((opp) => (
                <tr key={opp?._id}>
                  <Styled.Td>
                    <Styled.ClientName>
                      {opp?.PO}
                      {opp?.num ? `-${opp?.num}` : ''}
                    </Styled.ClientName>
                    <Styled.ClientDetails>
                      {renderClientName(
                        opp?.contactId?.isBusiness,
                        opp?.contactId?.fullName,
                        opp?.contactId?.businessName
                      )}
                    </Styled.ClientDetails>
                  </Styled.Td>
                  <Styled.Td>{opp?.saleDate ? dayjsFormat(opp?.saleDate, 'MM/DD/YY') : '--'}</Styled.Td>

                  <Styled.Td>${formatNumberToCommaS(opp?.commission)}</Styled.Td>
                  <Styled.Td>
                    ${formatNumberToCommaS(opp?.soldValue)}
                    <Styled.FinanceInfo>{opp?.discountPercent?.toFixed(2)}% Off</Styled.FinanceInfo>
                  </Styled.Td>
                  <Styled.Td>
                    {opp?.pType?.name}
                    <Styled.FinanceInfo>{opp?.paymentType ?? '--'}</Styled.FinanceInfo>
                  </Styled.Td>
                </tr>
              ))}
            </tbody>
          </Styled.Table>
        ) : null}
      </Styled.Section>
      {/* ========================================= Completed ========================================= */}
      <Styled.Section>
        <Styled.SectionHeader>
          <Styled.SectionTitle>COMPLETED</Styled.SectionTitle>
          <Styled.SummaryNumbers>
            <FlexCol>
              <div>NUM</div>
              <span>{data?.completed?.num}</span>
            </FlexCol>
            <FlexCol>
              <div>VOLUME</div>
              <span>${formatNumberToCommaS(data?.completed?.volume)}</span>
            </FlexCol>
            <FlexCol>
              <div>EARNED</div>
              <span>${formatNumberToCommaS(data?.completed?.commission)}</span>
            </FlexCol>
          </Styled.SummaryNumbers>
        </Styled.SectionHeader>
        {data?.completed?.num ? (
          <Styled.Table>
            <thead>
              <tr>
                <Styled.Th>CLIENT</Styled.Th>
                <Styled.Th>SIGNED</Styled.Th>
                <Styled.Th>COMPLETED</Styled.Th>
                <Styled.Th>EARNED</Styled.Th>
                <Styled.Th>PRICE</Styled.Th>
                <Styled.Th>TYPE</Styled.Th>
              </tr>
            </thead>
            <tbody>
              {data?.completed?.opps?.map((opp) => (
                <tr key={opp?._id}>
                  <Styled.Td>
                    <Styled.ClientName>
                      {opp?.PO}
                      {opp?.num ? `-${opp?.num}` : ''}
                    </Styled.ClientName>
                    <Styled.ClientDetails>
                      {renderClientName(
                        opp?.contactId?.isBusiness,
                        opp?.contactId?.fullName,
                        opp?.contactId?.businessName
                      )}
                    </Styled.ClientDetails>
                  </Styled.Td>
                  <Styled.Td>{opp?.saleDate ? dayjsFormat(opp?.saleDate, 'MM/DD/YY') : '--'}</Styled.Td>
                  <Styled.Td>{dayjsFormat(opp?.jobCompletedDate, 'MM/DD/YY')}</Styled.Td>
                  <Styled.Td>${formatNumberToCommaS(opp?.commission)}</Styled.Td>
                  <Styled.Td>
                    ${formatNumberToCommaS(opp?.soldValue)}
                    <Styled.FinanceInfo>{opp?.discountPercent?.toFixed(2)}% Off</Styled.FinanceInfo>
                  </Styled.Td>
                  <Styled.Td>
                    {opp?.pType?.name}
                    <Styled.FinanceInfo>{opp?.paymentType ?? '--'}</Styled.FinanceInfo>
                  </Styled.Td>
                </tr>
              ))}
            </tbody>
          </Styled.Table>
        ) : null}
      </Styled.Section>
      {/* ========================================= Modifications ========================================= */}
      <Styled.Section>
        <Styled.SectionHeader>
          <Styled.SectionTitle>MODIFICATIONS</Styled.SectionTitle>
          <Styled.SummaryNumbers>
            <FlexCol>
              <div>NUM</div>
              <span>{data?.modified?.opp?.length}</span>
            </FlexCol>
            <FlexCol>
              <div>EARNED</div>
              <span>{formatDollarAmount(data?.modified?.amount)}</span>
            </FlexCol>
          </Styled.SummaryNumbers>
        </Styled.SectionHeader>
        {data?.modified?.num ? (
          <Styled.Table className="modify">
            <thead>
              <tr>
                <Styled.Th>CLIENT</Styled.Th>
                <Styled.Th>DATE</Styled.Th>
                <Styled.Th>EARNED</Styled.Th>
                <Styled.Th>TYPE</Styled.Th>
                <Styled.Th>REASON</Styled.Th>
              </tr>
            </thead>
            <tbody>
              {data?.modified?.opp?.map((ops) => (
                <tr key={ops?._id}>
                  <Styled.Td>
                    <Styled.ClientName>
                      {ops?.oppId?.PO}
                      {ops?.oppId?.num ? `-${ops?.oppId?.num}` : ''}
                    </Styled.ClientName>
                    <Styled.ClientDetails>
                      {renderClientName(ops?.contact?.isBusiness, ops?.contact?.fullName, ops?.contact?.businessName)}
                    </Styled.ClientDetails>
                  </Styled.Td>
                  <Styled.Td>{dayjsFormat(ops?.date, 'MM/DD/YY')}</Styled.Td>
                  <Styled.Td>{formatDollarAmount(ops?.amount)}</Styled.Td>
                  <Styled.Td>
                    {ops?.pType?.name}
                    <Styled.FinanceInfo>{ops?.paymentType ?? '--'}</Styled.FinanceInfo>
                  </Styled.Td>
                  <Styled.Td className="reason">{ops?.reason}</Styled.Td>
                </tr>
              ))}
            </tbody>
          </Styled.Table>
        ) : null}
      </Styled.Section>
      <FlexCol>
        <Styled.SectionDesc>THIS PAY PERIOD</Styled.SectionDesc>
        <Styled.Detail>Salary: ${formatNumberToCommaS(data?.salary)}</Styled.Detail>
        <Styled.Detail>Commission: {formatDollarAmount(data?.commission)}</Styled.Detail>
      </FlexCol>
      {data?.useBenchmarkBonus && data?.monthEnd ? (
        <FlexCol margin="20px 0 0 0">
          <Styled.SectionDesc>BENCHMARKS</Styled.SectionDesc>
          <Styled.Detail>
            {data?.month?.num} sales for ${formatNumberToCommaS(Number(data?.month?.volume))}
          </Styled.Detail>

          <Styled.Detail>
            {data?.benchmarks?.num} {data?.benchmarks?.text}, total bonus of $
            {formatNumberToCommaS(Number(data?.benchmarks?.bonus))}
          </Styled.Detail>
        </FlexCol>
      ) : null}

      <FlexRow margin="20px 0">
        <b className="total">Total Earned This Period: {formatDollarAmount(data?.totalEarned)}</b>
      </FlexRow>
      <HorizontalDivider margin="0 0 10px 0" bg="lightGrey" />
    </Styled.ReportContainer>
  )
}

export default NewCommissionReport
