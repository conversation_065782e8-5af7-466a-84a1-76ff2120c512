import React, { useEffect, useRef, useState } from 'react'
import { Field, Form, Formik } from 'formik'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'

import { useAppSelector } from '../../../../logic/redux/reduxHook'

import Button from '../../../../shared/components/button/Button'

import Toggle from '../../../../shared/toggle/Toggle'
import { handleInputWithTwoDecimals } from '../../../../shared/helpers/util'

interface I_ActionModal {
  onClose: any
  handleUpdateData: (data: any, onClose: () => void) => Promise<void>
  modifiedBudget: {
    subContractorTotal: number
    isSubcontractorOnly: boolean
    rawLaborBudget: number
  }
  loading: boolean
}

interface InitialValues {
  subContractorTotal: number
  isSubcontractorOnly: boolean
  rawLaborBudget: number
}

const SubContracctorModal: React.FC<I_ActionModal> = (props) => {
  const { company } = useAppSelector((state) => state)
  const [showPreview, setShowPreview] = useState(false)

  const memberId = company?.currentMember?._id

  const { onClose, handleUpdateData, modifiedBudget, loading } = props
  const initialValues: InitialValues = {
    subContractorTotal: Object.entries(modifiedBudget || {}).length ? modifiedBudget?.subContractorTotal : 0,
    isSubcontractorOnly: Object.entries(modifiedBudget || {}).length ? modifiedBudget?.isSubcontractorOnly : true,
    rawLaborBudget: Object.entries(modifiedBudget || {}).length ? modifiedBudget?.rawLaborBudget : 0,
  }

  const handleSubmit = async (values: typeof initialValues) => {
    const data = {
      subContractorTotal: values.subContractorTotal ? values.subContractorTotal : 0,
      isSubcontractorOnly: values.isSubcontractorOnly,
      rawLaborBudget: values.rawLaborBudget ? values.rawLaborBudget : 0,
    }
    await handleUpdateData({ modifiedBudget: data }, onClose)
  }

  return (
    <Styled.StepModalContainer className={showPreview ? 'preview' : ''}>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        // validationSchema={newActionSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue }) => {
          useEffect(() => {
            if (values.isSubcontractorOnly) {
              setFieldValue('rawLaborBudget', 0)
            }
          }, [values])
          return (
            <>
              <Styled.ModalHeaderContainer>
                <Styled.ModalHeader>Subcontractor Budget</Styled.ModalHeader>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer padding="0px 20px 20px 20px">
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Budgeted Amount"
                      stateName="subContractorTotal"
                      forceType="number"
                      onChange={(e: any) => {
                        handleInputWithTwoDecimals(e.target.value, setFieldValue, 'subContractorTotal')
                      }}
                      error={touched.subContractorTotal && errors.subContractorTotal ? true : false}
                    />

                    <Toggle
                      title="Subcontractor only"
                      //   className="text"
                      customStyles={{ margin: '16px', justifyContent: 'flex-start' }}
                      isToggled={values.isSubcontractorOnly}
                      onToggle={() => {
                        setFieldValue('isSubcontractorOnly', !values.isSubcontractorOnly)
                      }}
                    />
                    {/* Subcontractor only */}
                    {!values.isSubcontractorOnly && (
                      <InputWithValidation
                        labelName="In-House Crew Budgeted Amount"
                        stateName="rawLaborBudget"
                        forceType="number"
                        onChange={(e: any) => {
                          handleInputWithTwoDecimals(e.target.value, setFieldValue, 'rawLaborBudget')
                        }}
                        error={touched.rawLaborBudget && errors.rawLaborBudget ? true : false}
                      />
                    )}

                    <SharedStyled.FlexRow margin="16px 0 0 0">
                      <Button type="submit" isLoading={loading}>
                        Save
                      </Button>
                    </SharedStyled.FlexRow>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.StepModalContainer>
  )
}

export default SubContracctorModal
