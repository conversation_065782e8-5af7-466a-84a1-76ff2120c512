import React, { useEffect, useRef, useState } from 'react'
import { Form, Formik } from 'formik'

import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UploadIcon from '../../../../assets/newIcons/uploadIcon.svg'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { Table } from '../../../../shared/table/Table'
import { formatNumberToCommaS, generateUUID, isSuccess, notify } from '../../../../shared/helpers/util'
import { TableContainer, TableContent, TableContentLabel, TableHeading, TableTitle } from '../style'
import { useAppSelector } from '../../../../logic/redux/reduxHook'
import { FilePathTypeEnum, mimeTypesMap } from '../../../../shared/helpers/constants'
import { processFile, renderMedia } from '../../../media/Media'
import MediaPreview from '../../../media/components/MediaPreview'
import Button from '../../../../shared/components/button/Button'
import { extractImageData, validateFiles } from '../../../media/mediaUtils'
import { createMediaOpportunity, FilePayload, getPresignedUrlMedia } from '../../../../logic/apis/media'
import { FullpageLoader } from '../../../../shared/components/loader/Loader'

interface I_ActionModal {
  onClose: any
  updateData: () => void
  setInventoryCost: React.Dispatch<React.SetStateAction<number>>
  setQbCOGS: React.Dispatch<React.SetStateAction<number>>
  setReceipts: React.Dispatch<React.SetStateAction<number>>
  setOther: React.Dispatch<React.SetStateAction<number>>
  setActualMatCost: React.Dispatch<React.SetStateAction<number>>
  loading: boolean
  inventoryCost: number
  qbCOGS: number
  receipts: number
  other: number
  actualMatCost: number
  projectReportData: any
  oppIdURL?: string
  mediaData?: any[]
  onSuccess?: () => void
}

interface I_initialValues {
  cogs: number
  receipts: number
  inventoryCosts: number
  other: number
  actualMatCost: number
}

const MaterialInputModal: React.FC<I_ActionModal> = (props) => {
  const { company } = useAppSelector((state) => state)
  const [files, setFiles] = useState<any>([])
  const [showPreview, setShowPreview] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [uploadLoading, setUploadLoading] = useState(false)

  const memberId = company?.currentMember?._id

  const allowedMediaTypes = company?.companySettingForAll?.allowedMediaTypes ?? []

  const allowedMIMETypes = allowedMediaTypes.map((ext: string) => mimeTypesMap[ext] || '').filter(Boolean)

  const [inventory, setInventory] = useState([])
  const [cost, setCost] = useState(0)
  const [tax, setTax] = useState(0)
  const [totalCost, setTotalCost] = useState(0)
  const {
    onClose,
    updateData,
    setInventoryCost,
    setQbCOGS,
    setReceipts,
    setOther,
    setActualMatCost,
    loading,
    qbCOGS,
    receipts,
    inventoryCost,
    other,
    actualMatCost,
    oppIdURL,
    mediaData,
    onSuccess,
    projectReportData,
  } = props

  useEffect(() => {
    if (mediaData?.length) {
      setFiles(mediaData)
    }
  }, [mediaData])
  const oppId = oppIdURL!

  const initialValues: I_initialValues = {
    cogs: qbCOGS ?? 0,
    receipts: receipts ?? 0,
    inventoryCosts: inventoryCost ?? 0,
    other: other ?? 0,
    actualMatCost: actualMatCost ?? 0,
  }

  const uploadRef = useRef(null)
  const columns = [
    {
      Header: 'Name',
      accessor: 'name',
    },
    {
      Header: 'Amt',
      accessor: (row: any) => row.amount?.toFixed(2),
    },
    {
      Header: 'Cost',
      accessor: (row: any) => row.cost?.toFixed(2),
    },
  ]
  const handleSubmit = async (values: typeof initialValues) => {
    await updateData()
  }

  useEffect(() => {
    if (Object.keys(projectReportData?.opportunity).length !== 0) {
      const { inventory, cost, tax, totalCost } = inventoryItems(projectReportData)
      setInventory(inventory)
      setCost(cost)
      setTax(tax)
      setTotalCost(totalCost)
    }
  }, [projectReportData])

  const inventoryItems = (data: any) => {
    const inventory = data?.opportunity?.order?.matList.filter(
      (mat: any) => mat.inventory === true && mat.name !== 'Dump Fee'
    )
    let cost: number = 0
    inventory.map((mat: any) => {
      cost += mat.cost ?? 0
    })
    cost = Number(cost?.toFixed(2))
    const tax = data?.opportunity?.opp?.state === 'ID' ? Number((cost * 0.06)?.toFixed(2)) : 0
    const totalCost = Number((cost + tax)?.toFixed(2))
    return {
      inventory,
      cost,
      tax,
      totalCost,
    }
  }

  const handleFileChange = async (e: any) => {
    const selectedFiles = Array.from(e.target.files)

    const processedFiles = await Promise.all(selectedFiles.map(processFile))

    const newFiles = processedFiles.map((file: any) => ({
      url: URL.createObjectURL(file),
      mimetype: file.type,
      name: file?.name,
      file,
    }))

    const validFiles = validateFiles(newFiles, {
      maxImageSizeMB: company?.companySettingForAll?.maxImageSizeMB,
      maxVideoSizeMB: company?.companySettingForAll?.maxVideoSizeMB,
    })

    setFiles([...files, ...validFiles])

    try {
      setUploadLoading(true)

      const res = await getPresignedUrlMedia(
        FilePathTypeEnum.Project,
        validFiles?.map((itm: any) => ({
          fileName: itm?.name,
          mimetype: itm?.mimetype,
        })),
        memberId!,
        oppId!
      )

      const urlData: Record<string, any> = res?.data?.data?.signedUrls?.reduce((acc: any, itm: any) => {
        acc[itm?.fileName] = itm
        return acc
      }, {})

      const uploadPromises = validFiles?.map((itm: any) => {
        const myHeaders = new Headers()
        myHeaders.append('Content-Type', itm?.mimetype)

        const requestOptions = {
          method: 'PUT',
          headers: myHeaders,
          body: itm?.file,
        }

        return fetch(urlData[itm?.name]?.url, requestOptions)
      })

      await Promise.all(uploadPromises)

      const payload: FilePayload[] = []

      const onlyImages = validFiles?.filter((itm: any) => itm?.mimetype?.includes('image'))

      const metaData = await extractImageData(onlyImages)

      validFiles?.forEach((itm: any) => {
        if (itm?.mimetype?.includes('image')) {
          payload.push({
            _id: generateUUID()!,
            createdAt: metaData?.[itm?.name]?.createdAt,
            createdBy: memberId,
            mimetype: itm?.mimetype,
            name: itm?.name,
            url: urlData?.[itm?.name]?.url?.split('?')[0],
            location: metaData?.[itm?.name]?.location || undefined,
            tags: ['Job Cost'],
          })
        } else {
          payload.push({
            _id: generateUUID()!,
            createdAt: new Date().toISOString(),
            createdBy: memberId,
            mimetype: itm?.mimetype,
            name: itm?.name,
            url: urlData?.[itm?.name]?.url?.split('?')[0],
            tags: ['Job Cost'],
          })
        }
      })

      const uploadRes = await createMediaOpportunity(oppId!, payload)

      if (isSuccess(uploadRes)) {
        notify(uploadRes?.data?.data?.message, 'success')
        onSuccess?.()
      }
    } catch (error) {
    } finally {
      setUploadLoading(false)
    }
  }

  return (
    <Styled.StepModalContainer className={showPreview ? 'preview' : ''}>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        // validationSchema={newActionSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange, handleSubmit }) => {
          useEffect(() => {
            setQbCOGS(Number(values.cogs))
            setReceipts(Number(values.receipts))
            setInventoryCost(Number(values.inventoryCosts))
            setOther(Number(values.other))
            setActualMatCost(
              Number(values.cogs) + Number(values.receipts) + Number(values.inventoryCosts) + Number(values.other)
            )
          }, [values])
          return (
            <>
              <Styled.ModalHeaderContainer>
                <Styled.ModalHeader>Add Materials</Styled.ModalHeader>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                    setQbCOGS(Number(projectReportData?.opportunity?.order?.actualTotals?.qbCOGS || 0))
                    setReceipts(Number(projectReportData?.opportunity?.order?.actualTotals?.receipts || 0))
                    setInventoryCost(Number(projectReportData?.opportunity?.order?.actualTotals?.inventoryCost || 0))
                    setOther(Number(projectReportData?.opportunity?.order?.actualTotals?.other || 0))
                    setActualMatCost(Number(projectReportData?.opportunity?.order?.actualTotals?.actualMatCost || 0))
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer padding="0px 20px 20px 20px">
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="COGS from QB:"
                      stateName="cogs"
                      forceType="number"
                      error={touched.cogs && errors.cogs ? true : false}
                    />

                    <InputWithValidation
                      labelName="Receipts:"
                      stateName="receipts"
                      forceType="number"
                      error={touched.receipts && errors.receipts ? true : false}
                    />

                    <InputWithValidation
                      labelName="Inventory Costs:"
                      stateName="inventoryCosts"
                      forceType="number"
                      error={touched.inventoryCosts && errors.inventoryCosts ? true : false}
                    />

                    <InputWithValidation
                      labelName="Other:"
                      stateName="other"
                      forceType="number"
                      error={touched.other && errors.other ? true : false}
                    />

                    <SharedStyled.FlexCol>
                      <SharedStyled.FlexRow margin="0 0 20px 0" justifyContent="space-between">
                        <SharedStyled.ButtonContainer marginTop="20px">
                          <SharedStyled.FlexRow>
                            <Button type="submit" maxWidth="150px" isLoading={loading}>
                              Save Materials
                            </Button>

                            {/* {!files?.length ? null : (
                              <Button
                                isLoading={uploadLoading}
                                type="button"
                                maxWidth="150px"
                                onClick={handleFileUpload}
                              >
                                Upload
                              </Button>
                            )} */}
                          </SharedStyled.FlexRow>
                        </SharedStyled.ButtonContainer>
                        {uploadLoading ? (
                          <div className="upload-loading">
                            <FullpageLoader />
                          </div>
                        ) : (
                          <div style={{ width: 'max-content' }}>
                            <input
                              type="file"
                              ref={uploadRef}
                              style={{ visibility: 'hidden', width: '0px' }}
                              onChange={handleFileChange}
                              accept={`${allowedMIMETypes.join(',')}`}
                              multiple
                            />
                            <img
                              src={UploadIcon}
                              alt="upload icon"
                              style={{ width: '40px', marginTop: '20px', cursor: 'pointer' }}
                              onClick={() => {
                                // @ts-ignore
                                uploadRef.current.click()
                              }}
                            />
                          </div>
                        )}
                      </SharedStyled.FlexRow>
                      <SharedStyled.FlexRow flexWrap="wrap">
                        {(files || mediaData)?.map((file: any, idx: number) => {
                          return (
                            <Styled.MediaCont
                              key={idx}
                              onClick={() => {
                                setSelectedIndex(idx)
                                setShowPreview(true)
                              }}
                            >
                              {renderMedia(file.url, file.mimetype)}
                            </Styled.MediaCont>
                          )
                        })}
                      </SharedStyled.FlexRow>

                      {showPreview ? (
                        <MediaPreview
                          isShareView
                          selectedIndex={selectedIndex}
                          allMedia={files?.map((itm: any) => ({
                            url: itm?.url,
                            mimetype: itm?.mimetype,
                            user: itm?.createdBy?.name,
                            userImage: itm?.createdBy?.userImageUrl?.imageUrl,
                            createdAt: itm?.createdAt,
                            id: itm?.createdBy?.userImageUrl?._id,
                            tags: itm?.tags,
                            imageId: itm?._id,
                            name: itm?.name,
                            originalData: itm,
                          }))}
                          setAllMedia={setFiles}
                          onClose={() => {
                            setShowPreview(false)
                          }}
                        />
                      ) : null}
                    </SharedStyled.FlexCol>

                    <div>
                      <Styled.ModalHeader>From Inventory</Styled.ModalHeader>

                      <SharedStyled.FlexBox justifyContent="space-between">
                        <SharedStyled.FlexBox flexDirection={'column'} alignItems={'flex-end'}>
                          <p>Cost:</p>
                          <p>Tax:</p>
                          <p>
                            <b>Total</b>
                          </p>
                        </SharedStyled.FlexBox>
                        &emsp;
                        <SharedStyled.FlexBox flexDirection={'column'} alignItems={'flex-start'}>
                          <p>{cost}</p>
                          <p>{tax}</p>
                          <p>
                            <b>{totalCost}</b>
                          </p>
                        </SharedStyled.FlexBox>
                      </SharedStyled.FlexBox>

                      <TableContainer>
                        <TableHeading column="4fr repeat(2,1fr)">
                          <TableTitle>Name</TableTitle>
                          <TableTitle className="right-align">Amt</TableTitle>
                          <TableTitle className="right-align">Cost</TableTitle>
                        </TableHeading>
                        {inventory?.map((value: any, i: number) => {
                          return (
                            <TableContent column="4fr repeat(2,1fr)" pointer="pointer" key={i}>
                              <TableContentLabel>{value?.name}</TableContentLabel>
                              <TableContentLabel className="right-align">{value?.amount?.toFixed(2)}</TableContentLabel>
                              <TableContentLabel className="right-align">
                                ${formatNumberToCommaS(value?.cost)}
                              </TableContentLabel>
                            </TableContent>
                          )
                        })}
                      </TableContainer>
                    </div>

                    {/* <Table
                      columns={columns}
                      data={inventory}
                      fetchData={() => {}}
                      noSearch={true}
                      noPagination={true}
                    /> */}
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.StepModalContainer>
  )
}

export default MaterialInputModal
