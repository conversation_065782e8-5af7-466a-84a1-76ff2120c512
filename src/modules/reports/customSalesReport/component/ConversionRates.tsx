import React, { Fragment, useState } from 'react'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from '../style'
import { Link } from 'react-router-dom'
import { dayjsFormat, formatNumberToCommaS, getEnumValue, renderClientName } from '../../../../shared/helpers/util'
import { I_Stage } from '../../../opportunity/components/assessmentForm/AssessmentForm'
interface Checkpoint {
  name: string
  count: number
  percent: number | null
  sequence: number | null
  breakdown: any[]
  salesPerson: any
  soldValue?: number
  perLeadSold?: number
}

interface ConvReport {
  id: string
  allOpps: any[] // Assuming it's an array, replace 'any' with the actual type if known
  checkpoints: Checkpoint[]
  name: string
  typeReplacement: boolean
}

interface InitialValues {
  convReport: ConvReport[]
  stages: I_Stage[]
}

const ConversionRates: React.FC<InitialValues> = ({ convReport, stages }) => {
  const [toggleConvCount, setToggleConvCount] = useState<{ [key: string]: boolean }>({})

  const toggleCount = (type: string) => {
    setToggleConvCount((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const checkValue = (value: number | null, isPercent?: boolean) => {
    if (value === null || value === 0) {
      return '--' // or return whatever you want to indicate "nothing"
    }
    // Add additional conditions or return values if needed
    return isPercent ? `${value}%` : value
  }

  const renderDates = (opp: any, cardName: string) => {
    if (['Opportunity > NA Scheduled', 'Needs Assessment Drop Off'].includes(cardName)) {
      return (
        <>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity?.oppDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity?.needsAssessmentDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
        </>
      )
    }

    if (
      [
        'NA Kept > Price Given',
        'NA Kept > No Price Given',
        'No Price Given > 2nd Appt kept',
        'Appt Kept > No Price Given',
        'No Price Given > 2nd Appt',
        'No Price Given > No 2nd Appt',
      ].includes(cardName)
    ) {
      return (
        <>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity?.needsAssessmentDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity.presentationDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
        </>
      )
    }

    if (['Price Given > Closed 1 stop', 'Price Given > Closed in followup'].includes(cardName)) {
      return (
        <>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity.presentationDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity?.saleDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
        </>
      )
    }

    if (['2nd Appt > Closed at table', '2nd Appt > Closed in followup'].includes(cardName)) {
      return (
        <>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity.needsAssessmentDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
          <Styled.CrewReportTableContentLabel>
            {dayjsFormat(opp?.checkpointActivity?.saleDate?.created, 'M/D/YY') || '--'}
          </Styled.CrewReportTableContentLabel>
        </>
      )
    }

    switch (cardName) {
      case 'Opportunity':
        return (
          <>
            <Styled.CrewReportTableContentLabel>
              {dayjsFormat(opp?.checkpointActivity?.oppDate?.created, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
          </>
        )

      case 'NA Scheduled > NA Kept':
        return (
          <>
            <Styled.CrewReportTableContentLabel>
              {dayjsFormat(opp?.checkpointActivity?.oppDate?.created, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
            <Styled.CrewReportTableContentLabel>
              {dayjsFormat(opp?.checkpointActivity?.needsAssessmentDate?.created, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
          </>
        )

      case 'NA Scheduled > NA Kept':
        return (
          <>
            <Styled.CrewReportTableContentLabel>
              {dayjsFormat(opp?.checkpointActivity?.needsAssessmentDate?.created, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
          </>
        )

      case 'Needs Assessment Cancels':
        return (
          <>
            <Styled.CrewReportTableContentLabel>
              {dayjsFormat(opp?.checkpointActivity?.presentationDate?.created, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
            <Styled.CrewReportTableContentLabel>
              {dayjsFormat(opp?.checkpointActivity?.needsAssessmentDate?.deleted, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
          </>
        )
      case 'Price Given > Not closed':
        return (
          <>
            <Styled.CrewReportTableContentLabel>
              {dayjsFormat(opp?.checkpointActivity?.presentationDate?.created, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
            <Styled.CrewReportTableContentLabel colors={opp?.checkpointActivity?.lostDate?.created ? 'red' : ''}>
              {dayjsFormat(opp?.checkpointActivity?.lostDate?.created, 'M/D/YY') || '--'}
            </Styled.CrewReportTableContentLabel>
          </>
        )
      default:
        return <Styled.CrewReportTableContentLabel>--</Styled.CrewReportTableContentLabel>
    }
  }

  const renderDatesColumn = (cardName: string) => {
    if (['Opportunity > NA Scheduled', 'Needs Assessment Drop Off'].includes(cardName)) {
      return (
        <>
          <Styled.TableTitle className="center-align">Opportunity</Styled.TableTitle>
          <Styled.TableTitle className="center-align">NA</Styled.TableTitle>
        </>
      )
    }

    if (
      [
        'NA Kept > Price Given',
        'NA Kept > No Price Given',
        'No Price Given > 2nd Appt kept',
        'Appt Kept > No Price Given',
        'No Price Given > 2nd Appt',
        'No Price Given > No 2nd Appt',
      ].includes(cardName)
    ) {
      return (
        <>
          <Styled.TableTitle className="center-align">NA</Styled.TableTitle>
          <Styled.TableTitle className="center-align">Presentation</Styled.TableTitle>
        </>
      )
    }

    if (['Price Given > Closed 1 stop', 'Price Given > Closed in followup'].includes(cardName)) {
      return (
        <>
          <Styled.TableTitle className="center-align">Presentation</Styled.TableTitle>
          <Styled.TableTitle className="center-align">Sale</Styled.TableTitle>
        </>
      )
    }

    if (['2nd Appt > Closed at table', '2nd Appt > Closed in followup'].includes(cardName)) {
      return (
        <>
          <Styled.TableTitle className="center-align">NA</Styled.TableTitle>
          <Styled.TableTitle className="center-align">Sale</Styled.TableTitle>
        </>
      )
    }

    switch (cardName) {
      case 'Opportunity':
        return (
          <>
            <Styled.TableTitle className="center-align">Opportunity</Styled.TableTitle>
          </>
        )

      case 'NA Scheduled > NA Kept':
        return (
          <>
            <Styled.TableTitle className="center-align">Opportunity</Styled.TableTitle>
            <Styled.TableTitle className="center-align">NA</Styled.TableTitle>
          </>
        )

      case 'NA Scheduled > NA Kept':
        return (
          <>
            <Styled.TableTitle className="center-align">NA</Styled.TableTitle>
          </>
        )

      case 'Needs Assessment Cancels':
        return (
          <>
            <Styled.TableTitle className="center-align">Presentation</Styled.TableTitle>
            <Styled.TableTitle className="center-align">NA</Styled.TableTitle>
          </>
        )
      case 'Price Given > Not closed':
        return (
          <>
            <Styled.TableTitle className="center-align">Presentation</Styled.TableTitle>
            <Styled.TableTitle className="center-align">Lost</Styled.TableTitle>
          </>
        )
      default:
        return <Styled.TableTitle className="center-align">--</Styled.TableTitle>
    }
  }

  const renderDays = (brk: { avg: number; low: number; high: number }) => {
    return (
      <Styled.ReportHeading textAlign="center" fontSize="12px">
        <span className="color-1">{brk.avg ? `${brk.avg}d (a), ` : ''}</span>
        <span className="color-3">{brk.low ? `${brk.low}d (l), ` : ''}</span>
        <span className="color-2">{brk.high ? `${brk.high}d (h)` : ''} </span>
      </Styled.ReportHeading>
    )
  }

  const renderSubCards = (chk: Checkpoint, brk: any, idx: number, index: number) => {
    return (
      <Styled.RateCard
        pointer="pointer"
        scaleOnHover={true}
        background={toggleConvCount[index + idx + chk?.name + brk?.name] ? '#d7d7d7' : 'none'}
        onClick={() => toggleCount(index + idx + chk?.name + brk?.name)}
      >
        <Styled.ReportHeading textAlign="center" fontSize="14px">
          {brk?.name || <></>}
        </Styled.ReportHeading>
        <Styled.ReportHeading textAlign="center" fontSize="14px">
          {checkValue(brk?.count)}{' '}
          {chk?.name === 'Opportunity'
            ? `: $${brk?.perLeadSold}`
            : `: ${checkValue(brk?.percent, true)} ${
                chk?.name === 'Sold' ? ` : $${parseInt(brk?.soldValue || 0)}` : ''
              }`}
        </Styled.ReportHeading>
        {renderDays(brk)}
        <br />
        {brk?.salesPerson &&
          Object?.entries(brk?.salesPerson)
            ?.sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
            ?.map(([key, value]: any) => (
              <SharedStyled.FlexBox justifyContent="space-between">
                <Styled.ReportHeading fontSize="14px">{key?.split(' ')[0]}:</Styled.ReportHeading>
                <Styled.ReportHeading fontSize="14px">
                  {value?.count || 0}{' '}
                  {chk?.name === 'Opportunity'
                    ? `: $${value?.perLeadSold}`
                    : `: ${checkValue(value?.percent, true)} ${
                        chk?.name === 'Sold' ? ` : $${parseInt(value?.sold || 0)}` : ''
                      }`}
                </Styled.ReportHeading>
              </SharedStyled.FlexBox>
            ))}
      </Styled.RateCard>
    )
  }

  return (
    <Styled.ConvReportContainer>
      {/* <Styled.ReportHeading className="title" fontSize="24px" textAlign="center">
        Conversion Rates
      </Styled.ReportHeading> */}
      {convReport?.map((convR: ConvReport, index: number) => (
        <Fragment key={index}>
          <Styled.ReportHeading className="title" fontSize="21px" textAlign="left">
            {convR?.name || '--'}
          </Styled.ReportHeading>
          <SharedStyled.FlexBox flexWrap="wrap" gap="10px" justifyContent="center" padding="10px">
            {convR?.checkpoints?.map((chk: Checkpoint, idx: number) => (
              <Fragment key={idx}>
                <Styled.RateCard
                  pointer="pointer"
                  scaleOnHover={true}
                  background={toggleConvCount[index + idx + chk?.name] ? '#f4f3f3' : 'none'}
                  onClick={() => toggleCount(index + idx + chk?.name)}
                >
                  <Styled.ReportHeading textAlign="center" fontSize="18px">
                    {chk?.name || <></>}
                  </Styled.ReportHeading>
                  <Styled.ReportHeading textAlign="center" fontSize="14px">
                    {checkValue(chk?.count)} :{' '}
                    {chk?.name === 'Opportunity'
                      ? `$${chk?.perLeadSold}`
                      : `${checkValue(chk?.percent, true)} ${
                          chk?.name === 'Sold' ? ` : $${parseInt(chk?.soldValue || 0)}` : ''
                        }`}
                  </Styled.ReportHeading>

                  <br />

                  {chk?.salesPerson &&
                    Object?.entries(chk?.salesPerson)
                      ?.sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
                      ?.map(([key, value]: any) => (
                        <SharedStyled.FlexBox justifyContent="space-between">
                          <Styled.ReportHeading fontSize="14px">{key?.split(' ')[0]}:</Styled.ReportHeading>
                          <Styled.ReportHeading fontSize="14px">
                            {value?.count || 0}{' '}
                            {chk?.name === 'Opportunity'
                              ? `: $${value?.perLeadSold}`
                              : `: ${checkValue(value?.percent, true)} ${
                                  chk?.name === 'Sold' ? ` : $${parseInt(value?.sold || 0)}` : ''
                                }`}
                          </Styled.ReportHeading>
                        </SharedStyled.FlexBox>
                      ))}
                </Styled.RateCard>
              </Fragment>
            ))}
          </SharedStyled.FlexBox>

          <SharedStyled.FlexBox flexWrap="wrap" gap="10px" justifyContent="center" padding="10px">
            {convR?.checkpoints
              ?.sort((a, b) => a?.sequence - b?.sequence)
              ?.map((chk: Checkpoint, idx: number) => (
                <Fragment key={idx}>
                  {toggleConvCount[index + idx + chk?.name] && (
                    <Styled.RateCard padding={'10px'} width="100%">
                      <SharedStyled.FlexBox flexDirection="column" gap="5px">
                        <Styled.ReportHeading textAlign="left" fontSize="18px">
                          {chk?.name || <></>}
                        </Styled.ReportHeading>
                        <SharedStyled.FlexBox gap="10px" flexWrap={'wrap'}>
                          {chk?.name === 'Sold' ? (
                            <>
                              <SharedStyled.FlexBox flexDirection="column" gap="5px">
                                <Styled.ReportHeading textDecoration="underline" textAlign="left" fontSize="18px">
                                  1st Appt
                                </Styled.ReportHeading>

                                <SharedStyled.FlexBox gap="10px" flexWrap={'wrap'}>
                                  {chk?.breakdown?.slice(0, 3).map((brk: any) => (
                                    <>{renderSubCards(chk, brk, idx, index)}</>
                                  ))}
                                </SharedStyled.FlexBox>
                              </SharedStyled.FlexBox>
                              <SharedStyled.FlexBox flexDirection="column" gap="5px">
                                <Styled.ReportHeading textDecoration="underline" textAlign="left" fontSize="18px">
                                  2nd Appt
                                </Styled.ReportHeading>

                                <SharedStyled.FlexBox gap="10px" flexWrap={'wrap'}>
                                  {chk?.breakdown?.slice(3).map((brk: any) => (
                                    <>{renderSubCards(chk, brk, idx, index)}</>
                                  ))}
                                </SharedStyled.FlexBox>
                              </SharedStyled.FlexBox>
                            </>
                          ) : (
                            chk?.breakdown?.map((brk: any) => <>{renderSubCards(chk, brk, idx, index)}</>)
                          )}
                        </SharedStyled.FlexBox>

                        {chk?.breakdown?.map(
                          (brk: any) =>
                            toggleConvCount[index + idx + chk?.name + brk?.name] && (
                              <>
                                <Styled.TableContainer>
                                  {brk?.oppList?.length ? (
                                    <>
                                      <Styled.ReportHeading textAlign="left" fontSize="14px">
                                        <b>{brk?.name}</b>
                                      </Styled.ReportHeading>

                                      <Styled.TableHeading column="repeat(auto-fit, minmax(80px, 1fr))">
                                        <Styled.TableTitle>CLIENT NAME</Styled.TableTitle>
                                        <Styled.TableTitle className="center-align">PO#</Styled.TableTitle>
                                        <>{renderDatesColumn(brk?.name)}</>
                                        <Styled.TableTitle className="center-align">SOURCE</Styled.TableTitle>
                                        <Styled.TableTitle className="center-align">CITY</Styled.TableTitle>
                                      </Styled.TableHeading>
                                    </>
                                  ) : (
                                    <></>
                                  )}
                                  {brk?.oppList?.map((oppId: any) => {
                                    const opp = convR.allOpps?.find((v) => v._id === oppId) || {}
                                    console.log({ opp, convReport }, brk?.name)
                                    const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                    return (
                                      <>
                                        <tr>
                                          <Styled.TableContent
                                            as={Link}
                                            to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                            key={opp?._id}
                                            selfGen={opp?.selfGen}
                                            pointer="pointer"
                                            column="repeat(auto-fit, minmax(80px, 1fr))"
                                          >
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.client}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.PO}-{opp?.num}
                                            </Styled.CrewReportTableContentLabel>
                                            <>{renderDates(opp, brk?.name)}</>
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.leadSource || '--'}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.city || '--'}
                                            </Styled.CrewReportTableContentLabel>
                                          </Styled.TableContent>
                                        </tr>
                                      </>
                                    )
                                  })}
                                </Styled.TableContainer>
                              </>
                            )
                        )}
                        {/* </Styled.Section> */}
                      </SharedStyled.FlexBox>
                    </Styled.RateCard>
                  )}
                </Fragment>
              ))}
          </SharedStyled.FlexBox>
        </Fragment>
      ))}
    </Styled.ConvReportContainer>
  )
}

export default ConversionRates
