import styled, { css, keyframes } from 'styled-components'
import { colors, screenSizes } from '../../../styles/theme'
import { Nue } from '../../../shared/helpers/constants'
import { ProductionReportSubHeading } from '../productionReport/style'

export const ReportContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  padding: 30px;
  place-items: start;
  .form {
    width: 100%;
    height: 100%;
  }
  @media (min-width: 2000px) {
    margin: 0 auto;
  }
`
export const SubHeading = styled.h5`
  font-size: 28px;
  text-align: center;
  font-weight: 500;
  margin: 0;
  @media (max-width: ${screenSizes.M}px) {
    font-size: 14px;
  }
`
export const ReportMainContainer = styled.div`
  /* ... */
`
export const ReportWrapper = styled.div`
  margin: 20px 0;

  &.gap {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
`

export const ReportSubHeading = styled.div`
  font-size: 16px;
  font-family: ${Nue.medium};
  text-align: start;
`
export const ReportContent = styled.div`
  font-size: 18px;
  font-weight: 500;
  color: #000;
`
export const TableContainer = styled.table`
  border: 1px solid ${colors.lightGray};
  width: 100%;
`

export const ReportLgSubHeading = styled(ReportSubHeading)`
  font-size: 16px;
  font-family: ${Nue.regular};
`

export const TableHeading = styled.tr<any>`
  display: grid;
  grid-template-columns: ${(props) => (props.column ? props.column : '2fr repeat(3, 1fr)')};
  padding: 5px 0;
  border: 1px solid #dddddd;
  color: ${colors.gray};
  font-family: ${Nue.medium};
  font-size: 11px;
  font-weight: 500;
  line-height: 20px;
  text-transform: uppercase;
  text-align: left;
  padding: 10px 12px;
  background: #f9f9f9;

  @media (min-width: ${screenSizes.S}px) {
    padding: 14px 24px;
  }
`
export const TableTitle = styled.th`
  font-size: 14px;
  font-family: ${Nue.medium};
  padding-right: 5px;
  &.right-align {
    text-align: right;
  }
  &.center-align {
    text-align: center;
  }

  &.no-wrap {
    white-space: nowrap;
  }
`
export const TableContent = styled.div<any>`
  display: grid;
  grid-template-columns: ${(props) => (props.column ? props.column : '2fr repeat(3, 1fr)')};
  cursor: ${(props) => props.pointer};
  padding: ${(props) => (props.padding ? props.padding : '8px 16px')};
  justify-items: ${(props) => props.justifyItems};
  /* color: ${colors.black}; */
  font-family: ${Nue.medium};
  font-size: 14px;
  border: 1px solid ${colors.lightGray};
  border-left: none;
  border-right: none;
  text-transform: capitalize;
  background-color: ${(props) => (props.type % 2 == 0 ? '#F2F2F2' : 'none')};
  border-bottom: ${(props) => (props.isOpen ? 'none' : 'inherit')};
  td {
    color: ${(props) => (props.selfGen ? '#2fac2f' : colors.black)};
  }

  @media (min-width: ${screenSizes.S}px) {
    padding: ${(props) => (props.padding ? props.padding : '10px 24px')};
  }

  &.right {
    text-align: center;
  }
  &.change-orders {
    td,
    a {
      color: brown;
    }
  }

  &.nested {
    border: none;
    td,
    a {
      color: brown;
      font-style: italic;
    }
  }
`
export const CrewReportTableContentLabel = styled.td<any>`
  /* ... */
  color: ${(props) => (props.colors ? `${props.colors} !important` : colors.black)};
  /* font-family: ${Nue.medium}; */
  font-size: 14px;
  padding-right: 5px;
  cursor: ${(props) => props.pointer};
  &:first-child {
    text-align: left;
  }
  &.right-align {
    text-align: right;
  }
`
export const ReportFlexBox = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
`
export const TableContainerMain = styled.table`
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
  background-color: transparent;

  th {
    text-align: left;
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    padding: 0.3rem;
  }

  td {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    padding: 0.3rem;
  }
`
export const ReportGridBox = styled.div<any>`
  display: grid;
  align-items: flex-start;
  justify-content: space-between;
  gap: 10px;
  grid-template-columns: ${(props) => (props.column ? props.column : '1fr 1fr')};
  @media (max-width: ${screenSizes.M}px) {
    grid-template-columns: 1fr;
  }
`
export const ConversionReportBox = styled.div<any>`
  display: grid;
  align-items: flex-start;
  justify-content: space-between;
  gap: ${(props) => (props.gap ? props.gap : '10px')};
  grid-template-columns: ${(props) => (props.column ? props.column : '1fr 1fr 1fr')};
  @media (max-width: ${screenSizes.M}px) {
    grid-template-columns: 1fr;
  }
`
export const KPIButtonContainer = styled.div`
  align-self: flex-end;
  button {
    height: 52px;
  }
  @media (max-width: ${screenSizes.M}px) {
    align-self: flex-start;
    button {
      height: 40px;
    }
  }
`

// Styled component for RateCard
export const RateCard = styled.div<any>`
  position: relative; /* Ensure z-index works correctly */
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: ${(props) => (props.padding ? props.padding : '20px')};
  background: ${(props) => props.background};
  width: ${(props) => (props.width ? props.width : '250px')};
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  cursor: ${(props) => props.pointer};

  transition: transform 0.2s ease-in-out; /* Smooth transition for scaling */
  ${(props) =>
    props.scaleOnHover &&
    css`
      &:hover {
        transform: scale(1.05);
      }
    `}
`
export const ReportHeading = styled.div<any>`
  font-size: ${(props) => (props.fontSize ? props.fontSize : '22px')};
  cursor: ${(props) => props.pointer};
  font-family: ${Nue.regular};
  text-align: ${(props) => (props.textAlign ? props.textAlign : 'left')};
  margin: ${(props) => props.margin};
  text-decoration: ${(props) => props.textDecoration};
  &.title {
    margin-bottom: 10px;
    font-family: ${Nue.medium};
  }
  &.margin {
    margin-bottom: 8px;
  }
  .cursor {
    cursor: pointer;
  }
`

export const Section = styled.div<any>`
  display: flex;
  align-items: center;
  justify-content: ${(props) => (props.justifyContent ? props.justifyContent : 'center')};
  width: ${(props) => props.width};
  padding: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
`
export const ConvReportContainer = styled.div`
  width: 100%;
  .color-1 {
  }
  .color-2 {
    color: red;
  }
  .color-3 {
    color: green;
  }
`
