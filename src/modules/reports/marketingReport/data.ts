export interface Root {
  leadSource: LeadSource[]
  opps: Opp[]
  campaign: Campaign[]
  channels: Channel[]
}

export interface LeadSource {
  _id: string
  name: string
  channelId: string
  actualCost?: ActualCost[]
  oppIds: string[]
  totalOpps: number
  totalCost: number
  totalBudget: number
  totalGrossProfit: number
  checkpointCounts: CheckpointCounts
  conversion: Conversion
}

export interface ActualCost {
  month: number
  year: number
  cost: number
}

export interface CheckpointCounts {
  'New Lead': number
  'New Opportunity': number
  'Needs Assessment': number
  Presentation: number
  Sale: number
}

export interface Conversion {
  'New Lead': NewLead
  'New Opportunity': NewOpportunity
  'Needs Assessment': NeedsAssessment
  Presentation: Presentation
}

export interface NewLead {
  'New Lead > New Opportunity': number
  'New Lead > Needs Assessment': number
  'New Lead > Presentation': number
  'New Lead > Sale': number
}

export interface NewOpportunity {
  'New Opportunity > Needs Assessment': number
  'New Opportunity > Presentation': number
  'New Opportunity > Sale': number
}

export interface NeedsAssessment {
  'Needs Assessment > Presentation': number
  'Needs Assessment > Sale': number
}

export interface Presentation {
  'Presentation > Sale': number
}

export interface Opp {
  _id: string
  oppType: string
  clientId: string
  saleDate: string
  PO: string
  acceptedType: string
  acceptedProjectDetails: string
  budget: Budget
  grossProfit: number
  newLeadDate: string
  oppDate: string
  needsAssessmentDate: string
  presentationDate: string
  leadSource: string
  client: string
  campaign?: string
}

export interface Budget {
  total: number
}

export interface Campaign {
  _id?: string
  campaignId?: string
  name?: string
  leadSourceId?: string
  actualCost?: ActualCost2[]
  oppIds: string[]
  totalOpps: number
  totalCost: number
  totalBudget: number
  totalGrossProfit: number
  checkpointCounts: CheckpointCounts2
  conversion: Conversion2
}

export interface ActualCost2 {
  month: number
  year: number
  cost: number
}

export interface CheckpointCounts2 {
  'New Lead': number
  'New Opportunity': number
  'Needs Assessment': number
  Presentation: number
  Sale: number
}

export interface Conversion2 {
  'New Lead': NewLead2
  'New Opportunity': NewOpportunity2
  'Needs Assessment': NeedsAssessment2
  Presentation: Presentation2
}

export interface NewLead2 {
  'New Lead > New Opportunity': number
  'New Lead > Needs Assessment': number
  'New Lead > Presentation': number
  'New Lead > Sale': number
}

export interface NewOpportunity2 {
  'New Opportunity > Needs Assessment': number
  'New Opportunity > Presentation': number
  'New Opportunity > Sale': number
}

export interface NeedsAssessment2 {
  'Needs Assessment > Presentation': number
  'Needs Assessment > Sale': number
}

export interface Presentation2 {
  'Presentation > Sale': number
}

export interface Channel {
  _id: string
  channelId: string
  oppIds: string[]
  totalOpps: number
  name: string
  createdBy: string
  deleted: boolean
  createdAt: string
  description: string
  order: number
  totalCost: number
  totalBudget: number
  totalGrossProfit: number
  checkpointCounts: CheckpointCounts3
  conversion: Conversion3
}

export interface CheckpointCounts3 {
  'New Lead': number
  'New Opportunity': number
  'Needs Assessment': number
  Presentation: number
  Sale: number
}

export interface Conversion3 {
  'New Lead': NewLead3
  'New Opportunity': NewOpportunity3
  'Needs Assessment': NeedsAssessment3
  Presentation: Presentation3
}

export interface NewLead3 {
  'New Lead > New Opportunity': number
  'New Lead > Needs Assessment': number
  'New Lead > Presentation': number
  'New Lead > Sale': number
}

export interface NewOpportunity3 {
  'New Opportunity > Needs Assessment': number
  'New Opportunity > Presentation': number
  'New Opportunity > Sale': number
}

export interface NeedsAssessment3 {
  'Needs Assessment > Presentation': number
  'Needs Assessment > Sale': number
}

export interface Presentation3 {
  'Presentation > Sale': number
}
