import styled from 'styled-components'
import { Nue } from '../../../../shared/helpers/constants'
import { colors } from '../../../../styles/theme'

export const Group = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`

export const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-family: ${Nue.regular};
  font-size: 16px;
`

export const RadioInput = styled.input.attrs({ type: 'radio' })`
  accent-color: black;
  width: 16px;
  height: 16px;
`

export const Table = styled.table`
  border-collapse: collapse;
  width: 100%;
  font-size: 14px;
`

export const Th = styled.th`
  text-align: left;
  padding: 10px 0px;
  font-family: ${Nue.medium};
  cursor: pointer;
  color: ${colors.black};
`

export const Td = styled.td`
  padding: 10px 0px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  a {
    font-family: ${Nue.regular};
  }
`

export const Link = styled.span`
  color: ${colors.black};
  cursor: pointer;
`

export const ThContent = styled.div`
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
`

export const SortIndicator = styled.span<{ visible: boolean }>`
  visibility: ${(props) => (props.visible ? 'visible' : 'hidden')};

  display: inline-block;
  width: 14px;
  text-align: center;
`
