import React, { useState } from 'react'
import styled from 'styled-components'
import * as Styled from './style'
import { TooltipContainer } from '../../../../styles/styled'
import { dayjsFormat, formatCurrencyNumber, formatDollarAmount, getEnumValue } from '../../../../shared/helpers/util'
import { TooltipPortal } from '../../../../shared/components/tooltip'
import { Link } from 'react-router-dom'

// Column definition type
export interface Column {
  label: string
  key: string
  tooltip?: boolean
  link?: boolean
  tooltipContent?: string
  isDate?: boolean
  isCurrency?: boolean
  isContact?: boolean
}

// Props interface
interface SortableTableProps {
  data: Record<string, any>[]
  columns: Column[]
  onLinkClick?: (value: any, key: string) => void
  defaultSort?: { key: string; direction: 'asc' | 'desc' }
  className?: string
}

const SortableTable: React.FC<SortableTableProps> = ({
  data = [],
  columns = [],
  onLinkClick = (value) => alert(`Show report for ${value}`),
  defaultSort = { key: null, direction: 'asc' },
  className,
}) => {
  const [sortConfig, setSortConfig] = useState(defaultSort)
  const [test, setTest] = useState<any>()

  const sortedData = [...data].sort((a, b) => {
    if (!sortConfig.key) return 0
    const aVal = a[sortConfig.key]
    const bVal = b[sortConfig.key]
    if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1
    if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (key: string) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc',
    }))
  }

  const handleCellHover = (event: React.MouseEvent<HTMLTableCellElement>, rowIdx: number, colIdx: number) => {
    const rect = event.currentTarget.getBoundingClientRect()

    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop

    setTest({
      left: rect.left + scrollLeft,
      top: rect.top + scrollTop,
      rowIdx,
      colIdx,
    })
  }

  return (
    <Styled.Table className={className}>
      <thead>
        <tr>
          {columns.map((col) => (
            <Styled.Th key={col.key} onClick={() => handleSort(col.key)}>
              <Styled.ThContent>
                {col.label}
                <Styled.SortIndicator visible={sortConfig.key === col.key}>
                  {sortConfig.key === col.key && sortConfig.direction === 'asc' ? <>&#129033;</> : <>&#129035;</>}
                </Styled.SortIndicator>
              </Styled.ThContent>
            </Styled.Th>
          ))}
        </tr>
      </thead>
      <tbody>
        {sortedData.map((row, rowIdx) => (
          <tr key={rowIdx}>
            {columns.map((col, colIdx) => (
              <Styled.Td
                key={col.key}
                onMouseEnter={(e) => handleCellHover(e, rowIdx, colIdx)}
                onMouseLeave={() => setTest(null)}
              >
                <>
                  {col.link ? (
                    <Styled.Link
                      style={{
                        pointerEvents: row[col.key] ? 'auto' : 'none',
                      }}
                      onClick={() => onLinkClick(row[col.key], col.key)}
                    >
                      {row[col.key] || '--'}
                    </Styled.Link>
                  ) : col?.isDate ? (
                    <TooltipPortal
                      content={dayjsFormat(row[col.key], 'M/D/YY')}
                      position="top"
                      isTableTooltip={test?.rowIdx === rowIdx && test?.colIdx === colIdx && row[col.key]}
                      customStyle={{ top: test?.top - 25, left: test?.left - 25 }}
                    >
                      <>{row[col.key] ? dayjsFormat(row[col.key], 'M/D') : '--'}</>
                    </TooltipPortal>
                  ) : col?.isCurrency ? (
                    <TooltipPortal
                      content={formatDollarAmount(row[col.key])}
                      position="top"
                      isTableTooltip={test?.rowIdx === rowIdx && test?.colIdx === colIdx && row[col.key]}
                      customStyle={{ top: test?.top - 25, left: test?.left - 25 }}
                    >
                      {row[col.key] ? <>${formatCurrencyNumber(row[col.key])}</> : <>--</>}
                    </TooltipPortal>
                  ) : col?.isContact ? (
                    <TooltipPortal
                      content={row?.contactName}
                      position="top"
                      isTableTooltip={test?.rowIdx === rowIdx && test?.colIdx === colIdx && row[col.key]}
                      customStyle={{ top: test?.top - 25, left: test?.left - 25, transform: 'translateX(25%)' }}
                    >
                      <Link
                        to={
                          row?.stageGroup
                            ? `/${getEnumValue(row?.stageGroup)}/opportunity/${row?.oppId}`
                            : `/contact/profile/${row?.contactId}`
                        }
                        target="_blank"
                      >
                        {row[col.key]}
                      </Link>
                    </TooltipPortal>
                  ) : (
                    row[col.key] || '--'
                  )}
                </>
              </Styled.Td>
            ))}
          </tr>
        ))}
      </tbody>
    </Styled.Table>
  )
}

export default SortableTable
