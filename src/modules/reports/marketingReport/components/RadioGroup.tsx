import { Group, Label, RadioInput } from './style'

const RadioGroup = ({
  name,
  options,
  selected,
  onChange,
}: {
  name: string
  options: string[]
  selected: string
  onChange: (value: string) => void
}) => (
  <Group>
    {options.map((option) => (
      <Label key={option}>
        <RadioInput name={name} value={option} checked={selected === option} onChange={() => onChange(option)} />
        {option}
      </Label>
    ))}
  </Group>
)

export default RadioGroup
