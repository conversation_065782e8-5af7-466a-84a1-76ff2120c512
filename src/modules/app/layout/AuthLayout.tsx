import { Navigate, Outlet, useLocation, useParams } from 'react-router-dom'
import { Suspense, useEffect } from 'react'

import * as Styled from './style'
import { getDataFromLocalStorage } from '../../../shared/helpers/util'
import { StorageKey } from '../../../shared/helpers/constants'
import { profilePath } from '../../../logic/paths'

const AuthLayout = () => {
  const token = getDataFromLocalStorage(StorageKey.token)
  const { pathname } = useLocation()
  const regex = /\/media\/[^\/]+/

  if (token && pathname !== '/plans' && !regex.test(pathname)) {
    return <Navigate to={profilePath} />
  }

  useEffect(() => {
    if (pathname) {
      document.title = 'Piece Work Pro'
    }
  }, [pathname])
  return (
    <Styled.AuthLayoutContainer className={pathname === '/plans' || regex.test(pathname) ? 'plans' : ''}>
      <Styled.ChildrenContainer>
        <Suspense fallback={<></>}>
          <Outlet />
        </Suspense>
      </Styled.ChildrenContainer>
    </Styled.AuthLayoutContainer>
  )
}

export default AuthLayout
