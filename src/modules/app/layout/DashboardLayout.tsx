import { useEffect, Suspense } from 'react'
import { Navigate, Outlet, useLocation } from 'react-router-dom'

import * as Styled from './style'

import { signinPath } from '../../../logic/paths'
import { useAppDispatch, useAppSelector } from '../../../logic/redux/reduxHook'
import Sidebar from '../Newsidebar/Sidebar'
import Navbar from '../navbar/Navbar'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import { CompanyCreationForm } from '../../company/components/companyCreationForm/CompanyCreationForm'
import { setShowAddOptions, setShowCompanyCreationModal } from '../../../logic/redux/actions/ui'
import AddOptionsModal from '../navbar/components/AddOptionsModal'
import { FullpageLoader } from '../../../shared/components/loader/Loader'
import { StorageKey } from '../../../shared/helpers/constants'
import { getDataFromLocalStorage } from '../../../shared/helpers/util'

const DashboardLayout = () => {
  const token = localStorage.getItem(StorageKey.token)
  const dispatch = useAppDispatch()
  const { pathname } = useLocation()

  const companyShortName = getDataFromLocalStorage(StorageKey.companyName)
  useEffect(() => {
    if (companyShortName) {
      document.title = companyShortName
    }
  }, [pathname, companyShortName])

  const { navCollapsed, showMobileSideNav, showAddOptions, showCompanyCreationForm } = useAppSelector(
    (state) => state.ui
  )

  useEffect(() => {
    window?.scrollTo(0, 0)
  }, [])

  if (!token) {
    return <Navigate to={signinPath} />
  }
  return (
    <Styled.StyledRoutesWrapper navCollapsed={navCollapsed}>
      <Styled.SideNavCont navCollapsed={navCollapsed} showMobileSideNav={showMobileSideNav}>
        <Sidebar />
      </Styled.SideNavCont>
      <Styled.StyledRoutes>
        <Navbar />

        <Styled.ChildContainer>
          <Suspense fallback={<FullpageLoader />}>
            <Outlet />
          </Suspense>
        </Styled.ChildContainer>
      </Styled.StyledRoutes>

      <CustomModal show={showCompanyCreationForm} styles={{ zIndex: '101' }}>
        <CompanyCreationForm isGlobal />
      </CustomModal>

      <CustomModal show={showAddOptions} styles={{ zIndex: '101' }}>
        <AddOptionsModal
          setShowModal={() => {
            dispatch(setShowAddOptions(false))
          }}
        />
      </CustomModal>
    </Styled.StyledRoutesWrapper>
  )
}

export default DashboardLayout
