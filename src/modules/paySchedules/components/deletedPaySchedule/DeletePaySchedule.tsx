import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { Table } from '../../../../shared/table/Table'
import { useCallback, useMemo, useRef, useState } from 'react'
import { getPaySchedule } from '../../../../logic/apis/paySchedule'
import { useParams } from 'react-router-dom'
import { getDataFromLocalStorage, notify } from '../../../../shared/helpers/util'
import { PERIOD_OBJ1, StorageKey } from '../../../../shared/helpers/constants'

const DeletePaySchedule = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<any>([])
  const fetchIdRef = useRef(0)

  const fetchData = useCallback(async ({ pageSize, pageIndex }: any) => {
    try {
      // This will get called when the table needs new data

      let receivedData: any = []

      let currentCompanyData: any = localStorage.getItem('currentCompany')

      const statusResponse = await getPaySchedule({ deleted: true })

      if (statusResponse?.data?.statusCode === 200) {
        let statusRes = statusResponse?.data?.data?.paySchedule
        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            name: res.name,
            howOftenToPay: PERIOD_OBJ1[res.period],
          })
        })
      } else {
        notify(statusResponse?.data?.message, 'error')
      }

      // Give this fetch an ID
      const fetchId = ++fetchIdRef.current

      // Set the loading state
      setLoading(true)

      // We'll even set a delay to simulate a server here
      setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          setPageCount(Math.ceil(receivedData.length / pageSize))
          setLoading(false)
        }
      }, 1000)
    } catch (error) {
      console.error('PaySchedule fetchData error', error)
    }
  }, [])

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
      },
      {
        Header: 'How often to pay',
        accessor: 'howOftenToPay',
      },
    ],
    []
  )

  return (
    <Styled.DeletePayScheduleContainer>
      <SharedStyled.ContentHeader textAlign="left">Delete Pay Schedule</SharedStyled.ContentHeader>
      <SharedStyled.HorizontalDivider />
      <Table columns={columns} data={data} loading={loading} pageCount={pageCount} fetchData={fetchData} />
    </Styled.DeletePayScheduleContainer>
  )
}

export default DeletePaySchedule
