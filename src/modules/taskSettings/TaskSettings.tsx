import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import { EditIcon } from '../../assets/icons/EditIcon'
import { getDepartments } from '../../logic/apis/department'
import { getPosition } from '../../logic/apis/position'
import { getTasks, updateWorkTaskSequence } from '../../logic/apis/task'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { ConfirmationPopUp } from './components/confirmationPopup/ConfirmationPopUp'
import { CreateTaskPopUp } from './components/createTaskPopUp/CreateTaskPopUp'
import { EditTaskPopUp } from './components/editTaskPopUp/EditTaskPopUp'
import * as Styled from './style'
import { ButtonCont, SettingsCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import TabBar from '../../shared/components/tabBar/TabBar'
import DeletedTaskSettings from './components/deletedTaskSettings/DeletedTaskSettings'
import { DraggableTable } from '../../shared/table/DraggableTable'
import { StorageKey } from '../../shared/helpers/constants'

const TaskSettings = () => {
  interface I_Data {
    taskName: string
    description: string
  }

  const [loading, setLoading] = useState<boolean>(false)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [showCreateTaskPopUp, setShowCreateTaskPopUp] = useState<boolean>(false)
  const [showEditTaskPopUp, setShowEditTaskPopUp] = useState<boolean>(false)
  const [taskData, setTaskData] = useState<any>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const [dataForSequence, setDataForSequence] = useState<any[]>([])
  const fetchIdRef = useRef(0)

  const loadmoreRef = useRef(null)
  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        setLoading(true)
        // This will get called when the table needs new data

        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')
        let posPermissionArray = []

        const apiCalls = [
          getPosition({ deleted: false, skip: pageIndex, limit: pageSize }, false),
          getTasks({ skip: pageIndex, limit: pageSize }, false),
        ]
        const [positionResponse, taskResponse] = await Promise.all(apiCalls)

        if (taskResponse?.data?.statusCode === 200 && positionResponse?.data?.statusCode === 200) {
          let statusRes = taskResponse?.data?.data?.workTask
          let statusPosRes = taskResponse?.data?.data?.position
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              taskName: res?.name,
              description: res?.description || '-',
              action: (
                <>
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    <Styled.IconContainer
                      className="delete"
                      onClick={() => {
                        setTaskData({
                          taskName: res?.name,
                          description: res?.description,
                          rate: res?.rate,
                          id: res?._id,
                        })
                        setShowConfirmationPopUp(true)
                      }}
                    >
                      <DeleteIcon />
                    </Styled.IconContainer>
                    <Styled.IconContainer
                      className="edit"
                      onClick={() => {
                        setTaskData({
                          taskName: res?.name,
                          description: res?.description,
                          rate: res?.rate,
                          id: res?._id,
                          position: res?.position,
                          pieceWork: res?.pieceWork,
                          showOnScoreboard: res?.showOnScoreboard,
                          addTravel: res?.addTravel,
                        })
                        setShowEditTaskPopUp(true)
                      }}
                    >
                      <EditIcon />
                    </Styled.IconContainer>
                  </SharedStyled.FlexBox>
                </>
              ),
            })
          })
          setDataForSequence(statusRes?.map((v: any, index: number) => ({ _id: v._id, sequence: index + 1 })))
        } else {
          notify(taskResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Task Name',
        accessor: 'taskName',
      },
      {
        Header: 'Description',
        accessor: 'description',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  const updatedSequenceToApi = async (data: any[]): Promise<void> => {
    try {
      const response = await updateWorkTaskSequence(data)
      if (isSuccess(response)) {
        notify('Sequence Updated', 'success')
      } else {
        notify('Sequence Failed', 'success')
      }
    } catch (error) {
      console.error({ error })
    }
  }

  return (
    <SettingsCont gap="24px">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Work Task Settings</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setShowCreateTaskPopUp(true)
            }}
          >
            Add New Task
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <DraggableTable
                    columns={columns}
                    data={data}
                    loading
                    fetchData={fetchData}
                    noLink={true}
                    ref={loadmoreRef}
                    disabledSearch={true}
                    isLoadMoreLoading={loading}
                    draggable={true}
                    setDragItemLocal={setData}
                    setDataForSequence={setDataForSequence}
                    sendUpdatedSequenceToApi={updatedSequenceToApi}
                  />
                ),
              },
              {
                title: 'Inactive',
                render: () => <DeletedTaskSettings />,
              },
            ]}
            filterComponent={<></>}
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={showConfirmationPopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Delete Task"
          taskData={taskData}
        />
      </CustomModal>
      <CustomModal show={showCreateTaskPopUp}>
        <CreateTaskPopUp setShowCreateTaskPopUp={setShowCreateTaskPopUp} setDetailsUpdate={setDetailsUpdate} />
      </CustomModal>
      <CustomModal show={showEditTaskPopUp}>
        <EditTaskPopUp
          setShowEditTaskPopUp={setShowEditTaskPopUp}
          setDetailsUpdate={setDetailsUpdate}
          taskData={taskData}
        />
      </CustomModal>
    </SettingsCont>
  )
}

export default TaskSettings
