import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { Table } from '../../../../shared/table/Table'
import { useCallback, useMemo, useRef, useState } from 'react'
import { colors } from '../../../../styles/theme'
import { useNavigate, useParams } from 'react-router-dom'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { ConfirmationPopUp } from '../confirmationPopup/ConfirmationPopUp'
import { RevokeIcon } from '../../../../assets/icons/RevokeIcon'
import { getDepartments } from '../../../../logic/apis/department'
import { getDataFromLocalStorage, notify } from '../../../../shared/helpers/util'
import { getTasks } from '../../../../logic/apis/task'
import { StorageKey } from '../../../../shared/helpers/constants'

const DeletedTaskSettings = () => {
  interface I_Data {
    taskName: string
    description: string
  }

  const [loading, setLoading] = useState<boolean>(false)

  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)

  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [taskData, setTaskData] = useState<any>({})
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)

  const loadmoreRef = useRef(null)
  const navigate = useNavigate()

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        setLoading(true)
        // This will get called when the table needs new data

        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const taskResponse = await getTasks({ skip: pageIndex, limit: pageSize }, true)

        if (taskResponse?.data?.statusCode === 200) {
          let statusRes = taskResponse?.data?.data?.workTask
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              taskName: res?.name,
              description: res?.description || '-',
              action: (
                <>
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    <Styled.IconContainer
                      content="Revoke"
                      className="restore"
                      onClick={() => {
                        setTaskData({ taskName: res?.name, description: res?.description, id: res?._id })
                        setShowConfirmationPopUp(true)
                      }}
                    >
                      <RevokeIcon />
                    </Styled.IconContainer>
                  </SharedStyled.FlexBox>
                </>
              ),
            })
          })
        } else {
          notify(taskResponse?.data?.message, 'error')
        }
        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Task Name',
        accessor: 'taskName',
      },
      {
        Header: 'Description',
        accessor: 'description',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  return (
    <>
      <Table
        columns={columns}
        data={data}
        loading={loading}
        // pageCount={pageCount}
        fetchData={fetchData}
        noLink={true}
        ref={loadmoreRef}
        isLoadMoreLoading={loading}
      />

      <CustomModal show={showConfirmationPopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Restore Task"
          taskData={taskData}
        />
      </CustomModal>
    </>
  )
}

export default DeletedTaskSettings
