import { Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import * as Yup from 'yup'

import AvtSvg from '../../../assets/newIcons/avatar.svg'

import { authenticate, signin } from '../../../logic/apis/auth'
import { getAllCompanies } from '../../../logic/apis/company'
import { getInvitationDetails } from '../../../logic/apis/invitation'
import { companyPath, forgotPasswordPath, profilePath, signupPath } from '../../../logic/paths'
import { setIsLoggedIn } from '../../../logic/redux/actions/auth'
import { setIsInvited } from '../../../logic/redux/actions/invitation'
import { isPasswordValid } from '../../../shared/helpers/regex'
import { notify } from '../../../shared/helpers/util'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../styles/styled'
import { colors } from '../../../styles/theme'
import * as Styled from './style'
import { ModalHeader } from '../../taxJurisdiction/taxJurisdictionModal/style'
import useWindowDimensions from '../../../shared/hooks/useWindowDimensions'
import { nhrLogoSvg } from '../../../shared/helpers/images'
import Button from '../../../shared/components/button/Button'
import '../../../shared/helpers/yupExtension'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  email: string
  password: string
}

/**
 *
 * @returns A SignIn component with all the validations to its input fields
 */
const Signin = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    email: '',
    password: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const { isMobile } = useWindowDimensions()

  const [rejectloading, setRejectLoading] = useState<boolean>(false)

  const [isInvitedValue, setIsInvitedValue] = useState<boolean>(false)

  const [data, setData] = useState<any>({})

  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { state } = useLocation()

  /**
   * SignInSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const SignInSchema = Yup.object().shape({
    email: Yup.string().trimEmail().email('Invalid email').required('Please provide your email'),
    password: Yup.string().required('Please provide your password'),
    // .matches(
    //   isPasswordValid,
    //   'Password should be minimum eight characters, at least one uppercase letter, one lowercase letter, one number and one special character'
    // ),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues) => {
    try {
      setLoading(true)
      if (state !== null && state?.invitationId) {
        let isAccepted = await onAcceptingInvite()
        if (!isAccepted) {
          return -1
        }
      }
      submittedValues.email = submittedValues.email.trim()
      const response = await signin(submittedValues)

      if (response?.data?.statusCode === 200) {
        localStorage.setItem('roleType', response?.data?.data?.user?.role)
        authenticate(
          response?.data?.data.access_token,
          response?.data?.data.refresh_token,
          response?.data?.data?.user.exp,
          response?.data?.data?.user?._id
        )
        await getCompanies(response?.data?.data?.user?._id)
        let inviteValue: any = localStorage.getItem('isInvited')
        // if (JSON.parse(inviteValue)) {
        //   navigate(profilePath)
        // } else {
        //   navigate(companyPath)
        // }
        navigate(profilePath)
        dispatch(setIsLoggedIn(true))
        setLoading(false)
        if (state !== null && state?.invitationId) {
          notify('Invitation accepted and Signed in successfully', 'success')
        } else {
          notify('Signed in successfully', 'success')
        }
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
      }
    } catch (error) {
      console.error('SignIn handleSubmit error', error)
      setLoading(false)
    }
  }

  const getCompanies = async () => {
    let breakException = {}
    try {
      const response = await getAllCompanies()
      if (response?.data?.statusCode === 200) {
        const allCompanies = response?.data?.data?.companiesData
        allCompanies.forEach((company: any) => {
          if (company.member.invited) {
            dispatch(setIsInvited(true))
            setIsInvitedValue(true)
            localStorage.setItem('isInvited', JSON.stringify(true))
            throw breakException
          }
        })
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      if (error !== breakException) {
        console.error('getCompanies error', error)
      }
    }
  }

  const onAcceptingInvite = async () => {
    try {
      const id: any = state?.invitationId
      setLoading(true)
      const response = await acceptRejectInvitation({ ...data, status: 2 })
      if (response?.data?.statusCode === 200) {
        return true
        // notify('Accepted invitation successfully', 'success')
        // setLoading(false)
      } else {
        notify(response?.data?.message, 'error')
        setLoading(false)
        return false
      }
    } catch (error) {
      console.error('onAcceptingInvite error', error)
      setLoading(false)
      return false
    }
  }

  const onRejectingInvite = async (resetForm: any) => {
    try {
      setRejectLoading(true)
      const response = await acceptRejectInvitation({ ...data, status: 3 })
      if (response?.data?.statusCode === 200) {
        notify('Invitation rejected successfully', 'success')
        setRejectLoading(false)
        resetForm()
        navigate(`/signin`, { replace: true })
      } else {
        notify(response?.data?.message, 'error')
        setRejectLoading(false)
      }
    } catch (error) {
      console.error('onDecliningInvite error', error)
      setRejectLoading(false)
    }
  }

  const getInviteDetail = async () => {
    try {
      let id: any = state?.invitationId
      const response = await getInvitationDetails(id)
      if (response?.data?.statusCode === 200) {
        let res = response?.data?.data?.invitation
        setData({
          senderEmail: res.senderEmail,
          recipientEmail: res.email,
          company: res.company,
          status: res.status,
        })
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getInviteDetail error', error)
    }
  }

  const getInvitationDetail = async () => {
    if (state !== null && state?.invitationId) {
      try {
        let id: any = state.invitationId
        const response = await getInvitationDetails(id)
        if (response?.data?.statusCode === 200) {
          let res = response?.data?.data?.invitation
          setInitialValues({ ...initialValues, email: res.email })
        } else {
          notify(response?.data?.message, 'error')
        }
      } catch (error) {
        console.error('getInviteDetail error', error)
      }
    }
  }

  useEffect(() => {
    getInvitationDetail()
  }, [])

  useEffect(() => {
    getInviteDetail()
  }, [])

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={handleSubmit}
      validationSchema={SignInSchema}
      validateOnChange={true}
      validateOnBlur={false}
      enableReinitialize={true}
    >
      {({ values, errors, touched, resetForm }) => {
        return (
          <Styled.SigninContainer>
            <SharedStyled.FlexCol alignItems="center" gap="30px">
              <Styled.Logo
                src={'https://pieceworkpro.s3.us-west-1.amazonaws.com/Admin/pieceworkpro.svg'}
                alt="nhr logo"
              />

              <SharedStyled.SettingModalContentContainer padding={isMobile ? '0' : ''}>
                <Form className="form">
                  <SharedStyled.Content maxWidth="500px" width="100%" borderRadius="8px">
                    <SharedStyled.ContentHeader>
                      <Styled.HeaderCont>
                        <img src={AvtSvg} alt="modal icon" />
                        <SharedStyled.FlexCol>
                          <ModalHeader>Welcome Back!</ModalHeader>
                        </SharedStyled.FlexCol>
                      </Styled.HeaderCont>
                    </SharedStyled.ContentHeader>
                    <SharedStyled.FlexCol gap="8px">
                      <InputWithValidation
                        labelName="Email"
                        stateName="email"
                        error={touched.email && errors.email ? true : false}
                      />
                      <InputWithValidation
                        type="password"
                        labelName="Password"
                        stateName="password"
                        value={values.password}
                        error={touched.password && errors.password ? true : false}
                        showValidationCard={false}
                      />
                    </SharedStyled.FlexCol>
                    {!state?.invitationId ? (
                      <SharedStyled.ButtonContainer marginTop="24px">
                        <Button type="submit" isLoading={loading}>
                          Sign In
                        </Button>
                      </SharedStyled.ButtonContainer>
                    ) : (
                      <SharedStyled.ButtonContainer marginTop="20px">
                        <Button type="submit" isLoading={loading}>
                          Signin to Accept
                        </Button>
                        <Button
                          className="delete"
                          type="button"
                          color={colors.white}
                          bgColor={colors.error}
                          onClick={() => onRejectingInvite(resetForm)}
                          isLoading={rejectloading}
                        >
                          Reject Invitation
                        </Button>
                      </SharedStyled.ButtonContainer>
                    )}
                    <Styled.OtherOptionsDiv marginTop="16px">
                      <Styled.OtherOptionsText>
                        Don’t have an account? <span onClick={() => navigate(signupPath)}>Register Company</span>
                      </Styled.OtherOptionsText>
                      <Styled.OtherOptionsText>
                        <span onClick={() => navigate(forgotPasswordPath)}>Forgot Password?</span>
                      </Styled.OtherOptionsText>
                    </Styled.OtherOptionsDiv>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </SharedStyled.FlexCol>
          </Styled.SigninContainer>
        )
      }}
    </Formik>
  )
}

export default Signin
