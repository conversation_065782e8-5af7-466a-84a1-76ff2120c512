import { ceilToDecimalPlaces, getAsbTest, getPermit, paymentMethodEnum, roundTo2, SignContractDto } from './constant'

const getOrderTasks = (
  upgradeSelectedPackage: any,
  upgradeOptions: any[],
  chosenUpgrades: string[],
  removeTaskArray: string[],
  projectTasks: any[]
) => {
  //taskArray
  const orderTasks: any[] = []

  // Filter out tasks that need to be removed
  const remainingTasks = upgradeSelectedPackage.taskArray.filter((taskId) => !removeTaskArray.includes(taskId))

  // Filter taskPrices based on remaining tasks
  const remainingTaskPrices = upgradeSelectedPackage.taskPrices.filter(({ taskId }) => remainingTasks.includes(taskId))

  // Collect taskPrices from chosen upgrades
  let chosenTaskPrices = []
  chosenUpgrades.forEach((upgradeId) => {
    const upgradeOption = upgradeOptions.find((opt) => opt._id === upgradeId)
    if (upgradeOption) {
      chosenTaskPrices = chosenTaskPrices.concat(
        upgradeOption.taskPrices.filter(({ taskId }) => upgradeOption.taskArray.includes(taskId))
      )
    }
  })

  // Combine and remove duplicate taskPrices based on taskId
  const combinedTaskPrices = [...remainingTaskPrices, ...chosenTaskPrices]
  const uniqueTaskPrices = Array.from(new Map(combinedTaskPrices.map((item) => [item.taskId, item])).values())

  // Apply filtered taskPrices to projectTasks
  uniqueTaskPrices.forEach(({ taskId, taskPrice }) => {
    const projectTask = projectTasks.find((task) => task._id === taskId)
    if (projectTask) {
      projectTask.price = taskPrice
      orderTasks.push(projectTask)
    }
  })

  return orderTasks
}

const initializePackageOptionFields = (option: any) => {
  Object.assign(option, {
    grandTotal: 0,
    cashTotal: 0,
    financeTotal: 0,
    jobCost: 0,
    lBurden: 0,
    lSubtotal: 0,
    lTotal: 0,
    laborCost: 0,
    mMarkup: 0,
    mTotal: 0,
    matCost: 0,
    matTax: 0,
    overhead: 0,
    profit: 0,
    commission: 0,
    travelFee: 0,
    upsell: 0,
  })
}

const calculateAndUpdatePackageAndOption = (
  priceTotal: any,
  task: any,
  variables: any,
  price: any,
  markup: number,
  upsellValue: number,
  isAdding: any
) => {
  // multiplier is used for addition or subtraction
  const multiplier = isAdding ? 1 : -1
  const taskPrice = createPrice(
    variables,
    task.matCost,
    task.labCost,
    price?.duration,
    price.state,
    price.tax.materialTax,
    markup,
    upsellValue
  )

  const {
    overhead,
    profit,
    grandTotal,
    cashTotal,
    financeTotal,
    jobCost,
    lBurden,
    lSubtotal,
    lTotal,
    laborCost,
    mMarkup,
    mTotal,
    matCost,
    matTax,
    commission,
    travelFee,
    upsell,
  } = taskPrice

  priceTotal.cashTotal += cashTotal * multiplier
  priceTotal.financeTotal += financeTotal * multiplier
  priceTotal.jobCost += jobCost * multiplier
  priceTotal.lBurden += lBurden * multiplier
  priceTotal.lSubtotal += lSubtotal * multiplier
  priceTotal.lTotal += lTotal * multiplier
  priceTotal.laborCost += laborCost * multiplier
  priceTotal.mMarkup += mMarkup * multiplier
  priceTotal.mTotal += mTotal * multiplier
  priceTotal.matCost += matCost * multiplier
  priceTotal.matTax += matTax * multiplier
  priceTotal.overhead += overhead * multiplier
  priceTotal.profit += profit * multiplier
  priceTotal.commission += commission * multiplier
  priceTotal.travelFee += travelFee * multiplier
  priceTotal.upsell += upsell * multiplier
  priceTotal.grandTotal += grandTotal * multiplier

  return taskPrice
}

// NOTE Ques: for create price in frontend we need to save some opp data in price
// so if it change nothing change on price like distance & duration
// markup & upsell is directly calculated in prices
const createPrice = (
  variables: any,
  matCost: number,
  laborCost: number,
  duration: number,
  state: string,
  materialTax: number,
  projectMarkup: number,
  pkgUpsell: number
) => {
  try {
    const { ttlBurden, matMarkup, modOH, modP, modS, financeMod, commission, travelHrlyRate } = variables
    // Calculate material costs
    const matTax = (materialTax * matCost) / 100 ?? 0
    const mMarkup = (matCost + matTax) * matMarkup
    const mTotal = matCost + matTax + mMarkup
    // Calculate travel fees (1 round trip each 8 hrs)
    const driveTime = Number(duration) || 0
    const tripFee = (driveTime / 60) * travelHrlyRate * 2
    const days = laborCost / 32 / 8 // TO DO: Replace 32 w/ dynamic crew wage
    const travelFeeCalc = days * tripFee
    // Calculate labor and burden
    const lSubtotal = laborCost + travelFeeCalc
    const lBurden = lSubtotal * ttlBurden
    const lTotal = lSubtotal + lBurden
    const jobCost = mTotal + lTotal
    const overhead = lSubtotal * modOH * (1 + projectMarkup)
    const profit = lSubtotal * modP * (1 + projectMarkup)
    const upsell = (overhead + profit) * pkgUpsell
    const cashTotal = jobCost + overhead + profit + upsell
    const financeTotal = cashTotal / (1 - financeMod)
    const financeModCalc = financeTotal - cashTotal
    const grandTotal = financeTotal / (1 - commission)
    const commissionCalc = grandTotal - financeTotal
    const price: any = {
      matCost: roundTo2(matCost * 100),
      matTax: roundTo2(matTax * 100),
      mMarkup: roundTo2(mMarkup * 100),
      mTotal: roundTo2(mTotal * 100),
      laborCost: roundTo2(laborCost * 100),
      travelFee: roundTo2(travelFeeCalc * 100),
      lSubtotal: roundTo2(lSubtotal * 100),
      lBurden: roundTo2(lBurden * 100),
      lTotal: roundTo2(lTotal * 100),
      jobCost: roundTo2(jobCost * 100),
      overhead: roundTo2(overhead * 100),
      profit: roundTo2(profit * 100),
      upsell: roundTo2(upsell * 100),
      cashTotal: roundTo2(cashTotal * 100),
      financeMod: roundTo2(financeModCalc * 100),
      financeTotal: roundTo2(financeTotal * 100),
      commission: roundTo2(commissionCalc * 100),
      grandTotal: roundTo2(grandTotal * 100),
    }
    return price
  } catch (e) {
    console.error(e)
  }
}

//gets options calculation
export const upgradeOptions = (price: any, selectedPackage: any, packageId: any) => {
  try {
    const variables = price?.variables
    const upgradeOptions = price?.options?.filter((option: { packagesId: string | any[] }) =>
      option?.packagesId?.includes(packageId)
    )
    const tasks = price?.tasks
    const markup = price?.projectType?.markup || 0

    for (const option of upgradeOptions ?? []) {
      option.priceTotal = {}
      initializePackageOptionFields(option.priceTotal)
      option.taskPrices = []

      const arrayOfArraysGroupTask = option.selectedGroups
      const filteredTaskRemove = tasks
        ?.filter((v: { group: any }) => arrayOfArraysGroupTask?.includes(v.group))
        ?.map((v: { _id: string }) => v._id)

      // Calculate upsell amount and grand total for each task in the option
      option.taskArray?.forEach((taskId: any) => {
        const task = tasks?.find((t: { _id: any }) => t._id === taskId)
        if (task) {
          let taskPrice = {}
          initializePackageOptionFields(taskPrice)
          taskPrice = calculateAndUpdatePackageAndOption(
            option.priceTotal,
            task,
            variables,
            price,
            markup,
            option.upsell,
            true
          )
          option.taskPrices.push({ taskId, taskPrice })
        }
      })

      // Subtract upsell amount and grand total for tasks that need to be replaced
      filteredTaskRemove?.forEach((taskId: any) => {
        if (selectedPackage?.taskArray.includes(taskId)) {
          const task = tasks?.find((t: { _id: any }) => t._id === taskId)
          if (task) {
            let taskPrice = {}
            initializePackageOptionFields(taskPrice)
            taskPrice = calculateAndUpdatePackageAndOption(
              option.priceTotal,
              task,
              variables,
              price,
              markup,
              selectedPackage.upsell,
              false
            )
            option.taskPrices.push({ taskId, taskPrice })
          }
        }
      })

      // Calculate total price for the option
      let calculatedPrice = Math.ceil(option.priceTotal.grandTotal / 100)

      if (option.useMinPrice && option.minPrice > calculatedPrice) {
        option.priceTotal.grandTotal = option.minPrice * 100
        calculatedPrice = option.minPrice
      }

      option.price = calculatedPrice
      // option.price = calculatedPrice === -0 ? 0 : calculatedPrice
      console.log({ option })
      // option.price = Math.ceil(option.priceTotal.grandTotal / 100)
    }
    return upgradeOptions ?? []
  } catch (error) {
    console.error('newRoofUpgradePackages', error)
    return []
  }
}

export const upgradePackagesMethod = (price: any) => {
  try {
    const variables = price?.variables
    const uPkg = price?.packages
    const tasks = price?.tasks
    const markup = price?.projectType?.markup || 0

    // Go through each package and pull in prices from the tasks
    for (const pkg of uPkg ?? []) {
      pkg.desc = pkg?.description
      pkg.pkgName = pkg?.name
      pkg.price = 0
      pkg.priceTotal = {}
      pkg.taskPrices = []

      initializePackageOptionFields(pkg.priceTotal)

      pkg?.taskArray?.forEach((taskId: any) => {
        const task = tasks?.find((t: { _id: any }) => t._id === taskId)
        if (task) {
          let taskPrice = {}
          initializePackageOptionFields(taskPrice)
          taskPrice = calculateAndUpdatePackageAndOption(
            pkg.priceTotal,
            task,
            variables,
            price,
            markup,
            pkg.upsell,
            true
          )
          pkg.taskPrices.push({ taskId, taskPrice })
        }
      })

      //minimum travel fee
      const minimumTravelPeople = price?.projectType?.minTravelPpl
      const minimumTravelFee = Math.round(
        ((price.duration * (minimumTravelPeople * 2)) / 60) * variables?.travelHrlyRate * 100
      )

      const feeDifference = Math.round(minimumTravelFee - pkg.priceTotal.travelFee)
      if (feeDifference > 0) {
        const travelBurden = feeDifference * variables.ttlBurden
        const travelLaborTotal = feeDifference + travelBurden
        const travelOverhead = feeDifference * variables.modOH * (1 + markup)
        const travelProfit = feeDifference * variables.modP * (1 + markup)
        const travelUpsell = (travelOverhead + travelProfit) * pkg.upsell
        const travelCashTotal = travelLaborTotal + travelOverhead + travelProfit + travelUpsell
        const travelFinance = travelCashTotal / (1 - variables.financeMod)
        const travelGrandTotal = travelFinance / (1 - variables.commission)
        const commissionCalc = travelGrandTotal - travelFinance

        pkg.priceTotal.travelFee += feeDifference
        pkg.priceTotal.lSubtotal += feeDifference
        pkg.priceTotal.lBurden += travelBurden
        pkg.priceTotal.lTotal += travelLaborTotal
        pkg.priceTotal.jobCost += travelLaborTotal
        pkg.priceTotal.overhead += travelOverhead
        pkg.priceTotal.profit += travelProfit
        pkg.priceTotal.upsell += travelUpsell
        pkg.priceTotal.cashTotal += travelCashTotal
        pkg.priceTotal.financeTotal += travelFinance
        pkg.priceTotal.commission += commissionCalc
        pkg.priceTotal.grandTotal += travelGrandTotal
      }

      // Calc permit cost and add to final price
      if (price?.projectType?.permitRequired) {
        const permit = getPermit(pkg.priceTotal.grandTotal / 100, variables)
        pkg.priceTotal.grandTotal += permit.total
        pkg.priceTotal.mTotal += permit.cost
        pkg.priceTotal.permit = permit.cost
        pkg.priceTotal.financeTotal += permit.financeFee
        pkg.priceTotal.commission += permit.commission
      }
      // Calc Asbestos test cost and add to final price
      if (price?.projectType?.asbTestRequired) {
        const asbTest = getAsbTest(price?.state, variables)
        pkg.priceTotal.grandTotal += asbTest.total
        pkg.priceTotal.mTotal += asbTest.rawCost
        pkg.priceTotal.asbTest = asbTest.rawCost
        pkg.priceTotal.financeTotal += asbTest.financeFee
        pkg.priceTotal.commission += asbTest.commission
      }
      // final package price
      pkg.price = Math.ceil(pkg.priceTotal.grandTotal / 100)
    }
    return uPkg
  } catch (error: any) {
    console.error('upgradePackagesMethod error', error)
  }
}

export const signContractMethod = (
  signContractDto: SignContractDto,
  projectType: any,
  opportunity: any,
  priceByProjectCopy: any,
  projectById: any,
  taxModal: any,
  // discount: any,
  selectedPackage?: any,
  upgradeOption?: any,
  filteredTaskForRemove?: string[],
  isCombineProject?: boolean
) => {
  try {
    const { projectId, pmtMethod } = signContractDto
    let { chosenUpgrades } = signContractDto
    // const upgradeSelectedPackage = upgradePackageData.find((v) => v._id === packageId)
    const projectPrice: any = JSON.parse(JSON.stringify(priceByProjectCopy))
    const projectTypes: any = projectPrice?.projectType
      ? JSON.parse(JSON.stringify(projectPrice?.projectType))
      : JSON.parse(JSON.stringify(projectType))
    // const projectTypes: any = project.projectTypes[0]
    const opp: any = opportunity
    const contact = opportunity?.contact

    // getting discount from price
    // const manualChange = discount || 0

    const variables = projectPrice?.variables

    const projectTasks = [...projectPrice?.tasks]
    const order = getOrderTasks(selectedPackage, upgradeOption, chosenUpgrades, filteredTaskForRemove, projectTasks)

    const rawTasks = []
    const rawMats = []
    const rawLabor = []

    for (const { _id, tMat, tLabor } of order) {
      rawTasks.push({ _id })
      rawMats.push(...tMat.map((item: any) => item.mat))
      rawLabor.push(...tLabor.map((item: any) => item.worker))
    }

    const matsUnique = [...new Set(rawMats)]

    // const matList = matsUnique.reduce((result: any[], matId: string) => {
    //   const mat: any = {
    //     amount: 0,
    //     cost: 0,
    //   }

    //   for (const { tMat } of order) {
    //     for (const item of tMat) {
    //       if (item.mat === matId) {
    //         mat.amount += roundTo2(item.matAmount)
    //         mat.cost += roundTo2(item.matCost)
    //         mat._id = item.mat
    //         mat.name = item.matName
    //         mat.category = item.matCat
    //         mat.inventory = item.matInv
    //         mat.unit = item.matUnit
    //         mat.vendor = item.matVendor
    //         mat.price = roundTo2(item.matCost)
    //         if (isCombineProject) {
    //           if (mat?.inventory === false && mat?.vendor === 1) {
    //             console.log('firstttttttt1', mat)
    //           }
    //           mat.ProjectType = projectPrice?.projectType?.id
    //           mat.projectId = projectPrice?.projectId?._id
    //         } else {
    //           if (mat?.inventory === false && mat?.vendor === 1) {
    //             console.log('firstttttttt2', mat)
    //           }
    //           mat.ProjectType = ''
    //           mat.projectId = projectPrice?.projectId
    //         }
    //       }
    //     }
    //   }

    //   result.push(mat)
    //   return result
    // }, [])

    const matList = matsUnique.reduce((result: any[], matId: string) => {
      // Iterate over each order to process materials per project
      for (const { tMat, _id: projectId } of order) {
        const mat: any = {
          amount: 0,
          cost: 0,
        }

        // Check each material in tMat
        for (const item of tMat) {
          if (item.mat === matId) {
            // Accumulate material details
            mat.amount += roundTo2(item.matAmount)
            mat.cost += roundTo2(item.matCost)
            mat._id = item.mat
            mat.name = item.matName
            mat.category = item.matCat
            mat.inventory = item.matInv
            mat.unit = item.matUnit
            mat.vendor = item.matVendor
            mat.price = roundTo2(item.matCost)

            // Assign ProjectType and projectId
            if (isCombineProject) {
              mat.ProjectType = projectPrice?.projectType?.id
              mat.projectId = projectPrice?.projectId?._id
            } else {
              mat.ProjectType = ''
              mat.projectId = projectPrice?.projectId // Use the specific projectId from this order
            }
          }
        }

        // Check if a material for the same matId AND projectId already exists
        const existingMat = result.find((r) => r._id === matId && r.projectId === mat.projectId)

        if (existingMat) {
          // Update the existing entry
          existingMat.amount += mat.amount
          existingMat.cost += mat.cost
        } else {
          // Add a new entry for this matId and projectId
          result.push(mat)
        }
      }

      return result
    }, [])

    console.log({ matList })

    const workOrder = rawTasks.map((rawTask: any) => {
      const labor: any = {
        ttlMinutes: 0,
        ttlHours: 0,
        cost: 0,
        waste: 0,
        rawMinutes: 0,
        modMinutes: 0,
      }
      const orderItem = order.find((orderItem: any) => orderItem._id === rawTask._id)
      if (orderItem) {
        for (const laborItem of orderItem.tLabor) {
          labor.ttlMinutes += roundTo2(laborItem.ttlMinutes)
          labor.ttlHours += roundTo2(laborItem.ttlHours)
          labor.cost += roundTo2(laborItem.cost)
          labor.waste += roundTo2(laborItem.waste)
          labor.rawMinutes += roundTo2(laborItem.rawMinutes)
          labor.modMinutes += roundTo2(laborItem.modMinutes)
          labor.worker = laborItem.worker
          labor.title = laborItem.posTitle
          labor.hrlyRate = laborItem.posRate
          labor.task = orderItem.tName
          labor.task_id = orderItem._id
          labor.rawValue = orderItem.tRawVal
          labor.value = orderItem.tValue
          labor.taskName = orderItem.tName
          labor.taskUnit = orderItem.tUnit
          labor.order = orderItem.order || 0
          labor.group = orderItem.group || ''
        }
      }

      return labor
    })

    // selected package price total
    const priceTotals = selectedPackage.priceTotal
    priceTotals.ceilGrandTotal = Math.ceil(priceTotals.grandTotal / 100) * 100

    for (let i = 0; i < chosenUpgrades.length; i++) {
      const opt = upgradeOption.find((o: any) => chosenUpgrades[i] === o._id)
      if (!opt) continue
      priceTotals.cashTotal += opt.priceTotal.cashTotal
      priceTotals.financeTotal += opt.priceTotal.financeTotal
      priceTotals.jobCost += opt.priceTotal.jobCost
      priceTotals.lBurden += opt.priceTotal.lBurden
      priceTotals.lSubtotal += opt.priceTotal.lSubtotal
      priceTotals.lTotal += opt.priceTotal.lTotal
      priceTotals.laborCost += opt.priceTotal.laborCost
      priceTotals.mMarkup += opt.priceTotal.mMarkup
      priceTotals.mTotal += opt.priceTotal.mTotal
      priceTotals.matCost += opt.priceTotal.matCost
      priceTotals.matTax += opt.priceTotal.matTax
      priceTotals.overhead += opt.priceTotal.overhead
      priceTotals.profit += opt.priceTotal.profit
      priceTotals.commission += opt.priceTotal.commission
      priceTotals.travelFee += opt.priceTotal.travelFee
      priceTotals.upsell += opt.priceTotal.upsell
      priceTotals.grandTotal += opt.priceTotal.grandTotal
      priceTotals.ceilGrandTotal += Math.ceil(opt.priceTotal.grandTotal / 100) * 100
    }
    //setting jobTotal field
    priceTotals.jobTotal = priceTotals.ceilGrandTotal

    //minimum price check
    // const minimumPrice = projectPrice?.projectType?.typeMinimum ? projectPrice?.projectType?.typeMinimum * 100 : 0
    // if (priceTotals.jobTotal < minimumPrice) {
    //   priceTotals.jobTotal = minimumPrice
    // }

    // priceTotals.discount = roundTo2(100 * manualChange)
    // priceTotals.jobTotal -= priceTotals.discount
    // priceTotals.commission -= roundTo2(variables?.commission * priceTotals.discount)
    // priceTotals.discountComm = roundTo2(variables?.commission * priceTotals.discount)

    //extra amount added to profit for rounding grandtotal to next whole number
    const profitWithExtraAmount = priceTotals.ceilGrandTotal - priceTotals.grandTotal + priceTotals.profit

    // //total travel hours for labor
    // priceTotals.lttHours=roundTo2((priceTotals.travelFee/100)/variables?.travelHrlyRate)
    //labor
    priceTotals.travelFee = roundTo2(priceTotals.travelFee / 100)
    priceTotals.laborCost = roundTo2(priceTotals.laborCost / 100)
    priceTotals.lBurden = roundTo2(priceTotals.lBurden / 100)
    priceTotals.lSubtotal = roundTo2(priceTotals.lSubtotal / 100)
    priceTotals.lTotal = roundTo2(priceTotals.lTotal / 100)
    //permits & asbtest (they are added in material)
    priceTotals.permit = roundTo2(priceTotals.permit / 100)
    priceTotals.asbTest = roundTo2(priceTotals.asbTest / 100)
    //material
    priceTotals.mMarkup = roundTo2(priceTotals.mMarkup / 100)
    priceTotals.matCost = roundTo2(priceTotals.matCost / 100)
    priceTotals.matTax = roundTo2(priceTotals.matTax / 100)
    priceTotals.mTotal = roundTo2(priceTotals.mTotal / 100)

    priceTotals.jobCost = roundTo2(priceTotals.jobCost / 100)
    //gross profit
    priceTotals.overhead = roundTo2(priceTotals.overhead / 100)
    priceTotals.profit = roundTo2(profitWithExtraAmount / 100)
    priceTotals.upsell = roundTo2(priceTotals.upsell / 100)
    priceTotals.financeTotal = roundTo2(priceTotals.financeTotal / 100)
    // priceTotals.commission = roundTo2(priceTotals.commission / 100)
    // priceTotals.discount = roundTo2(priceTotals.discount / 100)
    // priceTotals.discountComm = roundTo2(priceTotals.discountComm / 100)

    priceTotals.cashTotal = roundTo2(priceTotals.cashTotal / 100)

    priceTotals.grandTotal = roundTo2(priceTotals.ceilGrandTotal / 100)

    //minimum price check
    const minimumPrice = projectPrice?.projectType?.typeMinimum ? projectPrice?.projectType?.typeMinimum : 0
    if (priceTotals.grandTotal < minimumPrice) {
      priceTotals.grandTotal = minimumPrice
    }

    // priceTotals.jobTotal = roundTo2(priceTotals.jobTotal / 100)

    // priceTotals.actRev = roundTo2(priceTotals.jobTotal - priceTotals.mTotal)

    //deleting extra field
    delete priceTotals.ceilGrandTotal

    // price is used to show prices on price page only
    const price: any = {}
    // price.totalCost = priceTotals.grandTotal //task Total
    // // price total, down, deposit, final payment breakdown
    // price.subTotal = priceTotals.jobTotal

    // price.discount = priceTotals.discount

    // price.salesTax = priceTotals.salesTax

    // // price.total = Math?.ceil(price.subTotal + price.salesTax) || 0
    // price.total = roundTo2(price.subTotal + price.salesTax)

    // price.deposit = ceilToDecimalPlaces(price.total * (projectTypes?.deposit ?? 0.5), 0)
    // price.downPmt = ceilToDecimalPlaces(price.total * (projectTypes?.downPmt ?? 0), 0)
    // price.finalPmt = price.total - price.deposit - price.downPmt

    // price.paySchdl = {
    //   deposit: (projectTypes?.deposit ?? 0.5) * 100,
    //   downPmt: (projectTypes?.downPmt ?? 0) * 100,
    //   finalPmt: 100 - (projectTypes?.deposit ?? 0.5) * 100 - (projectTypes?.downPmt ?? 0) * 100,
    // }

    return {
      oppId: opp._id,
      projectId: projectId,
      projectPriceId: projectPrice?._id,
      name: projectById?.name,
      notes: projectById?.notes,
      projectType: projectType?._id,
      priceTotals,
      matList,
      workOrder,
      contact,
      typeReplacement: projectTypes.typeReplacement,
      typeName: projectTypes.name,
      price,
    }
  } catch (error: any) {
    console.error('signContractError', error)
  }
}
