import { Form, Formik } from 'formik'
import { useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import { Link, useNavigate, useParams } from 'react-router-dom'

import {
  getDashboardBacklog,
  getDayReportList,
  getMembersNotIn,
  getMissingDailyLogs,
  getNotClockedInData,
  getSalesPersonReport,
  getUnapprovedCardData,
} from '../../logic/apis/dashboard'
import { getPosition } from '../../logic/apis/position'
import { getPositionMembersById, getStages } from '../../logic/apis/sales'
import Button from '../../shared/components/button/Button'
import { SLoader } from '../../shared/components/loader/Loader'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import {
  dayjsFormat,
  endOfDate,
  extractPermissionByName,
  formatCurrency,
  formatNumberToCommaS,
  getDataFromLocalStorage,
  getEnumValue,
  getHoursAndMinutes,
  getWeekRange,
  hasValues,
  isSuccess,
  nextAction,
  notify,
  startOfDate,
} from '../../shared/helpers/util'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import { getCompanyCrews } from '../../logic/apis/crew'
import {
  IconContainer,
  KeyValueSpan,
  TimeCardApproveNoApproveRightDiv,
  WorkDoneTimeCardContainer,
  WorkDoneTimeCardHeader,
} from '../timeCard/components/approveTimeCards/style'
import { StorageKey, TimeCardStatusEnum } from '../../shared/helpers/constants'
import { colors } from '../../styles/theme'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import { TickmarkIcon } from '../../assets/icons/TickmarkIcon'
import SalesPersonDashboard from './components/SalesPersonDashboard'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { CommissionReportDescription, TableWrap } from '../reports/commissionReport/style'
import { Table } from '../../shared/table/Table'
import {
  modifiedSalesCommissionColumn,
  repairsColumn,
  roofColumnDashboard,
  roofCompletedColumn,
} from '../reports/commissionReport/tableColumn'
import { renderCrewDataLoading, renderLoadingTable, renderSalesDataLoading } from './components/dashboardLoaders'
import { I_Stage } from '../opportunity/components/assessmentForm/AssessmentForm'
import LeadsDashboard from './components/LeadsDashboard'
import dayjs from 'dayjs'

const notCompletedColums = ['Name', 'Date Signed', 'Volume']

// const salesDataVisibleTo = ['Owner', 'SalesManager', 'SalesPerson', 'Admin', 'RRTech'] // (reference) old way of showing dashboards
const salesDataAdminVisibleTo = ['Owner', 'SalesManager', 'Admin', 'GeneralManager']
// const managerDataVisibleTo = ['Owner', 'ProjectManager', 'GeneralManager', 'CrewLead', 'Foreman', 'Admin'] // (reference) old way of showing dashboards
// const approveTimecardVisbileTo = ['Owner', 'ProjectManager', 'CrewLead', 'GeneralManager', 'Foreman', 'Admin'] // (reference) old way of showing dashboards

const Dashboard = () => {
  const globalSelector = useSelector((state: any) => state)
  const { currentMember, positionDetails, positionPermissions } = globalSelector.company
  const position = positionDetails?.symbol
  {
    console.log({ positionDetails })
  }

  const dashboardTypes = useMemo(() => {
    return {
      leadsDashboard: positionPermissions['leads dashboard'],
      missingDailylogsDashboard: positionPermissions['missing dailylogs dashboard'],
      oppsToDoDashboard: positionPermissions['opps to do dashboard'],
      timeCardsDashboard: positionPermissions['timecards missing/unapproved dashboard'],
    }
  }, [positionPermissions])

  const [salesPersonDrop, setSalesPersonDrop] = useState<any[]>([])

  const [actionResultData, setActionResultData] = useState<any>([])
  const [noActionResultData, setNoActionResultData] = useState<any>([])
  const [isShowNoAction, setIsShowNoAction] = useState<boolean>(false)
  const [salesPersonName, setSalesPersonName] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [salesDataLoading, setSalesDataLoading] = useState(false)
  const [showNotCompleted, setShowNotCompleted] = useState(false)
  const [membersNotInData, setMembersNotInData] = useState<{ [key: string]: any }>({})
  const [crewDataLoading, setCrewDataLoading] = useState(false)
  const [notClockInData, setNotClockInData] = useState<{ [key: string]: any }>({})
  const [crewLeadsData, setCrewLeadsData] = useState([])
  const [crewLeadName, setCrewLeadName] = useState('')
  const [unapprovedDataLoading, setUnapprovedDataLoading] = useState(false)
  const [unapprovedData, setUnapprovedData] = useState<{ [key: string]: any }>({})
  const [reportListData, setReportListData] = useState({})
  const [showSalesBreakdown, setShowSalesBreakdown] = useState(false)
  const [approveRejectLoadingObj, setApproveRejectLoadingObj] = useState<any>({})
  const [timeCardName, setTimeCardName] = useState<string>('')
  const [weekStartDate, setWeekStartDate] = useState('')
  const [missingLogData, setMissingLogData] = useState<{ [key: string]: any }>({})
  const [showRoofsCompletedData, setShowRoofsCompletedData] = useState<{ [key: number]: boolean }>({})
  const [showRepairsData, setShowRepairsData] = useState<{ [key: number]: boolean }>({})
  const [stages, setStages] = useState<I_Stage[]>([])

  const [actionLoading, setActionLoading] = useState(false)
  const [actionData, setActionData] = useState<{ [key: string]: any }>({})
  const [actionLimit, setActionLimit] = useState(10)
  const [showMoreClicked, setShowMoreClicked] = useState(false)

  const [assignLoading, setAssignLoading] = useState(false)

  const [selectedOppsIds, setSelectedOppsIds] = useState([])
  const [assignSalesPerson, setAssignSalesPerson] = useState('')
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)

  const [showRoofCompletedOne, setShowRoofCompletedOne] = useState(false)
  const [showRoofCompletedTwo, setShowRoofCompletedTwo] = useState(false)

  const [prevWeekStartRange, setPrevWeekStartRange] = useState(0)

  const [backlogData, setBacklogData] = useState({
    crews: '',
    rawDays: '',
    projectedStart: '',
  })

  const toggleRoofsCompletedData = (type: number) => {
    setShowRoofsCompletedData((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const [dateRange, setDateRange] = useState({ start: '', end: '' })

  useEffect(() => {
    const { start, end } = getWeekRange(prevWeekStartRange)

    setDateRange({ start, end })

    setWeekStartDate(start)
  }, [prevWeekStartRange])

  const hasDayReportAccess =
    hasValues(positionDetails) &&
    extractPermissionByName(positionDetails, 'timecards missing/unapproved')?.permissions < 3

  const [approveDate, setApproveDate] = useState(new Date()?.toISOString())

  const [backlogDataLoading, setBacklogDataLoading] = useState(false)

  const navigate = useNavigate()

  const [salesData, setSalesData] = useState<{ [key: string]: any }>({})

  const initialValue = {}

  const getCompanyBacklog = async () => {
    try {
      setBacklogDataLoading(true)
      const response = await getDashboardBacklog()
      setBacklogData(response?.data?.data?.obj)
    } catch (error) {
      console.log('getDashboardBacklog Error', error)
    } finally {
      setBacklogDataLoading(false)
    }
  }

  const getMembersNotInData = async () => {
    try {
      setCrewDataLoading(true)
      const response = await getMembersNotIn()
      setMembersNotInData(response?.data?.data)
    } catch (error) {
      console.log('Error', error)
    } finally {
      setCrewDataLoading(false)
    }
  }
  const getNotClockedData = async () => {
    const response = await getNotClockedInData(startOfDate(new Date()))
    setNotClockInData(response?.data?.data?.notClockedIn)
  }

  const getAllCrewLeads = async () => {
    const response = await getCompanyCrews({
      deleted: false,
      retired: false,
    })

    const filterData = response?.data?.data?.crew?.filter((item: any) => item?.foremanId)
    const crewData = filterData?.map((item: any) => ({
      name: `${item?.foremanName} ( ${item?.name} )`,
      id: item?.managerId,
      crewId: item?._id,
      crewName: item?.name,
      foremanId: item?.foremanId,
    }))

    setCrewLeadsData(crewData)
  }

  useEffect(() => {
    // getPositions()
    dashboardTypes?.oppsToDoDashboard && getCompanyBacklog()

    if (dashboardTypes?.timeCardsDashboard) {
      getMembersNotInData()
      getNotClockedData()
    }
    position === 'Owner' && getAllCrewLeads()
    getStagesData()
  }, [position, dashboardTypes])

  const getpageById = (id: string, oppId: string) => {
    const matchedObject: any = stages.find((item) => item._id === id)
    // If a matching object is found, return its name property
    if (matchedObject) {
      navigate(`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${oppId}`)
    } else {
      // If no matching object is found, return null or an appropriate default value
      return null
    }
  }

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false)
      // const stagesRes = await getStages({ companyId: currentCompany._id }, false, operationsFlag)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  useEffect(() => {
    if (dateRange?.start && dateRange?.end) {
      dashboardTypes.missingDailylogsDashboard && getMissingDailyLogsData()
    }
  }, [dateRange, position, dashboardTypes])

  const getSalesReport = async () => {
    const salesPerson =
      position !== 'SalesPerson' || position !== 'RRTech'
        ? salesPersonDrop.filter((value: any) => value.name === salesPersonName)
        : []
    try {
      setSalesDataLoading(true)
      const response = await getSalesPersonReport({
        salesPersonId: position === 'SalesPerson' || position == 'RRTech' ? currentMember?._id : salesPerson[0]?._id,
        currentDate: startOfDate(new Date()),
      })

      setSalesData(response?.data?.data?.report)
    } catch (error) {
      console.log('Error', error)
    } finally {
      setSalesDataLoading(false)
    }
  }

  const getUnapprovedData = async () => {
    try {
      setUnapprovedDataLoading(true)

      const res = await getUnapprovedCardData({
        crewLeadId: crewLeadsData?.find((item: any) => item.name === crewLeadName)?.foremanId || currentMember?._id,
        dayStart: startOfDate(new Date()),
      })

      setUnapprovedData(res?.data?.data?.obj)
    } catch (error) {
      // console.log('Error', error)
    } finally {
      setUnapprovedDataLoading(false)
    }
  }

  useEffect(() => {
    if (currentMember?.name && (position === 'SalesPerson' || position == 'RRTech')) {
      setSalesPersonName(currentMember?.name)
    }
  }, [position, currentMember?.name])

  const getDayReportListData = async () => {
    const res = await getDayReportList({
      dayEnd: endOfDate(new Date(approveDate)),
      dayStart: startOfDate(new Date(approveDate)),
    })

    setReportListData(res?.data?.data?.dayObj)
  }

  const getMissingDailyLogsData = async () => {
    const res = await getMissingDailyLogs({
      dayEnd: endOfDate(dateRange?.end),
      dayStart: startOfDate(dateRange?.start),
    })

    setMissingLogData(res?.data?.data?.data)
  }

  useEffect(() => {
    if (salesPersonName || positionDetails?.symbol === 'SalesPerson' || position == 'RRTech') {
      // getDashboardSalesPersonAction()
      // getActionData()
      // getSalesReport()
    }

    if (
      (crewLeadName || position === 'Foreman') &&
      currentMember?._id &&
      positionDetails?.symbol &&
      dashboardTypes.timeCardsDashboard
    ) {
      getUnapprovedData()
    }
  }, [currentMember, positionDetails, salesPersonName, crewLeadName, dashboardTypes, crewLeadsData?.length])

  useEffect(() => {
    if (approveDate && hasDayReportAccess) {
      getDayReportListData()
    }

    if (position !== 'CrewLead' && dashboardTypes.timeCardsDashboard) {
      getUnapprovedData()
    }
  }, [
    currentMember,
    positionDetails,
    salesPersonName,
    crewLeadName,
    approveDate,
    dashboardTypes,
    crewLeadsData?.length,
  ])

  const getPositionMembers = async (positionId: string) => {
    try {
      const response = await getPositionMembersById({ positionId, hasOpportunity: true })
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getPositions = async () => {
    try {
      const response = await getPosition({ deleted: false }, false)
      if (isSuccess(response)) {
        const positions: any[] = response?.data?.data?.position
        // let salesPersonIdx = 0
        const salesPersonIdx: string[] = []
        // positions.forEach((position: any, idx) => {
        //   if (position.symbol === 'SalesPerson') {
        //     salesPersonIdx = idx
        //     return
        //   }
        // })
        // getPositionMembers(positions[salesPersonIdx]._id)
        positions.forEach((position: any, idx) => {
          if (position.symbol === 'SalesPerson') {
            salesPersonIdx.push(position._id)
            return
          }
          if (position.symbol === 'GeneralManager') {
            salesPersonIdx.push(position._id)
            return
          }
          if (position.symbol === 'SalesManager') {
            salesPersonIdx.push(position._id)
            return
          }
          if (position.symbol === 'RRTech') {
            salesPersonIdx.push(position?._id)
            return
          }
        })
        // getPositionMembers(positions[salesPersonIdx]._id)
        getPositionMembers(salesPersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const formatDate = (date: any) => {
    const parsedDateTime = new Date(date)

    const formattedDateTime = parsedDateTime.toLocaleString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    })

    return formattedDateTime
  }

  const toggleRepairsData = (type: number) => {
    setShowRepairsData((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  return (
    <Styled.DashboardWrap>
      <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
        <SharedStyled.SectionTitle>Dashboard</SharedStyled.SectionTitle>
      </SharedStyled.FlexRow>

      <Formik initialValues={initialValue} onSubmit={() => {}}>
        {({ setFieldValue }) => (
          <Form>
            <Styled.DashboardMainContainer>
              <SharedStyled.FlexCol gap="8px">
                {backlogDataLoading ? (
                  <SLoader width={100} height={20} />
                ) : (
                  <Styled.DashboardHeading>
                    Projected Start Date: {dayjsFormat(backlogData?.projectedStart, 'MMMM')} -{' '}
                    {dayjsFormat(dayjs(backlogData?.projectedStart).add(1, 'month'), 'MMMM')}
                  </Styled.DashboardHeading>
                )}

                {backlogDataLoading ? (
                  <SLoader width={200} height={20} />
                ) : (
                  <Styled.DashboardDescription>
                    {backlogData?.rawDays} days out w/ {backlogData?.crews} crews running
                  </Styled.DashboardDescription>
                )}
              </SharedStyled.FlexCol>

              {/* ========================================= Lead Dashboard ========================================= */}

              {dashboardTypes?.leadsDashboard ? <LeadsDashboard isVisible={dashboardTypes?.leadsDashboard} /> : null}

              {/* ========================================= opps to do ========================================= */}

              {false && (
                // {salesPersonName && (
                <>
                  <Styled.DashboardTextContentWrapper>
                    {salesDataLoading ? (
                      <>{renderSalesDataLoading()}</>
                    ) : (
                      <>
                        <SharedStyled.FlexCol gap="8px" margin="0 0 10px 0">
                          <Styled.DashboardHeading>
                            Current Month ({salesData?.dates?.thisMonth})
                          </Styled.DashboardHeading>
                          <Styled.DashboardHeading>
                            Total: {salesData?.soldCurr?.totalNum} for ${formatCurrency(salesData?.soldCurr?.totalVol)}
                          </Styled.DashboardHeading>
                        </SharedStyled.FlexCol>
                        <Styled.DashboardDescription>
                          {salesData?.benchmarks?.num} {salesData?.benchmarks?.text} so far, total bonus of $
                          {salesData?.benchmarks?.bonus}
                        </Styled.DashboardDescription>
                        <Styled.DashboardDescription>
                          Next benchmark in: ${formatCurrency(salesData?.benchmarks?.next)}
                        </Styled.DashboardDescription>

                        <TableWrap>
                          {/* <CommissionReportDescription
                                type="roof"
                                onClick={() => setShowRoofCompletedOne((prev) => !prev)}
                                cursor={true}
                              >
                                {'Roofs'} Completed: {salesData?.sold?.completedNum || 0} | Volume: $
                                {formatNumberToCommaS(Number(salesData?.sold?.completedVol))}
                              </CommissionReportDescription>

                              <CommissionReportDescription type="commission">
                                Commission: ${formatNumberToCommaS(Number(salesData?.sold?.completedComm))}
                              </CommissionReportDescription>

                              {showRoofCompletedOne && (
                                <Table
                                  columns={roofColumnDashboard}
                                  data={salesData?.sold?.completed?.sort(
                                    (a: any, b: any) => new Date(a.saleDate) - new Date(b.saleDate)
                                  )}
                                  fetchData={() => {}}
                                  noSearch
                                  minWidth=""
                                  noBorder
                                  onRowClick={(val) => getpageById(val.stage, val._id)}
                                />
                              )} */}

                          {salesData?.soldCurr?.type.map((type: any, index: number) => {
                            return (
                              <div>
                                {/* {type?.typeReplacement ? ( */}
                                <>
                                  {
                                    <>
                                      <CommissionReportDescription
                                        type="roof"
                                        onClick={() =>
                                          type?.sold?.num &&
                                          toggleRoofsCompletedData(type?.name + index + 0 + salesData?.dates?.thisMonth)
                                        }
                                        cursor={true}
                                      >
                                        {type?.name} Sold: {type?.sold?.num || 0} | Volume: $
                                        {formatNumberToCommaS(Number(type?.sold?.vol))}
                                      </CommissionReportDescription>

                                      <CommissionReportDescription type="commission">
                                        Commission: ${formatNumberToCommaS(Number(type?.sold?.commission))}
                                      </CommissionReportDescription>
                                      {showRoofsCompletedData[type?.name + index + 0 + salesData?.dates?.thisMonth] && (
                                        <Table
                                          columns={roofColumnDashboard}
                                          data={type?.sold?.opps?.sort(
                                            (a: any, b: any) =>
                                              new Date(a.saleDate)?.getTime() - new Date(b.saleDate)?.getTime()
                                          )}
                                          fetchData={() => {}}
                                          noSearch
                                          minWidth=""
                                          noBorder
                                          onRowClick={(val) => getpageById(val.stage, val._id)}
                                        />
                                      )}
                                    </>
                                  }

                                  {!!type?.started?.commission && (
                                    <>
                                      <CommissionReportDescription
                                        type="roof"
                                        onClick={() =>
                                          type?.started?.num && toggleRoofsCompletedData(type?.name + index + 1)
                                        }
                                        cursor={true}
                                      >
                                        {type?.name} Started: {type?.started?.num || 0} | Volume: $
                                        {formatNumberToCommaS(Number(type?.started?.volume))}
                                      </CommissionReportDescription>
                                      <CommissionReportDescription type="commission">
                                        Commission: ${formatNumberToCommaS(Number(type?.started?.commission))}
                                      </CommissionReportDescription>
                                      {showRoofsCompletedData[type?.name + index + 1] && (
                                        <Table
                                          columns={roofCompletedColumn}
                                          data={type?.started?.opps?.sort(
                                            (a: any, b: any) =>
                                              new Date(a.saleDate)?.getTime() - new Date(b.saleDate)?.getTime()
                                          )}
                                          fetchData={() => {}}
                                          noSearch
                                          minWidth=""
                                          noBorder
                                          onRowClick={(val) => getpageById(val.stage, val._id)}
                                        />
                                      )}
                                    </>
                                  )}
                                  {
                                    <>
                                      <CommissionReportDescription
                                        type="roof"
                                        onClick={() =>
                                          type?.completed?.num &&
                                          toggleRoofsCompletedData(
                                            type?.name + index + 10 + salesData?.dates?.thisMonth
                                          )
                                        }
                                        cursor={true}
                                      >
                                        {type?.name} Completed: {type?.completed?.num || 0} | Volume: $
                                        {formatNumberToCommaS(Number(type?.completed?.vol))}
                                      </CommissionReportDescription>
                                      <CommissionReportDescription type="commission">
                                        Commission: ${formatNumberToCommaS(Number(type?.completed?.commission))}
                                      </CommissionReportDescription>
                                      {showRoofsCompletedData[
                                        type?.name + index + 10 + salesData?.dates?.thisMonth
                                      ] && (
                                        <Table
                                          columns={roofColumnDashboard}
                                          data={type?.completed?.opps?.sort(
                                            (a: any, b: any) =>
                                              new Date(a.saleDate)?.getTime() - new Date(b.saleDate)?.getTime()
                                          )}
                                          fetchData={() => {}}
                                          noSearch
                                          minWidth=""
                                          noBorder
                                          onRowClick={(val) => getpageById(val.stage, val._id)}
                                        />
                                      )}
                                    </>
                                  }

                                  {/* {showRoofsCompletedData[type?.name + index + 1] && (
                                          <Table
                                            columns={roofCompletedColumn}
                                            data={type?.completed?.opps?.sort(
                                              (a: any, b: any) => new Date(a.saleDate) - new Date(b.saleDate)
                                            )}
                                            fetchData={() => {}}
                                            noSearch
                                            minWidth=""
                                            noBorder
                                            onRowClick={(val) => getpageById(val.stage, val._id)}
                                          />
                                        )} */}
                                </>
                                {/*   // ) : (
                                      // <>
                                      //   <CommissionReportDescription
                                      //     type="roof"
                                      //     onClick={() => toggleRepairsData(type?.name + index)}
                                      //     cursor={true}
                                      //   >
                                      //     {type?.name}: {type?.sold?.num || 0} | Volume: $
                                      //     {formatNumberToCommaS(Number(type?.sold?.vol))}
                                      //   </CommissionReportDescription>
                                      //   <CommissionReportDescription type="commission">
                                      //     Commission: ${formatNumberToCommaS(Number(type?.sold?.commission))}
                                      //   </CommissionReportDescription>
                                      //   {showRepairsData[type?.name + index] && (
                                      //     <Table
                                      //       columns={repairsColumn}
                                      //       data={type?.sold?.opps?.sort(
                                      //         (a: any, b: any) => new Date(a.saleDate) - new Date(b.saleDate)
                                      //       )}
                                      //       fetchData={() => {}}
                                      //       noSearch
                                      //       minWidth=""
                                      //       noBorder
                                      //       onRowClick={(val) => getpageById(val.stage, val._id)}
                                      //     />
                                      //   )}
                                      // </>
                                    // )}*/}
                              </div>
                            )
                          })}

                          {salesData?.soldCurr?.modified?.opp?.length > 0 ? (
                            <>
                              <CommissionReportDescription type="roof">
                                Commission Modifications
                              </CommissionReportDescription>
                              <CommissionReportDescription type="commission">
                                Commission: ${formatNumberToCommaS(Number(salesData?.soldCurr?.modified?.amount))}
                              </CommissionReportDescription>
                            </>
                          ) : null}

                          {salesData?.soldCurr?.modified?.opp?.length > 0 ? (
                            <Table
                              columns={modifiedSalesCommissionColumn}
                              data={salesData?.soldCurr?.modified?.opp?.sort(
                                (a, b) => new Date(a.saleDate) - new Date(b.saleDate)
                              )}
                              fetchData={() => {}}
                              noSearch={true}
                              minWidth=""
                              noBorder
                              onRowClick={(val) => getpageById(val?.oppId?.stage, val?.oppId?._id)}
                            />
                          ) : null}
                        </TableWrap>

                        <Styled.DashboardDescription className="total">
                          <p>
                            Commission: <span>${formatNumberToCommaS(Number(salesData?.soldCurr?.totalComm))}</span>
                          </p>
                          <p>
                            Salary: <span>${formatNumberToCommaS(Number(salesData?.soldCurr?.salary))}</span>
                          </p>
                          <p>
                            Total pay: <span>${formatNumberToCommaS(Number(salesData?.soldCurr?.totalEarned))}</span>
                          </p>
                        </Styled.DashboardDescription>
                      </>
                    )}
                  </Styled.DashboardTextContentWrapper>
                  <Styled.DashboardTextContentWrapper>
                    {salesDataLoading ? (
                      <>{renderSalesDataLoading()}</>
                    ) : (
                      <>
                        <SharedStyled.FlexCol gap="8px" margin="0 0 10px 0">
                          <Styled.DashboardHeading>Last Month ({salesData?.dates?.lastMonth})</Styled.DashboardHeading>
                          <Styled.DashboardHeading>
                            Total: {salesData?.soldPrev?.totalNum} for ${formatCurrency(salesData?.soldPrev?.totalVol)}
                          </Styled.DashboardHeading>
                        </SharedStyled.FlexCol>
                        <Styled.DashboardDescription>
                          {salesData?.benchmarksPrev?.num} {salesData?.benchmarksPrev?.text} so far, total bonus of $
                          {salesData?.benchmarksPrev?.bonus}
                        </Styled.DashboardDescription>
                        <Styled.DashboardDescription>
                          Next benchmark in: ${formatCurrency(salesData?.benchmarksPrev?.next)}
                        </Styled.DashboardDescription>

                        <TableWrap>
                          {/* <CommissionReportDescription
                                type="roof"
                                onClick={() => setShowRoofCompletedTwo((prev) => !prev)}
                                cursor={true}
                              >
                                {'Roofs'} Completed: {salesData?.soldPrev?.completedNum || 0} | Volume: $
                                {formatNumberToCommaS(Number(salesData?.soldPrev?.completedVol))}
                              </CommissionReportDescription>

                              <CommissionReportDescription type="commission">
                                Commission: ${formatNumberToCommaS(Number(salesData?.soldPrev?.completedComm))}
                              </CommissionReportDescription>

                              {showRoofCompletedTwo && (
                                <Table
                                  columns={roofColumnDashboard}
                                  data={salesData?.soldPrev?.completed?.opps?.sort(
                                    (a: any, b: any) => new Date(a.saleDate) - new Date(b.saleDate)
                                  )}
                                  fetchData={() => {}}
                                  noSearch
                                  minWidth=""
                                  noBorder
                                  onRowClick={(val) => getpageById(val.stage, val._id)}
                                />
                              )} */}
                          {salesData?.soldPrev?.type.map((type: any, index: number) => {
                            return (
                              <div>
                                {/* {type?.typeReplacement ? ( */}
                                <>
                                  {
                                    <>
                                      <CommissionReportDescription
                                        type="roof"
                                        onClick={() =>
                                          toggleRoofsCompletedData(type?.name + index + 0 + salesData?.dates?.lastMonth)
                                        }
                                        cursor={true}
                                      >
                                        {type?.name} Sold: {type?.sold?.num || 0} | Volume: $
                                        {formatNumberToCommaS(Number(type?.sold?.vol))}
                                      </CommissionReportDescription>
                                      <CommissionReportDescription type="commission">
                                        Commission: ${formatNumberToCommaS(Number(type?.sold?.commission))}
                                      </CommissionReportDescription>
                                      {showRoofsCompletedData[type?.name + index + 0 + salesData?.dates?.lastMonth] && (
                                        <Table
                                          columns={roofColumnDashboard}
                                          data={type?.sold?.opps?.sort(
                                            (a: any, b: any) =>
                                              new Date(a.saleDate)?.getTime() - new Date(b.saleDate)?.getTime()
                                          )}
                                          fetchData={() => {}}
                                          noSearch
                                          minWidth=""
                                          noBorder
                                          onRowClick={(val) => getpageById(val.stage, val._id)}
                                        />
                                      )}
                                    </>
                                  }

                                  {!!type?.started?.commission && (
                                    <>
                                      <CommissionReportDescription
                                        type="roof"
                                        onClick={() =>
                                          type?.started?.num && toggleRoofsCompletedData(type?.name + index + 1)
                                        }
                                        cursor={true}
                                      >
                                        {type?.name} Started: {type?.started?.num || 0} | Volume: $
                                        {formatNumberToCommaS(Number(type?.started?.volume))}
                                      </CommissionReportDescription>
                                      <CommissionReportDescription type="commission">
                                        Commission: ${formatNumberToCommaS(Number(type?.started?.commission))}
                                      </CommissionReportDescription>
                                      {showRoofsCompletedData[type?.name + index + 1] && (
                                        <Table
                                          columns={roofCompletedColumn}
                                          data={type?.started?.opps?.sort(
                                            (a: any, b: any) =>
                                              new Date(a.saleDate)?.getTime() - new Date(b.saleDate)?.getTime()
                                          )}
                                          fetchData={() => {}}
                                          noSearch
                                          minWidth=""
                                          noBorder
                                          onRowClick={(val) => getpageById(val.stage, val._id)}
                                        />
                                      )}
                                    </>
                                  )}

                                  {
                                    <>
                                      <CommissionReportDescription
                                        type="roof"
                                        onClick={() =>
                                          toggleRoofsCompletedData(
                                            type?.name + index + 10 + salesData?.dates?.lastMonth
                                          )
                                        }
                                        cursor={true}
                                      >
                                        {type?.name} Completed: {type?.completed?.num || 0} | Volume: $
                                        {formatNumberToCommaS(Number(type?.completed?.vol))}
                                      </CommissionReportDescription>
                                      <CommissionReportDescription type="commission">
                                        Commission: ${formatNumberToCommaS(Number(type?.completed?.commission))}
                                      </CommissionReportDescription>
                                      {showRoofsCompletedData[
                                        type?.name + index + 10 + salesData?.dates?.lastMonth
                                      ] && (
                                        <Table
                                          columns={roofColumnDashboard}
                                          data={type?.completed?.opps?.sort(
                                            (a: any, b: any) =>
                                              new Date(a.saleDate)?.getTime() - new Date(b.saleDate)?.getTime()
                                          )}
                                          fetchData={() => {}}
                                          noSearch
                                          minWidth=""
                                          noBorder
                                          onRowClick={(val) => getpageById(val.stage, val._id)}
                                        />
                                      )}
                                    </>
                                  }
                                </>
                                {/*    <>
                                        <CommissionReportDescription
                                          type="roof"
                                          onClick={() => toggleRepairsData(type?.name + index + 30)}
                                          cursor={true}
                                        >
                                          {type?.name}: {type?.sold?.num || 0} | Volume: $
                                          {formatNumberToCommaS(Number(type?.sold?.vol))}
                                        </CommissionReportDescription>
                                        <CommissionReportDescription type="commission">
                                          Commission: ${formatNumberToCommaS(Number(type?.sold?.commission))}
                                        </CommissionReportDescription>
                                        {showRepairsData[type?.name + index + 30] && (
                                          <Table
                                            columns={repairsColumn}
                                            data={type?.sold?.opps?.sort(
                                              (a: any, b: any) => new Date(a.saleDate) - new Date(b.saleDate)
                                            )}
                                            fetchData={() => {}}
                                            noSearch
                                            minWidth=""
                                            noBorder
                                            onRowClick={(val) => getpageById(val.stage, val._id)}
                                          />
                                        )}
                                      </>*/}
                                {/* // )} */}
                              </div>
                            )
                          })}
                          {salesData?.soldPrev?.modified?.opp?.length ? (
                            <>
                              <CommissionReportDescription type="roof">
                                Commission Modifications
                              </CommissionReportDescription>
                              <CommissionReportDescription type="commission">
                                Commission: ${formatNumberToCommaS(Number(salesData?.soldPrev?.modified?.amount))}
                              </CommissionReportDescription>
                            </>
                          ) : null}

                          {salesData?.soldPrev?.modified?.opp?.length > 0 ? (
                            <Table
                              columns={modifiedSalesCommissionColumn}
                              data={salesData?.soldPrev?.modified?.opp?.sort(
                                (a, b) => new Date(a.saleDate) - new Date(b.saleDate)
                              )}
                              fetchData={() => {}}
                              noSearch={true}
                              minWidth=""
                              noBorder
                              onRowClick={(val) => getpageById(val?.oppId?.stage, val?.oppId?._id)}
                              // onRowClick={(val) => {
                              //   navigate(`/sales/opportunity/${val?._id}`)
                              //   // navigate(`/sales/opportunity/${val?._id}`)
                              // }}
                            />
                          ) : null}
                        </TableWrap>

                        <Styled.DashboardDescription className="total">
                          <p>
                            Commission: <span>${formatNumberToCommaS(Number(salesData?.soldPrev?.totalComm))}</span>
                          </p>
                          <p>
                            Salary: <span>${formatNumberToCommaS(Number(salesData?.soldPrev?.salary))}</span>
                          </p>
                          <p>
                            Total pay: <span>${formatNumberToCommaS(Number(salesData?.soldPrev?.totalEarned))}</span>
                          </p>
                        </Styled.DashboardDescription>
                      </>
                    )}
                  </Styled.DashboardTextContentWrapper>
                </>
              )}
              {showSalesBreakdown && <SalesPersonDashboard salesData={salesData} />}

              {/* ========================================= timecards/Crew Dashboard ========================================= */}

              <>
                {dashboardTypes.timeCardsDashboard && (
                  <Styled.DashboardTextContentWrapper>
                    <Styled.DashboardHeading>Crew Management</Styled.DashboardHeading>
                    {crewDataLoading ? (
                      <>{renderCrewDataLoading()}</>
                    ) : (
                      <>
                        {membersNotInData?.noCrew?.bool && (
                          <Styled.DashboardTextContentWrapper>
                            <Styled.DashboardDescription>
                              Crew members w/ no crew: {membersNotInData?.noCrew?.num}
                            </Styled.DashboardDescription>
                            <ul>
                              {membersNotInData?.noCrew?.members?.map((member: any, idx: number) => (
                                <li key={idx}>
                                  <Styled.DashboardDescription>{member?.name}</Styled.DashboardDescription>
                                </li>
                              ))}
                            </ul>
                          </Styled.DashboardTextContentWrapper>
                        )}
                      </>
                    )}
                    {crewDataLoading ? (
                      <>{renderCrewDataLoading()}</>
                    ) : (
                      <>
                        {membersNotInData?.manyCrew?.bool && (
                          <Styled.DashboardTextContentWrapper>
                            <Styled.DashboardDescription>
                              Crew members in more than one crew: {membersNotInData?.manyCrew?.num}
                            </Styled.DashboardDescription>
                            <ul>
                              {membersNotInData?.manyCrew?.members?.map((member: any, index: number) => (
                                <li key={index}>
                                  <Styled.DashboardDescription>{member?.name}</Styled.DashboardDescription>
                                </li>
                              ))}
                            </ul>
                          </Styled.DashboardTextContentWrapper>
                        )}
                      </>
                    )}
                    {notClockInData?.bool && (
                      <Styled.DashboardTextContentWrapper>
                        <Styled.DashboardDescription>
                          Crew members not clocked in: {notClockInData?.num}
                        </Styled.DashboardDescription>
                        <ul>
                          {notClockInData?.members?.map((member: any, idx: number) => (
                            <li key={idx}>
                              <Styled.DashboardDescription>{member?.name}</Styled.DashboardDescription>
                            </li>
                          ))}
                        </ul>
                      </Styled.DashboardTextContentWrapper>
                    )}
                  </Styled.DashboardTextContentWrapper>
                )}
                {position === 'Owner' && hasDayReportAccess && (
                  <Styled.SelectCont>
                    <CustomSelect
                      labelName="Select Crew Lead"
                      // error={false}
                      value={crewLeadName}
                      dropDownData={crewLeadsData?.map((val: any) => val?.name)}
                      setValue={setCrewLeadName}
                      // setFieldValue={setFieldValue}
                      innerHeight="45px"
                      margin="10px 0 0 0"
                      stateName="bVentCount"
                    />
                  </Styled.SelectCont>
                )}

                {/* =========================================  timecards ========================================= */}
                {dashboardTypes.timeCardsDashboard && (
                  <div>
                    <SharedStyled.FlexCol gap="10px">
                      {unapprovedDataLoading ? (
                        <SLoader width={200} height={20} />
                      ) : (
                        <>
                          {unapprovedData?.dates?.length ? <p>Time Cards To Approve: {unapprovedData?.num}</p> : null}
                        </>
                      )}
                      {unapprovedDataLoading ? (
                        <SLoader width={100} height={20} />
                      ) : unapprovedData?.num ? (
                        <p>On dates:</p>
                      ) : null}
                    </SharedStyled.FlexCol>
                    <ul>
                      {unapprovedDataLoading && (
                        <SharedStyled.FlexCol gap="10px">
                          {Array.from({ length: 5 })?.map((_, idx) => (
                            <SLoader width={100} height={20} key={idx} />
                          ))}
                        </SharedStyled.FlexCol>
                      )}
                      {!unapprovedDataLoading &&
                        unapprovedData?.dates?.map((date: string, idx: number) => (
                          <li key={idx}>
                            <Styled.DashboardDescription>
                              {date} -{' '}
                              <Link
                                className="link"
                                target="_blank"
                                rel="noopener noreferrer"
                                to={`/time-cards/approve?crew=All`}
                                onClick={() => {
                                  // setApproveDate(date)
                                  localStorage.setItem('currentDate', JSON.stringify(date))
                                }}
                              >
                                GO &gt;&gt;
                              </Link>
                            </Styled.DashboardDescription>
                          </li>
                        ))}
                    </ul>
                  </div>
                )}

                {/* ========================================= missing dailylogs ========================================= */}

                {dashboardTypes?.missingDailylogsDashboard && (
                  <>
                    <SharedStyled.FlexCol gap="10px">
                      {unapprovedDataLoading ? (
                        <SLoader width={200} height={20} />
                      ) : (
                        <p>Missing Daily Logs: {missingLogData?.num}</p>
                      )}

                      {unapprovedDataLoading ? (
                        <SLoader width={100} height={20} />
                      ) : missingLogData?.num ? (
                        <p>On dates:</p>
                      ) : null}
                    </SharedStyled.FlexCol>

                    <ul>
                      {unapprovedDataLoading && (
                        <SharedStyled.FlexCol gap="10px">
                          {Array.from({ length: 5 })?.map((_, idx) => (
                            <SLoader width={100} height={20} key={idx} />
                          ))}
                        </SharedStyled.FlexCol>
                      )}
                      {!unapprovedDataLoading &&
                        missingLogData?.dates?.map((date: string, idx: number) => (
                          <li key={idx}>
                            <Styled.DashboardDescription>
                              {date} -{' '}
                              <Link
                                className="link"
                                target="_blank"
                                rel="noopener noreferrer"
                                to={`/time-cards/approve?crew=All`}
                                onClick={() => {
                                  // setApproveDate(date)
                                  localStorage.setItem('currentDate', JSON.stringify(date))
                                }}
                              >
                                GO &gt;&gt;
                              </Link>
                            </Styled.DashboardDescription>
                          </li>
                        ))}
                    </ul>
                  </>
                )}
              </>
            </Styled.DashboardMainContainer>
          </Form>
        )}
      </Formik>
    </Styled.DashboardWrap>
  )
}

export default Dashboard
