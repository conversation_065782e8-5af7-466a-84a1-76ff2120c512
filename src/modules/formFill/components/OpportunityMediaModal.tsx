import React, { useEffect, useState } from 'react'
import useFetch from '../../../logic/apis/useFetch'
import { Field } from 'formik'
import Button from '../../../shared/components/button/Button'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import * as SharedStyled from '../../../styles/styled'
import UnitSvg from '../../../assets/newIcons/unitModal.svg'
import CloseSvg from '../../../assets/newIcons/closeIcon.svg'

import { ModalContainer, ModalHeader, ModalHeaderContainer } from '../../../styles/styled'
import { getOpportunityMedia } from '../../../logic/apis/media'

const OpportunityMediaModal = ({ field, formik, oppId, dataMedia, loadingMedia }: any) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedMedia, setSelectedMedia] = useState([])

  // const mediaFiles = dataMedia?.images?.filter((file) => /^(audio|video|image)\//.test(file.mimetype)) || []
  const mediaFiles =
    (oppId ? dataMedia?.images : dataMedia?.media)?.filter((file: any) => {
      const mimeType = file.mimetype || ''
      const accept = field.accept

      if (!accept) {
        // No accept type specified, allow all media types
        return /^(audio|video|image)\//.test(mimeType)
      }

      if (accept.includes('image/*') && mimeType.startsWith('image/')) return true
      if (accept.includes('video/*') && mimeType.startsWith('video/')) return true
      if (accept.includes('audio/*') && mimeType.startsWith('audio/')) return true

      return false
    }) || []

  useEffect(() => {
    const existingSelected = (formik?.values?.[field?.name] || []).filter((item) => item.isFromPlatform)
    setSelectedMedia(existingSelected)
  }, [formik?.values?.[field?.name]])
  console.log({ field })
  // const toggleMediaSelection = (file) => {
  //   setSelectedMedia((prevSelected) => {
  //     const exists = prevSelected.some((item) => item._id === file._id)
  //     if (exists) {
  //       return prevSelected.filter((item) => item._id !== file._id)
  //     } else {
  //       const selectedFields = {
  //         _id: file._id,
  //         url: file.url,
  //         mimetype: file.mimetype,
  //         tags: file.tags,
  //         isFromPlatform: true,
  //       }
  //       return [...prevSelected, selectedFields]
  //     }
  //   })
  // }

  const toggleMediaSelection = (file) => {
    setSelectedMedia((prevSelected) => {
      const exists = prevSelected.some((item) => item._id === file._id)

      const updatedSelected = exists
        ? prevSelected.filter((item) => item._id !== file._id)
        : [
            ...prevSelected,
            {
              _id: file._id,
              url: file.url,
              mimetype: file.mimetype,
              tags: file.tags,
              isFromPlatform: true,
            },
          ]

      // Reflect this in Formik values
      const currentFormikValues = formik.values[field.name] || []

      const updatedFormikValues = exists
        ? currentFormikValues.filter((item) => item._id !== file._id)
        : [
            ...currentFormikValues,
            {
              _id: file._id,
              url: file.url,
              mimetype: file.mimetype,
              tags: file.tags,
              isFromPlatform: true,
            },
          ]

      formik.setFieldValue(field.name, updatedFormikValues)
      return updatedSelected
    })
  }

  const isSelected = (id) => selectedMedia.some((item) => item._id === id)

  console.log({ selectedMedia }, formik?.values[field?.name])
  return (
    <div>
      <div className="camera-file-upload">
        <button type="button" className="camera-button" onClick={() => setIsOpen(true)}>
          💾
        </button>
      </div>
      <div className="file-container">
        {Array.isArray(formik?.values?.[field?.name]) &&
          formik.values[field.name]
            ?.filter((v) => v?.isFromPlatform)
            ?.map((file: any, index: number) => (
              <div key={index} className="file-item">
                {(file.type?.startsWith('image/') || file.mimetype?.startsWith('image')) && (
                  <img src={file.url} alt={file.name} className="file-preview" />
                )}
                {(file.type?.startsWith('video/') || file.mimetype?.startsWith('video')) && (
                  <video src={file.url} className="file-preview" controls />
                )}
                {(file.type?.startsWith('audio/') || file.mimetype?.startsWith('audio')) && (
                  <audio src={file.url} controls />
                )}
                {file.file && <span className="new-file-tag">New</span>}
              </div>
            ))}
      </div>

      <CustomModal show={isOpen}>
        {/* onClose={() => setIsOpen(false)} */}
        <ModalContainer>
          <ModalHeaderContainer>
            <SharedStyled.FlexRow>
              <img src={UnitSvg} alt="modal icon" />
              <SharedStyled.FlexCol>
                <SharedStyled.Text fontWeight="700" fontSize="18px">
                  Select Media Files
                </SharedStyled.Text>
              </SharedStyled.FlexCol>
            </SharedStyled.FlexRow>

            <SharedStyled.CrossContainer onClick={() => setIsOpen(false)}>
              <img src={CloseSvg} alt="close icon" />
            </SharedStyled.CrossContainer>
          </ModalHeaderContainer>

          <SharedStyled.FlexRow justifyContent="center" gap="10px" padding="10px">
            {loadingMedia ? (
              <p>Loading...</p>
            ) : (
              <div className="media-container">
                {mediaFiles.map((file) => (
                  <div key={file._id} className="media-item">
                    {/* Checkbox wrapped in label to prevent event bubbling */}
                    <label className="checkbox-container" onClick={(e) => e.stopPropagation()}>
                      <input
                        type="checkbox"
                        checked={isSelected(file._id)}
                        onChange={() => toggleMediaSelection(file)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </label>

                    {/* Media Content */}
                    <div className="media-wrapper" onClick={(e) => e.stopPropagation()}>
                      {/* {file.mimetype.startsWith('image') && (
                        <img src={file.thumbnail || file.url} alt={file.name} className="media-content" />
                      )}
                      {file.mimetype.startsWith('video') && (
                        <video src={file.url} controls className="media-content" onClick={(e) => e.stopPropagation()} />
                      )}
                      {file.mimetype.startsWith('audio') && (
                        <audio src={file.url} controls className="media-content" onClick={(e) => e.stopPropagation()} />
                      )} */}

                      <img src={file.thumbnail || file.url} alt={file.name} className="media-content" />
                    </div>

                    {/* File Name */}
                    <p className="fileName">
                      <span>{file.name.length > 20 ? file.name.substring(0, 20) + '...' : file.name}</span>
                    </p>
                  </div>
                ))}
              </div>
            )}
          </SharedStyled.FlexRow>
          <div>
            <Button
              type="button"
              onClick={() => {
                const existingFiles = formik.values[field.name] || []

                // Filter out duplicates by _id
                const newFilesToAdd = selectedMedia.filter(
                  (newFile) => !existingFiles.some((existing) => existing._id === newFile._id)
                )

                // Merge old and new media
                const mergedFiles = [...existingFiles, ...newFilesToAdd]

                formik.setFieldValue(field.name, mergedFiles)

                setIsOpen(false)
              }}
            >
              Done
            </Button>
          </div>
        </ModalContainer>
      </CustomModal>
    </div>
  )
}

export default OpportunityMediaModal
