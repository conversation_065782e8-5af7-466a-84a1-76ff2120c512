import styled, { keyframes } from 'styled-components'
import { FlexCol, rotate, SkeletonLoader } from '../../styles/styled'
import { Nue } from '../../shared/helpers/constants'
import { colors, screenSizes } from '../../styles/theme'

export const ProfileContainer = styled.div``

export const ProfileCont = styled.section<{ isEdit?: boolean }>`
  width: 100%;
  padding-bottom: 40px;
  .profile {
    border-radius: 50%;
    border: 1px solid #9747ff;
    width: ${(props) => (props.isEdit ? '172px' : '156px')};
    height: ${(props) => (props.isEdit ? '172px' : '156px')};
    flex-direction: column;
    position: relative;
    aspect-ratio: 1;
  }

  .profileCont {
    flex-direction: column;
    align-items: center;
    max-width: 100%;

    .avatar-wrapper {
      width: ${(props) => (props.isEdit ? '172px' : '72px')};
      height: ${(props) => (props.isEdit ? '172px' : '72px')};
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .avatar-wrapper.loading::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 3px solid transparent; /* Default transparent border */
      border-top-color: #9747ff; /* Color for the animated border */
      animation: ${rotate} 1s linear infinite; /* Apply rotating animation */
      box-sizing: border-box; /* Ensure border is within the wrapper */
    }
  }

  hr {
    margin: 36px 0;
    background: ${colors.lightGray};
  }

  h6 {
    font-family: ${Nue.medium};
    font-size: 14px;
    letter-spacing: 1px;
    line-height: 20px;
  }

  h5 {
    font-family: ${Nue.medium};
    letter-spacing: 1px;
    font-size: 18px;
  }

  @media (min-width: ${screenSizes.M}px) {
    width: 70%;

    .profileCont {
      flex-direction: row;
    }

    .profile {
      width: ${(props) => (props.isEdit ? '172px' : '72px')};
      height: ${(props) => (props.isEdit ? '172px' : '72px')};
    }
  }

  p {
    font-family: ${Nue.regular};
  }
`

export const InfoCont = styled(FlexCol)`
  gap: 8px;
  align-items: center;
  margin-top: 16px;

  ${SkeletonLoader} {
    width: max-content;
  }

  p {
    color: ${colors.gray1};
    font-family: ${Nue.regular};
    font-size: 12px;
    line-height: 20px;
    text-align: center;

    @media (min-width: ${screenSizes.XS}px) {
      text-align: left;
    }

    &:first-child {
      font-family: ${Nue.medium};
      font-size: 13px;
    }
  }

  div {
    align-items: center;
  }

  @media (min-width: ${screenSizes.M}px) {
    margin-top: 0px;
    align-items: flex-start;

    div {
      align-items: flex-start;
    }
  }

  h6 {
    text-transform: capitalize;
  }
`

export const UploadCont = styled(FlexCol)`
  p {
    color: ${colors.gray1};
    font-family: ${Nue.regular};
    font-size: 12px;
    line-height: 21px;
  }
`

export const UploadFileCont = styled.div`
  position: relative;
  width: 100%;
  padding: 16px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0);
  box-shadow: 0px 8px 40px 0px rgba(0, 0, 0, 0.04), 0px 2px 5px 0px rgba(0, 0, 0, 0.05),
    0px 0px 2px 0px rgba(0, 0, 0, 0.15);

  cursor: pointer;
  input {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    visibility: hidden;
  }

  &:hover {
    opacity: 0.7;
    outline: 2px solid ${colors.darkBlue};
  }
`
export const ImgCont = styled.div`
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: ${colors.lightGray1};
  img {
    width: 24px;
    height: 24px;
  }
`
