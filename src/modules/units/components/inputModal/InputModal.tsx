import React, { useEffect, useRef, useState } from 'react'
import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { Formik } from 'formik'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import {
  getDataFromLocalStorage,
  getIdFromName,
  getKeysFromObjects,
  getNameFromId,
  getUnitIdFromSymbol,
  getUnitSymbolFromId,
  getValueByKeyAndMatch,
  isSuccess,
  notify,
} from '../../../../shared/helpers/util'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { createInput, deleteInput, getInputsById, getProjectTypes, updateInput } from '../../../../logic/apis/projects'
import { IFullUnit } from '../../Units'
import { AnyKey } from '../../../opportunity/components/assessmentForm/AssessmentForm'
import { ModalHeaderInfo } from '../newUnitModal/style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'
import { RenderData, SLoader } from '../../../../shared/components/loader/Loader'

interface IInput {
  name: string
}

interface INewInputModal {
  onClose: () => void
  setAddInputResponse?: React.Dispatch<React.SetStateAction<{}>>
  isEditing?: boolean
  inputData?: any
  handleUnitModal: () => void
  units?: IFullUnit[]
  onComplete: () => void
  projectTypesDrop: any
  inputName?: string
  measurement?: string
  projectType?: string
  autoInputName?: string
}

// const PROJECT_TYPES: any = {
//   're-roof': 1,
//   'roof-repair': 2,
// }

const InputModal: React.FC<INewInputModal> = (props) => {
  const {
    onClose,
    setAddInputResponse,
    handleUnitModal,
    isEditing,
    inputName,
    inputData,
    units,
    onComplete,
    projectTypesDrop,
    measurement,
    projectType,
    autoInputName,
  } = props
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const inputRef = useRef<HTMLInputElement>(null)
  const [inputLoading, setInputLoading] = useState(false)
  const [buttonLoading, setButtonLoading] = useState(false)

  const [unitsDrop, setUnitsDrop] = useState<AnyKey>({})

  const [initialValues, setInitialValues] = useState<{
    name: string
    unit: string
    projectType: string
    orderNumber: number
  }>({
    name: autoInputName ?? '',
    unit: measurement ?? '',
    projectType: projectType ?? '',
    orderNumber: 0,
  })

  useEffect(() => {
    if (units && units.length) {
      let localUnits: AnyKey = {}
      units?.forEach((unit) => {
        localUnits[`${unit.symbol} (${unit.name})`] = unit
      })
      if (Object.keys(localUnits)) setUnitsDrop(localUnits)
    }
  }, [units])

  useEffect(() => {
    if (inputData?._id) {
      fetchInputIdApi()
    }
  }, [inputData?._id, currentCompany])

  const fetchInputIdApi = async () => {
    try {
      setInputLoading(true)
      const res = await getInputsById({
        inputId: inputData?._id,
        deleted: false,
      })
      if (isSuccess(res)) {
        const { input } = res?.data?.data

        setInitialValues({
          name: input.name,
          unit: getUnitSymbolFromId(input.unit, units),
          projectType: getValueByKeyAndMatch('name', input.projectType, 'id', projectTypesDrop),
          orderNumber: input.orderNumber,
        })
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('init fetch failed!', error)
    } finally {
      setInputLoading(false)
    }
  }
  const onSubmit = async (val: typeof initValues) => {
    setButtonLoading(true)
    const type = getIdFromName(val.projectType, projectTypesDrop)
    // getNameFromId()
    const { unit, ...updatedObj } = val
    const unitId = getUnitIdFromSymbol(unit.split(' ')[0], units)

    try {
      const res = inputData
        ? await updateInput({
            inputId: inputData._id,
            ...updatedObj,
            unit: unitId,
            projectType: type,
            createdBy: inputData?.createdBy,
          })
        : await createInput({
            createdBy: currentMember._id,
            ...updatedObj,
            unit: unitId,
            projectType: type,
          })
      if (isSuccess(res)) {
        notify(`${!isEditing ? 'Created new' : 'Updated '} input!`, 'success')
        if (setAddInputResponse && measurement && projectType) {
          //make sure unit of measurement and project type is there before adding it to field
          setAddInputResponse(res?.data?.data?.input)
        }
        onComplete()
        onClose()
      } else throw new Error(res?.data?.message)
    } catch (err: any) {
      notify(err?.message ?? 'Failed to create new input!', 'error')
    } finally {
      setButtonLoading(false)
    }
  }

  const onDelete = async () => {
    try {
      const res = await deleteInput({
        id: inputData?._id ?? '',
      })
      if (isSuccess(res)) {
        notify(`Deleted input!`, 'success')
        onComplete()
        onClose()
      } else throw new Error(res?.data?.message)
    } catch (err: any) {
      notify(err?.message ?? 'Failed to delete input!', 'error')
    }
  }
  return (
    <Styled.ModalContainer>
      <Styled.ModalHeaderContainer>
        <SharedStyled.FlexRow>
          <img src={UnitSvg} alt="modal icon" />
          <SharedStyled.FlexCol>
            <Styled.ModalHeader>{isEditing ? 'Edit' : 'Add'} Input</Styled.ModalHeader>
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>
        <Styled.CrossContainer
          onClick={() => {
            onClose()
          }}
        >
          <CrossIcon />
        </Styled.CrossContainer>
      </Styled.ModalHeaderContainer>
      <SharedStyled.SettingModalContentContainer>
        {inputLoading ? (
          <>
            <SharedStyled.FlexCol gap="25px">
              <SLoader height={45} width={100} isPercent />
              <SLoader height={45} width={100} isPercent />
              <SLoader height={45} width={100} isPercent />
              <SLoader height={15} width={100} isPercent />
            </SharedStyled.FlexCol>
          </>
        ) : (
          <Formik
            initialValues={initialValues}
            enableReinitialize={true}
            onSubmit={onSubmit}
            validateOnChange={true}
            validateOnBlur={false}
          >
            {({ values, errors, touched, resetForm, setFieldValue, handleSubmit }) => {
              return (
                <SharedStyled.FlexBox width="100%" flexDirection="column" gap="10px">
                  <InputWithValidation
                    labelName="Input Name"
                    stateName="name"
                    value={values.name}
                    error={touched.name && errors.name ? true : false}
                    passRef={inputRef}
                  />
                  <CustomSelect
                    value={values.unit}
                    error={touched.unit && errors.unit ? true : false}
                    dropDownData={Object.keys(unitsDrop)}
                    labelName="Input Unit"
                    stateName="unit"
                    setFieldValue={setFieldValue}
                    onAddClick={handleUnitModal}
                    showAddOption
                    disabled={!!measurement}
                    margin="10px 0 0 0"
                    setValue={() => {}}
                  />

                  <CustomSelect
                    dropDownData={projectTypesDrop.map(({ name }: { name: string }) => name)}
                    setFieldValue={setFieldValue}
                    stateName="projectType"
                    labelName="Project Type"
                    value={values.projectType}
                    margin="10px 0 0 0"
                    setValue={() => {}}
                    disabled={!!projectType}
                    error={touched.projectType && errors.projectType ? true : false}
                  />
                  <InputWithValidation
                    labelName="Order Number"
                    stateName="orderNumber"
                    forceType="number"
                    value={values.orderNumber}
                    error={touched.orderNumber && errors.orderNumber ? true : false}
                  />

                  {inputData && (
                    <SharedStyled.FlexCol margin="20px 0 0 0">
                      <SharedStyled.FlexRow>
                        <SharedStyled.Text fontSize="16px" fontWeight="600">
                          Used in Project Tasks: {inputData?.projectTasks?.length || 'None'}
                        </SharedStyled.Text>

                        {/* <RenderData loader={<SLoader width={20} height={20} />} loading={projectsLoading}> */}

                        {/* </RenderData> */}
                      </SharedStyled.FlexRow>

                      {inputData?.projectTasks?.length ? (
                        <ol>
                          <SharedStyled.FlexCol gap="6px">
                            {inputData?.projectTasks.map((task: any) => (
                              <li
                                key={task._id}
                                style={{
                                  fontStyle: task.active ? 'normal' : 'italic',
                                  color: task.active ? 'inherit' : 'grey',
                                  textDecoration: task.deleted ? `line-through` : 'none',
                                }}
                              >
                                {task.name}
                              </li>
                            ))}
                          </SharedStyled.FlexCol>
                        </ol>
                      ) : (
                        <></>
                      )}
                    </SharedStyled.FlexCol>
                  )}

                  <SharedStyled.FlexBox gap="12px" marginTop="26px">
                    <Button isLoading={buttonLoading} type="button" onClick={() => handleSubmit()}>
                      Submit
                    </Button>
                    {inputData ? (
                      <Button type="button" className="delete" onClick={() => onDelete()}>
                        Delete
                      </Button>
                    ) : null}
                  </SharedStyled.FlexBox>
                </SharedStyled.FlexBox>
              )
            }}
          </Formik>
        )}
      </SharedStyled.SettingModalContentContainer>
    </Styled.ModalContainer>
  )
}

export default InputModal
