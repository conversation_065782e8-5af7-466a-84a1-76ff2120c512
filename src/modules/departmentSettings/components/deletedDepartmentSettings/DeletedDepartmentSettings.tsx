import { useCallback, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

import { RevokeIcon } from '../../../../assets/icons/RevokeIcon'
import { getDepartments } from '../../../../logic/apis/department'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, notify } from '../../../../shared/helpers/util'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { ConfirmationPopUp } from '../confirmationPopup/ConfirmationPopUp'
import * as Styled from './style'
import { StorageKey } from '../../../../shared/helpers/constants'

const DeletedDepartmentSettings = () => {
  interface I_Data {
    departmentName: string
    description: string
  }

  const [loading, setLoading] = useState<boolean>(false)
  const loadmoreRef = useRef(null)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)

  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [departmentData, setDepartmentData] = useState<any>({})
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)

  const navigate = useNavigate()

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const departmentResponse = await getDepartments({ deleted: false, skip: pageIndex, limit: pageSize }, true)

        if (departmentResponse?.data?.statusCode === 200) {
          let statusRes = departmentResponse?.data?.data?.department
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              departmentName: res?.name,
              description: res?.description || '-',
              action: (
                <>
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    <Styled.IconContainer
                      content="Revoke"
                      className="restore"
                      onClick={() => {
                        setDepartmentData({ departmentName: res?.name, description: res?.description, id: res?._id })
                        setShowConfirmationPopUp(true)
                      }}
                    >
                      <RevokeIcon />
                    </Styled.IconContainer>
                  </SharedStyled.FlexBox>
                </>
              ),
            })
          })
        } else {
          notify(departmentResponse?.data?.message, 'error')
        }
        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Department Name',
        accessor: 'departmentName',
      },
      {
        Header: 'Description',
        accessor: 'description',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  return (
    <>
      <Table
        columns={columns}
        data={data}
        loading={loading}
        // pageCount={pageCount}
        fetchData={fetchData}
        noLink={true}
        noSearch
        noOverflow
        ref={loadmoreRef}
        isLoadMoreLoading={loading}
      />

      <CustomModal show={showConfirmationPopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Restore Department"
          departmentData={departmentData}
        />
      </CustomModal>
    </>
  )
}

export default DeletedDepartmentSettings
