import { useCallback, useMemo, useRef, useState, lazy } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { EditIcon } from '../../assets/icons/EditIcon'
import { getSubContractors } from '../../logic/apis/subcontractor'
import Button from '../../shared/components/button/Button'
import Pill from '../../shared/components/pill/Pill'
import ProfileInfo from '../../shared/components/profileInfo/ProfileInfo'
import TabBar from '../../shared/components/tabBar/TabBar'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { StorageKey } from '../../shared/helpers/constants'
import { formatDate, formatPhoneNumber, getDataFromLocalStorage, notify } from '../../shared/helpers/util'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { ButtonCont, SettingsCont } from '../units/style'
import { AddCityModal } from './components/addCityModal/AddCityModal'
import { ConfirmationPopUp } from './components/confirmationPopup/ConfirmationPopUp'
const DeletedSubContractor = lazy(() => import('./components/deletedSubContractor/DeletedSubContractor'))
const RetiredSubContractor = lazy(() => import('./components/retiredSubcontractor/RetiredSubContractor'))
import { SubcontractorModal } from './components/subcontractorModal/SubcontractorModal'
import * as Styled from './style'

const Subcontractor = () => {
  interface I_Data {
    name: string
    // username: string
    email: string
  }

  interface I_Column {
    Header: string
    accessor: string
  }

  const [loading, setLoading] = useState<boolean>(false)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)
  const [actionLoading, setActionLoading] = useState<boolean>(false)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const [showAddSubContractorModal, setShowAddSubContractorModal] = useState<boolean>(false)
  const [showEditSubContractorModal, setShowEditSubContractorModal] = useState<boolean>(false)
  const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [showDeleteConfirmationModal, setShowDeleteConfirmationModal] = useState<boolean>(false)
  const [showRetireConfirmationModal, setShowRetireConfirmationModal] = useState<boolean>(false)
  const [subcontractorData, setSubcontractorData] = useState<any>({})
  const fetchIdRef = useRef(0)
  const loadmoreRef = useRef(null)

  const navigate = useNavigate()

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search }: any) => {
      // This will get called when the table needs new data

      try {
        setLoading(true)
        let receivedData: any = []

        const statusResponse = await getSubContractors({
          skip: pageIndex,
          limit: pageSize,
          deleted: false,
          search,
          retired: false,
        })

        if (statusResponse?.data?.statusCode === 200) {
          let statusRes = statusResponse?.data?.data?.subcontractor

          statusRes.forEach((res: any) => {
            receivedData.push({
              ...res,
              name: res?.name,
              contact: res?.mainContractorName ? res?.mainContractorName : '-',
              phone: res?.phone,
              email: res?.email,
              // action: (
              //   <>
              //     <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
              //       <SharedStyled.TooltipContainer width={'100px'}>
              //         <span className="tooltip-content">Edit</span>
              //         <Styled.IconContainer
              //           content="Edit"
              //           className="edit"
              //           onClick={() => {
              //             setSubcontractorData({
              //               id: res?._id,
              //             })
              //             setShowEditSubContractorModal(true)
              //           }}
              //         >
              //           <EditIcon />
              //         </Styled.IconContainer>
              //       </SharedStyled.TooltipContainer>
              //     </SharedStyled.FlexBox>
              //   </>
              // ),
            })
          })
        } else {
          notify(statusResponse?.data?.message, 'error')
        }
        const fetchId = ++fetchIdRef.current

        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))
        }
      } catch (error) {
        console.error('InvitationStatus fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [dataUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => <ProfileInfo data={props?.row?.original} showImagePlaceholder />,
      },
      {
        Header: 'Status',
        accessor: 'isActive', // accessor is the "key" in the data
        Cell: (props: any) => <Pill status={props?.row?.original?.isActive} />,
      },
      {
        Header: 'main contractor',
        accessor: 'mainContractorName',
      },
      {
        Header: 'Phone No',
        accessor: 'phone',
        Cell: (props: any) =>
          props?.row?.original?.phone ? formatPhoneNumber(String(props?.row?.original?.phone), '') : '-------',
      },
      {
        Header: 'Date added',
        accessor: 'createdAt',
        Cell: (props: any) => formatDate(props?.row?.original?.createdAt),
      },

      // {
      //   Header: 'Action',
      //   accessor: 'action',
      // },
    ],
    []
  )

  return (
    <>
      <SettingsCont gap="24px">
        <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
          <SharedStyled.SectionTitle>Subcontractors</SharedStyled.SectionTitle>
          <ButtonCont>
            <Button
              onClick={() => {
                setShowAddSubContractorModal(true)
              }}
            >
              Add Subcontractor
            </Button>
          </ButtonCont>
        </SharedStyled.FlexRow>

        <SharedStyled.FlexRow alignItems="flex-start">
          <SharedStyled.FlexCol gap="24px">
            <TabBar
              tabs={[
                {
                  title: 'Active',
                  render: () => (
                    <Table
                      columns={columns}
                      data={data}
                      loading={loading}
                      // pageCount={pageCount}
                      fetchData={fetchData}
                      onRowClick={(vals) => {
                        setShowEditSubContractorModal(true)
                        setSubcontractorData({ id: vals._id })
                      }}
                      // noLink={true}

                      ref={loadmoreRef}
                      isLoadMoreLoading={loading}
                    />
                  ),
                },
                {
                  title: 'Deleted',
                  render: () => <DeletedSubContractor />,
                },
                {
                  title: 'Retired',
                  render: () => <RetiredSubContractor />,
                },
              ]}
              filterComponent={<></>}
            />
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>

        <CustomModal show={showEditSubContractorModal}>
          <SubcontractorModal
            setShowSubContractorModal={setShowEditSubContractorModal}
            setShowAddCityModal={setShowAddCityModal}
            setSubcontractorData={setSubcontractorData}
            subcontractorData={subcontractorData}
            setShowDeleteConfirmationModal={setShowDeleteConfirmationModal}
            setShowRetireConfirmationModal={setShowRetireConfirmationModal}
            setDetailsUpdate={setDataUpdate}
            dataUpdate={dataUpdate}
            setDataUpdate={setDataUpdate}
            action="Edit Subcontractor"
          />
        </CustomModal>
        <CustomModal show={showAddSubContractorModal} className="top">
          <SubcontractorModal
            setShowSubContractorModal={setShowAddSubContractorModal}
            setShowAddCityModal={setShowAddCityModal}
            setSubcontractorData={setSubcontractorData}
            subcontractorData={subcontractorData}
            setShowDeleteConfirmationModal={setShowDeleteConfirmationModal}
            setShowRetireConfirmationModal={setShowRetireConfirmationModal}
            setDetailsUpdate={setDataUpdate}
            dataUpdate={dataUpdate}
            setDataUpdate={setDataUpdate}
            action="Add Subcontractor"
          />
        </CustomModal>
        <CustomModal show={showAddCityModal}>
          <AddCityModal setShowAddCityModal={setShowAddCityModal} action="Add City" setDetailsUpdate={setDataUpdate} />
        </CustomModal>
        {/* <CustomModal show={showDeleteConfirmationModal}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowDeleteConfirmationModal}
          setDetailsUpdate={setDataUpdate}
          header="Delete Subcontractor"
          subcontractorData={subcontractorData}
          setShowEditSubContractorModal={setShowEditSubContractorModal}
        />
      </CustomModal>
      <CustomModal show={showRetireConfirmationModal}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowRetireConfirmationModal}
          setDetailsUpdate={setDataUpdate}
          header="Retire Subcontractor"
          subcontractorData={subcontractorData}
          setShowEditSubContractorModal={setShowEditSubContractorModal}
        />
      </CustomModal> */}
      </SettingsCont>
    </>
  )
}

export default Subcontractor
