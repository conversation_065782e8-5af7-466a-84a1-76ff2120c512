import { useState } from 'react'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import Tag from '../../shared/components/tag'
import Modal from '../../shared/customModal/Modal'
import { FlexRow } from '../../styles/styled'
import { updateMediaOpportunity } from '../../logic/apis/media'
import { isSuccess, notify } from '../../shared/helpers/util'
import Button from '../../shared/components/button/Button'

interface MediaTagModalProps {
  selectedTags: string[]
  setSelectedTags: React.Dispatch<React.SetStateAction<string[]>>
  tagsData: {
    mediaSetting?: {
      tags?: string[]
    }
  }
  oppId?: string
  onClose: () => void
  onComplete: () => void
  selectedMedia: string[]
}

const MediaTagModal: React.FC<MediaTagModalProps> = ({
  selectedTags,
  setSelectedTags,
  tagsData,
  selectedMedia,
  onClose,
  onComplete,
  oppId,
}) => {
  const [updateLoading, setUpdateLoading] = useState(false)
  const handleUpdateMedia = async () => {
    try {
      console.log(
        selectedMedia?.map((itm: any) => ({
          _id: itm,
          tags: selectedTags,
        }))
      )

      const payload = selectedMedia?.map((itm: any) => ({
        _id: itm,
        tags: selectedTags,
      }))
      setUpdateLoading(true)
      const res = await updateMediaOpportunity(oppId!, payload)
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        onComplete()
      }
    } catch (error) {
    } finally {
      setUpdateLoading(false)
    }
  }

  return (
    <Modal title="Add Media Tags" onClose={onClose}>
      <div style={{ marginTop: '10px' }}>
        {selectedTags?.map((tag: string) => (
          <Tag
            key={tag}
            title={tag}
            showRemoveIcon
            onClose={() => {
              setSelectedTags((prev) => prev.filter((t) => t !== tag))
            }}
          />
        ))}
      </div>
      <AutoComplete
        labelName="Tags"
        stateName="tags"
        options={tagsData?.mediaSetting?.tags?.filter((tag) => !selectedTags?.includes(tag)) ?? []}
        setValueOnClick={(value: string) => {
          setSelectedTags((prev) => [...prev, value])
        }}
        dropdownHeight="300px"
        borderRadius="0px"
      />
      <FlexRow margin="20px 0 0 0">
        <Button onClick={onClose} className="gray" disabled={updateLoading}>
          Cancel
        </Button>
        <Button onClick={handleUpdateMedia} isLoading={updateLoading}>
          Update media
        </Button>
      </FlexRow>
    </Modal>
  )
}

export default MediaTagModal
