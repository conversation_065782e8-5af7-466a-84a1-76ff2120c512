import React, { useState } from 'react'
import Modal from '../../shared/customModal/Modal'
import { FlexRow } from '../../styles/styled'
import { deleteMediaOpportunity } from '../../logic/apis/media'
import Button from '../../shared/components/button/Button'
import { isSuccess, notify } from '../../shared/helpers/util'

interface DeleteMediaModalProps {
  onClose: () => void
  onComplete: () => void
  selectedMedia: string[]
}

const DeleteMediaModal = ({ selectedMedia, onClose, onComplete }: DeleteMediaModalProps) => {
  const [deleteLoading, setDeleteLoading] = useState(false)
  const handleDeleteSelection = async () => {
    try {
      setDeleteLoading(true)
      const res = await deleteMediaOpportunity(selectedMedia?.join(','))

      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        onComplete()
      }
    } catch (error) {
      console.log(error)
    } finally {
      setDeleteLoading(false)
    }
  }

  return (
    <Modal title="Delete Media" onClose={onClose}>
      <h3 className="text-center">Are you sure you want to delete the selected media?</h3>
      <FlexRow margin="20px 0 0 0">
        <Button onClick={onClose} className="gray" disabled={deleteLoading}>
          Cancel
        </Button>
        <Button onClick={handleDeleteSelection} isLoading={deleteLoading} className="delete">
          Delete
        </Button>
      </FlexRow>
    </Modal>
  )
}

export default DeleteMediaModal
