import React, { useEffect, useState } from 'react'
import { SLoader } from '../../shared/components/loader/Loader'
import Modal from '../../shared/customModal/Modal'
import ShareLink from '../../shared/shareLink/ShareLink'
import { generateTokenUrl } from '../../logic/apis/media'
import { isSuccess } from '../../shared/helpers/util'

interface ShareMediaModalProps {
  setShowShareModal: (show: boolean) => void
  showShareModal: boolean
  setSelectedMedia: (media: any[]) => void

  selectedMedia: any[]
  mediaTypeFilters?: string[]
  tagTypeFilters?: string[]
  userTypeFilters?: string[]
  data: any
  oppId?: string
}

export function ShareMediaModal({
  setShowShareModal,
  setSelectedMedia,
  showShareModal,

  selectedMedia,
  mediaTypeFilters,
  tagTypeFilters,
  userTypeFilters,
  data,
  oppId,
}: ShareMediaModalProps) {
  const [urlHash, setUrlHash] = useState('')
  const [shareMediaLoader, setShareMediaLoader] = useState(true)

  const generateUrl = async () => {
    try {
      const res = await generateTokenUrl(oppId!, {
        imageIds: selectedMedia?.length ? selectedMedia : undefined,
        tags: selectedMedia?.length ? undefined : tagTypeFilters,
        types: selectedMedia?.length ? undefined : mediaTypeFilters,
        createdBy: selectedMedia?.length
          ? undefined
          : userTypeFilters?.map((name) => {
              const found = data?.images.find((item: any) => item.createdBy?.name === name)
              return found?.createdBy?._id || null
            }),
      })
      if (isSuccess(res)) {
        setUrlHash(res?.data?.data?.hash)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setShareMediaLoader(false)
    }
  }

  const handleClose = () => {
    if (!shareMediaLoader) {
      setShowShareModal(false)
      setUrlHash('')
      setSelectedMedia([])
    }
  }

  useEffect(() => {
    if (showShareModal) {
      generateUrl()
    }
  }, [showShareModal])

  return (
    <Modal title="Share Media" onClose={handleClose}>
      <h3>Generate a public shareable link</h3>
      {shareMediaLoader ? (
        <SLoader height={45} width={100} isPercent />
      ) : (
        <div style={{ width: '100%' }}>
          <ShareLink url={`/media/${urlHash}`} />
        </div>
      )}
    </Modal>
  )
}
