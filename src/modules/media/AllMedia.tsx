import JSZip from 'jszip'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import Delete from '../../assets/newIcons/delete.svg'
import DownloadSvg from '../../assets/newIcons/download.svg'
import FilterSvg from '../../assets/newIcons/filter.svg'
import PhotoReportIcon from '../../assets/newIcons/photoReport.svg'
import ShareIcon from '../../assets/newIcons/share.svg'
import TagIcon from '../../assets/newIcons/tag.svg'
import { getAllMedia } from '../../logic/apis/media'
import useFetch from '../../logic/apis/useFetch'
import { FullpageLoader, SLoader } from '../../shared/components/loader/Loader'
import Tag from '../../shared/components/tag'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { DropdownContainer, DropdownContent } from '../../shared/dropdownWithCheckboxes/style'
import { mimeTypesMap } from '../../shared/helpers/constants'
import { dayjsFormat } from '../../shared/helpers/util'
import { CenterContainer, FlexRow, Loader, TooltipContainer } from '../../styles/styled'
import { colors } from '../../styles/theme'
import { FilterContainer } from '../operations/style'
import { getSelectedFilters } from '../reports/productionReport/ProductionReport'
import CheckboxList from '../track/components/CheckboxList'
import MediaPreview from './components/MediaPreview'
import DeleteMediaModal from './DeleteMediaModal'
import { getCommonTags, MediaType, renderMedia } from './Media'
import MediaTagModal from './MediaTagModal'
import { ShareMediaModal } from './ShareMediaModal'
import { AllMediaCont, Timestamp, VideoCard, VideoGrid } from './style'
import UploadFile from './UploadFile'

const typeDropdown = [
  {
    name: 'Photos',
    _id: 'image',
  },
  {
    name: 'Videos',
    _id: 'video',
  },
  {
    name: 'Documents',
    _id: 'application',
  },
]

const MediaSkeleton = () => (
  <VideoCard>
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <SLoader
        width={100}
        height={200}
        isPercent
        skeletonStyle={{
          aspectRatio: '1/1',
          borderRadius: '8px',
        }}
      />
    </div>
  </VideoCard>
)

const MediaGridSkeleton = () => (
  <VideoGrid>
    {Array.from({ length: 15 }).map((_, index) => (
      <MediaSkeleton key={index} />
    ))}
  </VideoGrid>
)

const AllMedia = () => {
  const [showFilter, setShowFilter] = useState(false)
  const ref = useRef(null)
  const [showShareModal, setShowShareModal] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [uploadLoading, setUploadLoading] = useState(false)
  const [showTagModal, setShowTagModal] = useState(false)
  const navigate = useNavigate()

  const [skip, setSkip] = useState(1)

  const [selectedMedia, setSelectedMedia] = useState<any>([])
  const [currentMedia, setCurrentMedia] = useState<any>({})
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [downloadLoading, setDownloadLoading] = useState(false)

  const [typeSelected, setTypeSelected] = useState<any>({})
  const [tagSelected, setTagSelected] = useState<any>({})
  const [bool, setBool] = useState(false)
  const [userSelected, setUserSelected] = useState<any>({})
  const [hasLoaded, setHasLoaded] = useState(false)
  const [mediaID, setMediaID] = useState<string | null>(null)
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [selectedMediaData, setSelectedMediaData] = useState<any>([])
  const [allMediaData, setAllMediaData] = useState<any>([])

  const mediaTypeFilters = getSelectedFilters(typeSelected)
  const tagFilters = getSelectedFilters(tagSelected)
  const userFilters = getSelectedFilters(userSelected)
  const { company } = useSelector((state: any) => state)
  const memberId = company?.currentMember?._id
  const allowedMediaTypes = company?.companySettingForAll?.allowedMediaTypes ?? []
  const allowedMIMETypes = allowedMediaTypes.map((ext: string) => mimeTypesMap[ext] || '').filter(Boolean)

  const tagsData = useMemo(() => {
    return company?.companySettingForAll?.tags?.map((itm: any) => ({ name: itm, _id: itm }))
  }, [company?.companySettingForAll?.tags])

  const { data, loading } = useFetch({
    fetchFn: () =>
      getAllMedia({
        limit: 20,
        skip: skip,
        types: mediaTypeFilters?.length ? mediaTypeFilters?.join(',') : undefined,
        tags: tagFilters?.length ? tagFilters?.join(',') : undefined,
        createdBy: selectedMemberId?.length ? selectedMemberId?.join(',') : undefined,
      }),
    refetchTrigger: bool,
  })

  useEffect(() => {
    if (data?.media?.length) {
      setHasLoaded(true)
      // If skip is 1, it means we're starting fresh (after upload/delete/filter change)
      if (skip === 1) {
        setAllMediaData(data?.media)
      } else {
        // Otherwise, append to existing data for pagination
        setAllMediaData((prev: any) => [...prev, ...data?.media])
      }
    } else if (data?.media && data?.media?.length === 0 && skip === 1) {
      // Handle case where no media is returned on fresh load
      setHasLoaded(true)
      setAllMediaData([])
    }
  }, [data?.media, skip])

  // Clear data when hasLoaded is set to false (during refetch operations)
  useEffect(() => {
    if (!hasLoaded) {
      setAllMediaData([])
    }
  }, [hasLoaded])

  const uniqueValues = useMemo(() => {
    const usersMap = new Map()

    allMediaData?.forEach((type: any) => {
      if (type?.createdBy?._id && type?.createdBy?.name) {
        usersMap.set(type.createdBy._id, {
          _id: type.createdBy.name,
          name: type.createdBy.name,
          userId: type.createdBy._id,
        })
      }
    })

    return {
      users: Array.from(usersMap.values()), // Convert Map values to an array
    }
  }, [allMediaData?.length])

  var selectedMemberId = useMemo(() => {
    return uniqueValues.users?.filter((user) => userFilters.includes(user?._id))?.map((itm) => itm?.userId)
  }, [userFilters?.length])

  const handleDownloadClick = async () => {
    setDownloadLoading(true)
    const poName = `NHR`
    const zip = new JSZip()
    const folder = zip.folder(poName)

    await Promise.all(
      selectedMediaData?.map(async (url: string, index: number) => {
        try {
          const res = await fetch(`${url}?nocache=${Date.now()}`)
          const blob = await res.blob()
          const name = url.split('/').pop()?.split('-')?.[1] || `file-${index}`
          folder?.file(name, blob)
        } catch (err) {
          console.error('Error fetching:', url, err)
        }
      })
    )

    const zipBlob = await zip.generateAsync({ type: 'blob' })

    const url = URL.createObjectURL(zipBlob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${poName}-media-files.zip`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    setDownloadLoading(false)
  }

  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleFilterChange = (selectedItems: { [key: string]: boolean }, fn: any) => {
    fn(selectedItems)
    // Reset pagination and trigger refetch when filters change
    setSkip(1)
    setHasLoaded(false) // Show loading state while fetching filtered data
    setBool((p) => !p)
  }

  const handleCloseClick = (filter: any, fn: Function, arr: any) => {
    fn({
      ...arr,
      [filter]: false,
    })
    // Reset pagination and trigger refetch when filters change
    setSkip(1)
    setHasLoaded(false) // Show loading state while fetching filtered data
    setBool((p) => !p)
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  const handleMediaSelect = (media: any) => {
    let mediaId = media?._id
    setSelectedMedia((prev: any) => {
      const newSelection = new Set(prev)
      if (newSelection.has(mediaId)) {
        newSelection.delete(mediaId)
        setSelectedMediaData((p: any) => p?.filter((itm: any) => itm?._id !== mediaId))
      } else {
        newSelection.add(mediaId)
        setSelectedMediaData((p: any) => [...p, media?.url])
      }
      return [...newSelection]
    })
  }

  useEffect(() => {
    if (!ref.current) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const intersecting = entry.isIntersecting
          if (intersecting) {
            const button = document.getElementById('load')
            button?.click()
          }
        })
      },
      {
        threshold: 1,
      }
    )

    observer.observe(ref.current)

    return () => observer.disconnect()
  }, [ref?.current])

  const handleCreatePhotoReport = () => {
    const imageData = selectedMedia?.map((id: string) => {
      return allMediaData?.find((itm: any) => itm?._id === id && itm?.mimetype?.includes('image'))
    })

    if (imageData?.length) {
      navigate(`/photo-report`, {
        state: {
          imageData: imageData?.filter(Boolean),
        },
      })
    }
  }

  return (
    <>
      <AllMediaCont>
        <FlexRow margin="30px 0 16px 0" justifyContent="space-between" alignItems="flex-end" className="filter-cont">
          <FlexRow alignItems="flex-end">
            {uploadLoading ? (
              <div>
                <FullpageLoader />
              </div>
            ) : (
              <div style={{ width: '20px', height: '24px' }}>
                <TooltipContainer
                  width="70px"
                  positionLeft="0px"
                  positionBottom="0px"
                  positionLeftDecs="30px"
                  positionBottomDecs="unset"
                  positionTopDecs="-40px"
                  fontSize="14px"
                >
                  <span className="tooltip-content">Upload</span>
                  <UploadFile
                    memberId={memberId!}
                    company={company!}
                    allowedMIMETypes={allowedMIMETypes}
                    setUploadLoading={setUploadLoading}
                    onComplete={() => {
                      // Reset pagination and trigger refetch after upload
                      setSkip(1)
                      setAllMediaData([])
                      setHasLoaded(false) // Show loading state while fetching new data
                      setBool((p) => !p)
                    }}
                  />
                </TooltipContainer>
              </div>
            )}
            <TooltipContainer
              width="120px"
              positionLeft="0px"
              positionBottom="0px"
              positionLeftDecs="40px"
              positionBottomDecs="unset"
              positionTopDecs="-40px"
              fontSize="14px"
            >
              <span className="tooltip-content">Delete Media</span>
              {selectedMedia?.length ? (
                <img
                  src={Delete}
                  alt="select icon"
                  style={{ width: '20px', cursor: 'pointer' }}
                  onClick={() => {
                    setShowDeleteModal(true)
                  }}
                />
              ) : null}
            </TooltipContainer>
            {/* <TooltipContainer
              width="120px"
              positionLeft="0px"
              positionBottom="0px"
              positionLeftDecs="10px"
              positionBottomDecs="unset"
              positionTopDecs="-40px"
              fontSize="14px"
            >
              <span className="tooltip-content">Share Media</span>
              {selectedMedia?.length ? (
                <img
                  src={ShareIcon}
                  alt="share icon"
                  style={{ width: '20px', cursor: 'pointer' }}
                  onClick={() => {
                    setShowShareModal(true)
                  }}
                />
              ) : null}
            </TooltipContainer> */}
            <TooltipContainer
              width="150px"
              positionLeft="0px"
              positionBottom="0px"
              positionLeftDecs="10px"
              positionBottomDecs="unset"
              positionTopDecs="-40px"
              fontSize="14px"
            >
              <span className="tooltip-content">Add/Remove Tags</span>
              {selectedMedia?.length ? (
                <img
                  src={TagIcon}
                  alt="tag icon"
                  style={{ width: '20px', cursor: 'pointer' }}
                  onClick={() => {
                    const dataItems = allMediaData?.filter((itm: any) => selectedMedia?.includes(itm?._id))
                    const tags = getCommonTags(dataItems)
                    setSelectedTags(tags)
                    setShowTagModal(true)
                  }}
                />
              ) : null}
            </TooltipContainer>

            {downloadLoading ? (
              <div className="download-loader">
                <FullpageLoader />
              </div>
            ) : (
              <TooltipContainer
                width="100px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Download</span>
                {selectedMedia?.length ? (
                  <img
                    src={DownloadSvg}
                    alt="download icon"
                    style={{
                      width: '20px',
                      height: '20px',
                      cursor: 'pointer',
                    }}
                    onClick={handleDownloadClick}
                  />
                ) : null}
              </TooltipContainer>
            )}

            {selectedMedia?.length ? (
              <TooltipContainer
                width="160px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Create Photo Report</span>

                <img
                  src={PhotoReportIcon}
                  alt="photo-report-icon"
                  style={{ width: '20px', height: '20px', cursor: 'pointer' }}
                  onClick={() => {
                    handleCreatePhotoReport()
                  }}
                />
              </TooltipContainer>
            ) : null}
          </FlexRow>
          <FlexRow className="tag-cont" justifyContent="space-between">
            <FlexRow className="tags">
              {mediaTypeFilters?.map((itm) => (
                <Tag
                  itm={{ name: MediaType[itm], type: 'Type' }}
                  onClose={() => handleCloseClick(itm, setTypeSelected, typeSelected)}
                  showRemoveIcon
                  key={itm}
                />
              ))}
              {tagFilters?.map((itm) => (
                <Tag
                  itm={{ name: itm, type: 'Tag' }}
                  onClose={() => handleCloseClick(itm, setTagSelected, tagSelected)}
                  showRemoveIcon
                  key={itm}
                />
              ))}
              {userFilters?.map((itm) => (
                <Tag
                  itm={{ name: itm, type: 'User' }}
                  onClose={() => handleCloseClick(itm, setUserSelected, userSelected)}
                  showRemoveIcon
                  key={itm}
                />
              ))}
              <p></p>
            </FlexRow>
            <DropdownContainer ref={dropdownRef} className="filter">
              <img
                src={FilterSvg}
                className="filter-icon"
                alt="filter icon"
                style={{ width: '20px', cursor: 'pointer' }}
                onClick={() => setShowFilter((p) => !p)}
              />
              {showFilter ? (
                <DropdownContent style={{ width: '280px', right: '0px' }}>
                  <h3>Filter by</h3>
                  <FilterContainer
                    margin="10px 0 0 0"
                    gap="0px"
                    justifyContent="flex-start"
                    onClick={(e) => e.stopPropagation()}
                    className="media-filter"
                  >
                    <CheckboxList
                      className="first"
                      title=""
                      data={[]}
                      checkedItems={{}}
                      onSelectionChange={(_val) => {
                        // Reset pagination and trigger refetch when clearing all filters
                        setSkip(1)
                        setHasLoaded(false) // Show loading state while fetching all data
                        setBool((p) => !p)
                        setTypeSelected([])
                        setTagSelected([])
                        setUserSelected([])
                      }}
                      allText="All"
                      isCheckedAll={!mediaTypeFilters?.length && !tagFilters?.length && !userFilters?.length}
                    />
                    <CheckboxList
                      title="Type"
                      className="first"
                      data={typeDropdown}
                      checkedItems={typeSelected}
                      onSelectionChange={(val) => {
                        handleFilterChange(val, setTypeSelected)
                      }}
                      hideAllCheckbox
                    />
                    <CheckboxList
                      title="People"
                      data={uniqueValues.users}
                      checkedItems={userSelected}
                      onSelectionChange={(val) => {
                        handleFilterChange(val, setUserSelected)
                      }}
                      hideAllCheckbox
                    />
                    <CheckboxList
                      checkedItems={tagSelected}
                      title="Tags"
                      data={tagsData}
                      hideAllCheckbox
                      onSelectionChange={(val) => {
                        handleFilterChange(val, setTagSelected)
                      }}
                    />
                  </FilterContainer>
                </DropdownContent>
              ) : null}
            </DropdownContainer>
          </FlexRow>
        </FlexRow>
        <>
          {loading && !hasLoaded ? (
            <MediaGridSkeleton />
          ) : !allMediaData?.length ? (
            <CenterContainer>
              <FlexRow justifyContent="center">
                <h1>No data found</h1>
              </FlexRow>
            </CenterContainer>
          ) : (
            <VideoGrid>
              {allMediaData?.map((media: any) => (
                <VideoCard key={media?._id} className={selectedMedia.includes(media?._id) ? 'isSelected' : ''}>
                  <div
                    style={{ width: '100%', height: '100%', position: 'relative' }}
                    // onClick={() => {
                    //   setSelectedMedia(media)
                    //   setShowPreview(true)
                    // }}
                    onClick={() => {
                      setCurrentMedia(media)
                      setShowPreview(true)
                    }}
                    onMouseEnter={() => {
                      // if (media?.mimetype?.split('/')?.[0] === 'image') {
                      setMediaID(media?._id)
                      // }
                    }}
                    onMouseLeave={() => {
                      // if (media?.mimetype?.split('/')?.[0] === 'image') {
                      setMediaID(null)
                      // }
                    }}
                  >
                    {renderMedia(media?.url, media?.mimetype)}
                    {mediaID === media?._id || selectedMedia?.includes(media?._id) ? (
                      <input
                        type="checkbox"
                        checked={selectedMedia?.includes(media?._id)}
                        onChange={() => handleMediaSelect(media)}
                        style={{
                          cursor: 'pointer',
                        }}
                        // onChange={() => handleMediaSelect(media?._id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    ) : null}
                    <Timestamp className="all-media">
                      <div className="fileName">
                        <TooltipContainer
                          width="220px"
                          positionLeft="0px"
                          positionBottom="0px"
                          positionLeftDecs="100px"
                          positionBottomDecs="20px"
                          positionTopDecs="-40px"
                          fontSize="14px"
                        >
                          <span className="tooltip-content">{media?.name}</span>
                          <span className="name">{media?.name}</span>
                        </TooltipContainer>{' '}
                        <TooltipContainer
                          width="160px"
                          positionLeft="0px"
                          positionBottom="0px"
                          positionLeftDecs="40px"
                          positionBottomDecs="20px"
                          positionTopDecs="-40px"
                          fontSize="14px"
                        >
                          <span className="tooltip-content">{dayjsFormat(media.createdAt, 'hh:mma M/D/YY')}</span>
                          <span
                            style={{
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {dayjsFormat(media.createdAt, 'hh:mma M/D')}
                          </span>
                        </TooltipContainer>
                      </div>
                    </Timestamp>
                  </div>
                </VideoCard>
              ))}
            </VideoGrid>
          )}
        </>
        <FlexRow ref={ref} justifyContent="center" margin="30px 0" className="loaderCont">
          {allMediaData?.length >= data?.pagination?.totalItems || !allMediaData?.length ? null : (
            <Loader
              onClick={() => {
                // setLimit(data?.media?.length + 20)

                setSkip((p) => p + 1)
                setBool((p) => !p)
              }}
              id="load"
              color={colors.darkGrey}
              width="22px"
              height="22px"
            />
          )}
        </FlexRow>
        {showPreview ? (
          <MediaPreview
            allMedia={[
              {
                ...currentMedia,
                user: currentMedia?.createdBy?.name,
                id: currentMedia?.createdBy?.userImageUrl?._id,
                userImage: currentMedia?.createdBy?.userImageUrl?.imageUrl,
                imageId: currentMedia?._id,
              },
            ]}
            selectedIndex={0}
            isMediaSection
            // isShareView
            isAllMedia
            allTags={company?.companySettingForAll?.tags}
            onClose={() => {
              setCurrentMedia({})
              setShowPreview(false)
            }}
            info={{
              po: data?.PO,
              clientName: `${data?.firstName || ''} ${data?.lastName || ''}`,
            }}
            onSuccess={() => {
              // Reset pagination and trigger refetch after media preview actions
              setSkip(1)
              setHasLoaded(false) // Show loading state while fetching new data
              setBool((p) => !p)
            }}
          />
        ) : null}
      </AllMediaCont>

      <CustomModal show={showDeleteModal} className="media">
        <DeleteMediaModal
          selectedMedia={selectedMedia}
          onClose={() => {
            setShowDeleteModal(false)
          }}
          onComplete={() => {
            setShowDeleteModal(false)
            // Reset pagination and trigger refetch after delete
            setSkip(1)
            setHasLoaded(false) // Show loading state while fetching new data
            setBool((p) => !p)
            setSelectedMedia([])
          }}
        />
      </CustomModal>

      <CustomModal show={showShareModal} className="media">
        <ShareMediaModal
          selectedMedia={selectedMedia}
          mediaTypeFilters={mediaTypeFilters}
          data={data}
          setShowShareModal={setShowShareModal}
          showShareModal={showShareModal}
          setSelectedMedia={setSelectedMedia}
        />
      </CustomModal>

      <CustomModal show={showTagModal} className="media overflow">
        <MediaTagModal
          onClose={() => {
            setShowTagModal(false)
          }}
          onComplete={() => {
            // Reset pagination and trigger refetch after tag update
            setSkip(1)
            setHasLoaded(false) // Show loading state while fetching new data
            setBool((p) => !p)
            setShowTagModal(false)
            setSelectedTags([])
            setSelectedMedia([])
          }}
          selectedTags={selectedTags}
          setSelectedTags={setSelectedTags}
          tagsData={{ mediaSetting: company?.companySettingForAll }}
          selectedMedia={selectedMedia}
        />
      </CustomModal>
    </>
  )
}

export default AllMedia
