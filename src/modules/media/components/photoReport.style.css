:root {
  --gray1: #666666;
  --grey1: #666666;
  --lightGrey: #cccccc;
  --border: rgba(106, 116, 126, 0.2);
}

.caption {
  margin-top: 5px;
  padding: 5px;
  height: 60px;
  border: none;
  outline: 1px solid var(--border);
  resize: none;
  font-size: 14px;
  border-radius: 4px;
  text-align: center;
}

.report-page {
  width: 8.5in;
  height: 11in;
  margin: 0 auto;
  padding: 0.5in;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  background: white;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.page-break {
  page-break-before: always;
}
.page-break-one {
  page-break-before: auto;
}

.no-break {
  break-inside: avoid;
  page-break-inside: avoid;
  overflow: hidden;
}

.print-view {
  .logo-section {
    margin-right: 0px;
  }

  width: 100% !important;
  padding: 0px !important;
  height: 90%;
  margin-bottom: 0px !important;
}
@media print {
  .caption {
    outline: none;
  }

  .caption::placeholder {
    color: transparent;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--border);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.header p {
  color: var(--gray1);
  font-size: 14px;
}

.logo-section {
  width: 36%;
  margin-right: 16px;
  display: flex;
  align-items: flex-end;
}

.logo-section img {
  max-width: 100%;
  height: auto;
}

.title-section {
  text-align: left;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.editable-title {
  font-size: 24px;
  font-weight: bold;
  text-align: left;
  border: none;
  width: 100%;
  margin-top: 1px;
  outline: 1px solid var(--lightGrey);
  border-radius: 4px;
}

.editable-date {
  all: unset;
  width: max-content;
  font-size: 14px;
  text-align: left;
  color: var(--gray1);
  outline: 1px solid var(--lightGrey);
  border-radius: 4px;
}

/* Firefox workaround */
.editable-date[type='date'] {
  -moz-appearance: textfield;
}

.editable-date[type='date']:hover {
  -moz-appearance: auto;
}

.prepared-by {
  text-transform: capitalize;
}

.client-section {
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.client-section p {
  white-space: nowrap;
}

.photo-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  flex: 1;
  height: 200px;
}

.photo-grid > * {
  min-height: 0;
  min-width: 0;
}

.photo-item {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: flex-end;
}

.photo-container {
  width: 100%;
  height: 350px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  overflow: hidden;
}

.photo {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.page-number {
  text-align: right;
  margin-top: auto; /* Reduced margin */
  font-size: 12px;
  color: var(--gray1);
}

.caption-text {
  font-size: 14px;
  margin-top: 6px;
  text-align: center;
  color: var(--gray1);
  height: 60px;
}
