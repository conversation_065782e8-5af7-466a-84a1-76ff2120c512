import styled from 'styled-components'
import { FlexRow } from '../../../styles/styled'
import { screenSizes } from '../../../styles/theme'
import { rgba } from 'polished'

export const ViewerContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow: hidden; /* Prevents horizontal scrolling */

  .info {
    text-align: center;
    max-width: 90%;
    p {
      color: #efefef;
      font-size: 20px;
      margin-top: 20px;
      margin-bottom: 10px;
      word-wrap: break-word; /* Prevents text from causing overflow */
    }

    b {
      margin-right: 10px;
    }
  }

  @media (max-width: 768px) {
    padding: 10px;
    overflow-x: hidden;
    width: 100%;
    height: 100%;

    .info {
      p {
        font-size: 16px;
        margin-top: 10px;
      }
    }
  }
`

export const UserInfoCont = styled.div`
  height: fit-content;
  h1 {
    color: #efefef;
    font-size: 20px;
  }
  p {
    font-size: 16px;
    color: ${rgba('#efefef', 0.8)};
  }

  img {
    width: 50px;
    height: 50px;
  }
  position: absolute;
  left: 20px;
  bottom: 30px;
  background: rgba(0, 0, 0, 0.9);
  padding: 10px;
  border-radius: 8px;

  @media (max-width: 768px) {
    h1 {
      font-size: 16px;
    }
    p {
      font-size: 14px;
    }
    img {
      width: 40px;
      height: 40px;
    }
    left: 10px;
    padding: 8px;

    bottom: unset;
    top: 10px;
  }
`

export const MainImage = styled.div<{ $image?: string }>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  background-image: url(${(props) => props.$image});
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  height: 100%;
  max-width: 100vw; /* Prevents overflow */
  width: 100%;

  .preview-image {
    /* max-width: 700px; */
    width: 96vw;
    margin: 0 auto;
    aspect-ratio: 2/1;
    object-fit: contain;
  }

  @media (max-width: 768px) {
    background-size: cover;
    background-position: center;

    .preview-image {
      max-width: 90vw;
      width: 100%;
      height: auto;
    }
  }
`
export const NavigationButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;

  z-index: 10; /* Add this to ensure it's on top */
  padding: 0; /* Reset any default browser padding */
  pointer-events: auto; /* Ensure it's clickable */

  &:hover {
    background: rgba(255, 255, 255, 1);
  }

  @media (max-width: 768px) {
    width: 30px;
    height: 30px;
  }
`

export const PrevButton = styled(NavigationButton)`
  left: 20px;
  font-size: 36px;

  &.tag {
    right: 80px;
    top: 40px;
    left: unset;
  }

  @media (max-width: 768px) {
    left: 10px;
    font-size: 24px;
    z-index: 200;

    &.tag {
      right: 60px;
      top: 30px;
    }
  }
`

export const NextButton = styled(NavigationButton)`
  right: 20px;
  font-size: 36px;

  @media (max-width: 768px) {
    right: 10px;
    font-size: 24px;
    z-index: 200;
  }
`

export const CloseButton = styled(NavigationButton)`
  top: 20px;
  right: 20px;
  transform: none;
  font-size: 36px;
  z-index: 1;

  @media (max-width: 768px) {
    top: 10px;
    right: 10px;
    font-size: 24px;
  }
`

export const RemoveButton = styled(NavigationButton)`
  top: 80px;
  right: 20px;
  transform: none;
  color: red;
  font-size: 36px;
  background: none;
  &:hover {
    background: none;
  }

  @media (max-width: 768px) {
    top: 60px;
    right: 10px;
    font-size: 24px;
  }
`

export const ZoomButton = styled(NavigationButton)`
  bottom: 20px;
  right: 20px;
  top: auto;
  transform: none;

  @media (max-width: 768px) {
    bottom: 10px;
    right: 10px;
  }
`

export const ThumbnailStrip = styled.div`
  height: 100px;
  background: #000;
  display: flex;
  gap: 4px;
  padding: 10px;
  overflow-x: auto;

  @media (max-width: 768px) {
    height: 80px;
    padding: 8px;
  }
`

export const Thumbnail = styled.div<{ $image: string; $isActive: boolean }>`
  width: 120px;
  height: 80px;
  flex-shrink: 0;
  background-image: url(${(props) => props.$image});
  background-size: cover;
  background-position: center;
  cursor: pointer;
  opacity: ${(props) => (props.$isActive ? 1 : 0.6)};
  transition: opacity 0.2s;

  &:hover {
    opacity: 1;
  }

  @media (max-width: 768px) {
    width: 80px;
    height: 60px;
  }
`

export const FloatingBar = styled(FlexRow)`
  border: 1px solid black;
  width: max-content;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 30px;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px;
  border-radius: 10px;

  .reset {
    position: relative;
    top: unset;
    right: unset;
    left: unset;
    bottom: unset;
  }

  @media (max-width: 768px) {
    bottom: 20px;
    padding: 6px;
    left: 50%;
  }
`

export const IconButton = styled.button`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s;

  &:hover {
    background: rgba(255, 255, 255, 1);
  }

  svg {
    color: white;
    font-size: 24px;
  }

  @media (max-width: 768px) {
    width: 30px;
    height: 30px;

    svg {
      font-size: 20px;
    }
  }
`

export const FullscreenContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #282828;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;

  .tui-image-editor-container .tui-image-editor-header-logo {
    display: none;
  }

  .tui-image-editor-header-buttons {
    display: none;
  }

  .loader {
    border-color: white;
    border-bottom-color: transparent;
  }

  button {
    position: absolute;
    top: 60px;
    right: 20px;

    @media (min-width: ${screenSizes.M}px) {
      top: 20px;
    }
  }

  .delete {
    right: 120px;
  }
`

export const EditorWrapper = styled.div`
  width: 90%;
  height: 90%;
`

export const GpsMediaCont = styled.section`
  overflow-y: hidden;
  #maps {
    height: calc(100vh - 114px) !important;
  }
`

export const InfoWindowContent = styled.div`
  width: 220px;
  text-align: center;

  p {
    font-size: 14px;
    margin: 4px 0;
  }

  .name {
    margin: unset;
  }
`

export const AvatarCont = styled(FlexRow)`
  width: max-content;
  margin: 0 auto;
  align-items: center;
  img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }

  .name-cont {
    width: 25px;

    span {
      font-size: 10px;
    }
  }
`
