import { useControls } from 'react-zoom-pan-pinch'
import { SmallLoaderCont } from '../../../shared/components/loader/style'
import { TooltipContainer } from '../../../styles/styled'
import { FloatingBar, IconButton } from './style'
import Tags from '../../../assets/newIcons/tag.svg'
import Delete from '../../../assets/newIcons/delete.svg'
import EditSvg from '../../../assets/newIcons/edit.svg'
import ZoomIn from '../../../assets/newIcons/zoomIn.svg'
import ZoomOut from '../../../assets/newIcons/zoomOut.svg'
import ZoomReset from '../../../assets/newIcons/zoomReset.svg'
import ShareIcon from '../../../assets/newIcons/share.svg'
import DownloadIcon from '../../../assets/newIcons/download.svg'
import { useState } from 'react'
import { isSuccess, notify } from '../../../shared/helpers/util'
import { getDownloadUrl } from '../../../logic/apis/media'

const FloatingActionButton = ({
  onDeleteClick,
  onTagClick,
  onEditClick,
  onZoomInClick,
  onZoomOutClick,
  isShareView,
  onZoomResetClick,
  mediaType,
  editLoading,
  isAllMedia,
  oppId,
  mediaUrl,
}: any) => {
  const [downloadLoading, setDownloadLoading] = useState(false)

  const onDownloadClick = async () => {
    try {
      setDownloadLoading(true)
      const fileName = mediaUrl.split('/').pop()?.split('-')?.[1]
      // const response = await fetch(mediaUrl)
      // const blob = await response.blob()
      // const objectUrl = URL.createObjectURL(blob)

      const objectKey = mediaUrl.split('.com/')[1]

      const downloadResponse = await getDownloadUrl(objectKey)

      if (isSuccess(downloadResponse)) {
        const url = downloadResponse?.data?.data?.downloadUrl

        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        // Cleanup
        link.remove()
        // URL.revokeObjectURL(objectUrl)
      }
    } catch (error) {
      notify('Errorrr', 'error')
      console.error('Error downloading image:', error)
    } finally {
      setDownloadLoading(false)
    }
  }
  return (
    <FloatingBar gap="20px" className={isShareView ? 'pdf-media' : ''}>
      {isShareView ? null : (
        <>
          <TooltipContainer
            width="120px"
            positionLeft="0px"
            positionBottom="0px"
            positionLeftDecs="10px"
            positionBottomDecs="unset"
            positionTopDecs="-40px"
            fontSize="14px"
          >
            <span className="tooltip-content">Delete Media</span>
            <IconButton type="button" onClick={onDeleteClick}>
              <img src={Delete} alt="delete icon" />
            </IconButton>
          </TooltipContainer>
          <TooltipContainer
            width="80px"
            positionLeft="0px"
            positionBottom="0px"
            positionLeftDecs="10px"
            positionBottomDecs="unset"
            positionTopDecs="-40px"
            fontSize="14px"
          >
            <span className="tooltip-content">Add Tags</span>
            <IconButton type="button" onClick={onTagClick}>
              <img
                src={Tags}
                style={{
                  width: '30px',
                }}
                alt="tag icon"
              />
            </IconButton>
          </TooltipContainer>

          {mediaType === 'image' ? (
            <>
              <TooltipContainer
                width="80px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Edit</span>
                <IconButton type="button" onClick={onEditClick}>
                  {editLoading ? <SmallLoaderCont className="black" /> : <img src={EditSvg} alt="edit icon" />}
                </IconButton>
              </TooltipContainer>
              <TooltipContainer
                width="80px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Download</span>
                {/* <a href={mediaUrl} download target="_blank"> */}
                <IconButton type="button" onClick={onDownloadClick} disabled={downloadLoading}>
                  {downloadLoading ? (
                    <SmallLoaderCont className="black" />
                  ) : (
                    <img src={DownloadIcon} alt="download icon" />
                  )}
                </IconButton>
                {/* </a> */}
              </TooltipContainer>
            </>
          ) : null}
        </>
      )}

      {mediaType === 'video' || mediaType === 'image' ? (
        <>
          <TooltipContainer
            width="80px"
            positionLeft="0px"
            positionBottom="0px"
            positionLeftDecs="10px"
            positionBottomDecs="unset"
            positionTopDecs="-40px"
            fontSize="14px"
          >
            <span className="tooltip-content">Zoom In</span>
            <IconButton
              type="button"
              onClick={() => {
                onZoomInClick?.()
              }}
            >
              <img
                src={ZoomIn}
                style={{
                  width: '30px',
                }}
                alt="ZoomIn icon"
              />
            </IconButton>
          </TooltipContainer>

          <>
            <TooltipContainer
              width="80px"
              positionLeft="0px"
              positionBottom="0px"
              positionLeftDecs="10px"
              positionBottomDecs="unset"
              positionTopDecs="-40px"
              fontSize="14px"
            >
              <span className="tooltip-content">Zoom out</span>
              <IconButton
                type="button"
                onClick={() => {
                  onZoomOutClick()
                }}
              >
                <img
                  src={ZoomOut}
                  style={{
                    width: '30px',
                  }}
                  alt="ZoomOut icon"
                />
              </IconButton>
            </TooltipContainer>
            <TooltipContainer
              width="120px"
              positionLeft="0px"
              positionBottom="0px"
              positionLeftDecs="10px"
              positionBottomDecs="unset"
              positionTopDecs="-40px"
              fontSize="14px"
            >
              <span className="tooltip-content">Reset zoom</span>
              <IconButton
                type="button"
                onClick={() => {
                  onZoomResetClick?.()
                }}
              >
                <img
                  src={ZoomReset}
                  style={{
                    width: '30px',
                  }}
                  alt="ZoomReset icon"
                />
              </IconButton>
            </TooltipContainer>
          </>
        </>
      ) : null}

      {isAllMedia ? (
        <>
          {/* {mediaType === 'image' ? (
            <TooltipContainer
              width="80px"
              positionLeft="0px"
              positionBottom="0px"
              positionLeftDecs="10px"
              positionBottomDecs="unset"
              positionTopDecs="-40px"
              fontSize="14px"
            >
              <span className="tooltip-content">Download</span>

              <IconButton type="button" onClick={onDownloadClick} disabled={downloadLoading}>
                {downloadLoading ? (
                  <SmallLoaderCont className="black" />
                ) : (
                  <img src={DownloadIcon} alt="download icon" />
                )}
              </IconButton>
            </TooltipContainer>
          ) : null}
          <TooltipContainer
            width="80px"
            positionLeft="0px"
            positionBottom="0px"
            positionLeftDecs="10px"
            positionBottomDecs="unset"
            positionTopDecs="-40px"
            fontSize="14px"
          >
            <span className="tooltip-content">Add Tags</span>
            <IconButton type="button" onClick={onTagClick}>
              <img
                src={Tags}
                style={{
                  width: '30px',
                }}
                alt="tag icon"
              />
            </IconButton>
          </TooltipContainer> */}

          {oppId ? (
            <TooltipContainer
              width="160px"
              positionLeft="0px"
              positionBottom="0px"
              positionLeftDecs="10px"
              positionBottomDecs="unset"
              positionTopDecs="-40px"
              fontSize="14px"
            >
              <span className="tooltip-content">View opportunity</span>
              <IconButton
                type="button"
                onClick={() => {
                  window.open(`/sales/opportunity/${oppId}/media`, '_blank')
                }}
              >
                <img
                  src={ShareIcon}
                  style={{
                    width: '30px',
                  }}
                  alt="share icon"
                />
              </IconButton>
            </TooltipContainer>
          ) : null}
        </>
      ) : null}
    </FloatingBar>
  )
}

export default FloatingActionButton

export const WithZoomFeatures = (Component: any) => {
  return (props: any) => {
    const { zoomIn, zoomOut, resetTransform } = useControls()
    return (
      <Component
        {...props}
        onZoomInClick={zoomIn}
        onZoomOutClick={zoomOut}
        onZoomResetClick={() => {
          props?.onZoomResetClick?.()
          resetTransform()
        }}
      />
    )
  }
}
