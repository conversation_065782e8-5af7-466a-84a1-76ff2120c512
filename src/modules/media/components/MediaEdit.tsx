import React, { useEffect, useRef, useState } from 'react'
import styled from 'styled-components'
import ImageEditor from 'tui-image-editor'
import 'tui-image-editor/dist/tui-image-editor.css'
import 'tui-color-picker/dist/tui-color-picker.css'
import Button from '../../../shared/components/button/Button'
import { base64ToBlob, extractImageData, getThumbnailUrl } from '../mediaUtils'
import useImageUpload from '../../../shared/hooks/useImageUpload'
import { FilePathTypeEnum } from '../../../shared/helpers/constants'
import { Loader } from '../../../styles/styled'
import { FullpageLoader } from '../../../shared/components/loader/Loader'
import { EditorWrapper, FullscreenContainer } from './style'
import { createMediaOpportunity, FilePayload } from '../../../logic/apis/media'
import { generateUUID, isSuccess, notify } from '../../../shared/helpers/util'
import { useParams } from 'react-router-dom'

function addEditedToFilename(filename: string) {
  const parts = filename?.split('.')

  if (parts.length > 1) {
    const extension = parts?.pop()

    const name = parts?.join('.') + '-edited'

    return `${name}.${extension}`
  } else {
    return `${filename}-edited`
  }
}

interface IEditMedia {
  imageUrl: string
  imageName: string
  onClose: () => void
  onEditSuccess: () => void
  currentImageData?: any
  memberId?: string
}

const EditMedia = ({ imageUrl, imageName, onClose, currentImageData, memberId, onEditSuccess }: IEditMedia) => {
  const editorRef = useRef(null)
  const imageEditorInstance = useRef<any>(null)
  const [uploadLoading, setUploadLoading] = useState(false)
  const { uploadImage, error, imageUrl: uploadedUrl, loading } = useImageUpload()
  const { oppId } = useParams()

  useEffect(() => {
    if (uploadedUrl) {
      const updateOppMedia = async () => {
        const payload: FilePayload[] = []
        payload.push({
          name: addEditedToFilename(imageName),
          _id: generateUUID()!,
          mimetype: 'image/jpg',
          url: uploadedUrl,
          thumbnail: getThumbnailUrl(uploadedUrl),
          location: currentImageData?.location,
          createdAt: currentImageData?.createdAt || new Date().toISOString(),
          createdBy: memberId!,
        })

        const uploadRes = await createMediaOpportunity(oppId!, payload)

        if (isSuccess(uploadRes)) {
          notify(uploadRes?.data?.data?.message, 'success')
          setUploadLoading(false)
          onEditSuccess()
        }
      }

      updateOppMedia()
    }
  }, [uploadedUrl])

  useEffect(() => {
    if (editorRef.current && imageUrl) {
      try {
        imageEditorInstance.current = new ImageEditor(editorRef.current, {
          includeUI: {
            loadImage: {
              path: imageUrl,
              name: addEditedToFilename(imageName),
            },
            menu: ['crop', 'flip', 'rotate', 'draw', 'shape', 'text'],
            initMenu: 'draw',
            uiSize: {
              width: '100%',
              height: '100%',
            },
            menuBarPosition: 'bottom',
          },
          cssMaxWidth: window.innerWidth,
          cssMaxHeight: window.innerHeight,
          selectionStyle: {
            cornerSize: 20,
            rotatingPointOffset: 70,
          },
          usageStatistics: false,
        })
      } catch (error) {
        console.log('error===[err]===>', error)
      }
    }

    return () => {
      if (imageEditorInstance.current) {
        imageEditorInstance.current.destroy()
      }
    }
  }, [imageUrl])

  const handleSave = () => {
    if (imageEditorInstance.current) {
      setUploadLoading(true)
      const image = imageEditorInstance.current.toDataURL()

      uploadImage(base64ToBlob(image), FilePathTypeEnum.Project, {
        fileName: addEditedToFilename(imageName),
        mimeType: 'image/jpg',
      })
    }
  }

  const handleCloseClick = () => {
    const confirm = window.confirm('Are you sure you want to close? Your current edit changes will be lost.') ?? false
    if (confirm) {
      onClose()
    }
  }

  return (
    <FullscreenContainer>
      <EditorWrapper ref={editorRef} />
      <Button width="max-content" onClick={handleSave} isLoading={uploadLoading}>
        Save
      </Button>
      <Button width="max-content" onClick={handleCloseClick} disabled={uploadLoading} className="delete">
        Close
      </Button>
    </FullscreenContainer>
  )
}

export default EditMedia
