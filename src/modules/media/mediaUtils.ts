// @ts-ignore
import exifr from 'exifr/dist/full.esm'
import { notify } from '../../shared/helpers/util'
import imageCompression from 'browser-image-compression'

export const extractImageData = async (mediaArray: { name: string; url: string }[]) => {
  try {
    const promises = mediaArray.map((media) => exifr.parse(media.url, true))
    const res = await Promise.all(promises)

    console.log('Parsed data======>', res)
    const imageMetaData = mediaArray.reduce<Record<string, any>>((acc, media, index) => {
      acc[media.name] = {
        ...media,
        createdAt: res[index]?.CreateDate ? new Date(res[index]?.CreateDate).toISOString() : new Date().toISOString(),
        location: res[index]?.latitude
          ? {
              type: 'Point',
              coordinates: [res[index]?.latitude, res[index]?.longitude],
            }
          : undefined,
      }
      return acc
    }, {})

    return imageMetaData
  } catch (error) {
    console.error('Error extracting image metadata:', error)
    return {}
  }
}

export const validateFiles = (fileList: any[], constraints: any) => {
  return fileList.filter((file) => {
    const fileSizeMB = file?.file?.size / (1024 * 1024)

    console.log('fileSizeMB======>', fileSizeMB)

    if (file?.mimetype?.includes('image') && fileSizeMB > constraints.maxImageSizeMB) {
      notify(`Image ${file.name} exceeds the maximum allowed size of ${constraints.maxImageSizeMB}MB`, 'error')
      return false
    }
    if (file?.mimetype?.includes('video') && fileSizeMB > constraints.maxVideoSizeMB) {
      notify(`Video ${file.name} exceeds the maximum allowed size of ${constraints.maxVideoSizeMB}MB`, 'error')
      return false
    }
    return true
  })
}

export const validateFormFiles = (fileList: any[], constraints: any) => {
  return fileList.filter((file) => {
    const fileSizeMB = file?.size / (1024 * 1024)

    if (file?.type?.includes('image') && fileSizeMB > constraints.maxImageSizeMB) {
      notify(`Image ${file.name} exceeds the maximum allowed size of ${constraints.maxImageSizeMB}MB`, 'error')
      return false
    }
    if (file?.type?.includes('video') && fileSizeMB > constraints.maxVideoSizeMB) {
      notify(`Video ${file.name} exceeds the maximum allowed size of ${constraints.maxVideoSizeMB}MB`, 'error')
      return false
    }
    if (file?.type?.includes('audio') && fileSizeMB > constraints.maxAudioSizeMB) {
      notify(`Audio ${file.name} exceeds the maximum allowed size of ${constraints.maxAudioSizeMB}MB`, 'error')
      return false
    }
    return true
  })
}

export const getThumbnailUrl = (imageUrl: string) => {
  return imageUrl.replace(/(\.[^.]+)$/, '-thumbnail.jpg')
}

export const base64ToBlob = (base64: string, mimeType = 'image/jpg') => {
  const base64Data = base64.split(',')[1] || base64

  const byteCharacters = atob(base64Data)

  const byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  const byteArray = new Uint8Array(byteNumbers)

  return URL.createObjectURL(new Blob([byteArray], { type: mimeType }))
}

export const compressImage = async (file: File, options = {}) => {
  const defaultOptions = {
    maxSizeMB: 3,
    maxWidthOrHeight: 1280,
    useWebWorker: true,
  }

  try {
    const compressedFile = await imageCompression(file, {
      ...defaultOptions,
      ...options,
    })

    const compressedBlobUrl = URL.createObjectURL(compressedFile)

    return {
      file: compressedFile,
      url: compressedBlobUrl,
    }
  } catch (error) {
    console.error('Image compression failed:', error)
    throw error
  }
}
