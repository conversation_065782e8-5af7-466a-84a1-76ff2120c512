import { useEffect, useRef, useState } from 'react'
import { Form, Formik } from 'formik'
import * as Yup from 'yup'
import dayjs from 'dayjs'

import { getDepartments } from '../../logic/apis/department'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { TrackCont } from './style'
import { FlexCol, FlexRow } from '../../styles/styled'
import { SharedDate } from '../../shared/date/SharedDate'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import Button from '../../shared/components/button/Button'
import CheckboxList from './components/CheckboxList'
import { getPosition } from '../../logic/apis/position'
import { getDataFromLocalStorage, notify, startOfDate } from '../../shared/helpers/util'
import { getGpsData } from '../../logic/apis/gps'
import Maps from './Maps'
import { CaretSvg } from '../../shared/helpers/images'
import { useClickOutside } from '../../shared/hooks/useClickOutside'
import { StorageKey } from '../../shared/helpers/constants'
import MapWithoutCluster from './MapWithoutCluster'

interface Department {
  name: string
  _id: string
  position?: string
}

interface IGPSLocation {
  type: string
  location: [number, number, number, string][]
}

interface IGPSData {
  _id: string
  memberId: string
  timeCardId: string
  projectPO: string
  location: IGPSLocation
}

interface IData {
  memberId: string
  name: string
  data: IGPSData[]
}

const initialValues = {
  date: dayjs()?.format('YYYY-MM-DD'),
  startTime: '00:00',
  endTime: '23:59',
}

const ValidationSchema = Yup.object().shape({
  date: Yup.string().required('Required'),
  startTime: Yup.string().required('Required'),
  endTime: Yup.string().required('Required'),
})

const createTimeCardMap = (dataArray: any[]) =>
  dataArray.reduce((acc, item) => {
    item.data.forEach(({ timeCardId }: any) => {
      acc[timeCardId] = true
    })
    return acc
  }, {})

const Track = () => {
  const globalSelector = useAppSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [departmentData, setDepartmentData] = useState<Department[]>([])
  const [positionData, setPositionData] = useState([])
  const [loading, setLoading] = useState(false)
  const [showFilter, setShowFilter] = useState(false)
  const dropdownRef = useRef(null)
  useClickOutside(dropdownRef, setShowFilter)

  const [departmentSelected, setDepartmentSelected] = useState<{ [key: string]: boolean }>({})
  const [positionSelected, setPositionSelected] = useState<{ [key: string]: boolean }>({})
  const [gpsData, setGpsData] = useState<undefined | []>()
  const [timeCardIdObj, setTimeCardIdObj] = useState<{ [key: string]: boolean }>({})

  const handleDepartmentChange = (selectedItems: { [key: string]: boolean }) => {
    setDepartmentSelected(selectedItems)
  }
  const handlePositionChange = (selectedItems: { [key: string]: boolean }) => {
    setPositionSelected(selectedItems)
  }

  const fetchDepartments = async () => {
    const departmentResponse = await getDepartments({ limit: '100' }, false)
    setDepartmentData(
      departmentResponse?.data?.data?.department?.map((itm: Department) => ({
        name: itm?.name,
        _id: itm?._id,
      }))
    )
  }

  const fetchPositions = async () => {
    const response = await getPosition({ deleted: false, limit: '200' }, false)

    setPositionData(
      response?.data?.data?.position?.map((itm: Department) => ({
        name: itm?.position,
        _id: itm?._id,
      }))
    )
  }

  useEffect(() => {
    if (currentCompany?._id) {
      fetchDepartments()
      fetchPositions()
    }
  }, [currentCompany?._id])

  const generateCustomData = (arr: any[]) => {
    return arr?.map((item: any) => {
      return {
        _id: item._id,
        memberId: item.memberId,
        timeCardId: item.timeCardId,
        location: {
          type: 'MultiPoint',
          coordinates: [[Number(item.long), Number(item.lat), item?.activity, item.createdAt]],
        },
      }
    })
  }

  const handleSubmit = async (val: typeof initialValues) => {
    try {
      setLoading(true)

      const start = startOfDate(val?.date)
      const [startHours, startMins] = val?.startTime?.split(':')
      const [endHours, endMins] = val?.endTime?.split(':')

      const startTime = new Date(start).setHours(Number(startHours), Number(startMins), 0)
      const endTime = new Date(start).setHours(Number(endHours), Number(endMins), 0)

      const response = await getGpsData({
        endDate: new Date(endTime)?.toISOString(),
        startDate: new Date(startTime)?.toISOString(),
        filter: [
          ...Object?.keys(departmentSelected)?.filter((id) => departmentSelected[id]),
          ...Object?.keys(positionSelected)?.filter((id) => positionSelected[id]),
        ],
      })

      // Transform the data to a format compatible with the Maps component
      // Organize data by memberId first, then by timeCardId within each member
      const organizedByMember = response?.data?.data?.data?.map((item: IData) => {
        // Group data by timeCardId
        const timeCardGroups = item?.data?.reduce((acc: { [key: string]: IGPSData[] }, dataItem: IGPSData) => {
          const timeCardId = dataItem?.timeCardId
          if (!acc[timeCardId]) {
            acc[timeCardId] = []
          }
          acc[timeCardId].push(dataItem)
          return acc
        }, {})

        // Create a single entry for each member with paths organized by timeCardId
        return {
          name: item.name,
          memberId: item.memberId,
          timeCards: Object.entries(timeCardGroups)?.map(([timeCardId, dataItems]) => {
            return {
              timeCardId: timeCardId,
              timeCardName: dataItems?.[0]?.projectPO,
              path: dataItems?.map((dataItem: any) =>
                dataItem?.location?.map((coord: any) => ({
                  lat: coord[0], // Latitude is the second element in coordinates arraydataItem
                  lng: coord[1], // Longitude is the first element in coordinates array
                  activity: coord[2], // Activity is the third element
                  createdAt: coord[3], // Timestamp is the fourth element
                  memberId: item.memberId,
                  timeCardId: dataItem.timeCardId,
                }))
              )?.[0],
            }
          }),
        }
      })

      setTimeCardIdObj(createTimeCardMap(response?.data?.data?.data))

      setGpsData(organizedByMember)
    } catch (error) {
      console.error(error)
      notify('Error fetching GPS data', 'error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <TrackCont>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={ValidationSchema}
        enableReinitialize={true}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, setFieldValue, errors }) => {
          return (
            <FlexRow justifyContent="space-between" alignItems="flex-start" flexWrap="wrap">
              <FlexCol className="filter">
                <FlexCol
                  alignItems="center"
                  onClick={() => {
                    setShowFilter(!showFilter)
                  }}
                  ref={dropdownRef}
                >
                  <FlexRow>
                    <p>Team member filter</p>
                    <img className={showFilter ? 'rotate' : ''} src={CaretSvg} alt="chevron" />
                  </FlexRow>

                  {showFilter && (
                    <FlexCol gap="0px" justifyContent="flex-start" onClick={(e) => e.stopPropagation()}>
                      <CheckboxList
                        title="Departments"
                        data={departmentData}
                        checkedItems={departmentSelected}
                        onSelectionChange={handleDepartmentChange}
                      />
                      <CheckboxList
                        checkedItems={positionSelected}
                        title="Positions"
                        data={positionData}
                        onSelectionChange={handlePositionChange}
                      />
                    </FlexCol>
                  )}
                </FlexCol>
              </FlexCol>
              <Form>
                <FlexRow justifyContent="flex-end" alignItems="flex-start" width="max-content" className="input-filter">
                  <SharedDate
                    value={values?.date}
                    labelName="Date"
                    stateName="date"
                    setFieldValue={setFieldValue}
                    min={new Date()?.toISOString()}
                    minWidth="200px"
                    error={errors?.date ? true : false}
                  />

                  <InputWithValidation
                    labelName="Start time"
                    error={errors?.startTime ? true : false}
                    stateName="startTime"
                    type="time"
                    minWidth="150px"
                  />

                  <InputWithValidation
                    error={errors?.endTime ? true : false}
                    labelName="End time"
                    stateName="endTime"
                    type="time"
                    minWidth="150px"
                  />

                  <FlexRow margin="8px 0 0 0">
                    <Button width="max-content" height="52px" isLoading={loading}>
                      Submit
                    </Button>
                  </FlexRow>
                </FlexRow>
              </Form>
            </FlexRow>
          )
        }}
      </Formik>

      {!gpsData?.length && Array.isArray(gpsData) ? <h2>No location data found</h2> : null}
      {!!gpsData?.length && <Maps data={gpsData} timeCardIdObj={timeCardIdObj} />}
      {/* {!!gpsData?.length && <MapWithoutCluster data={gpsData} />} */}
    </TrackCont>
  )
}

export default Track
