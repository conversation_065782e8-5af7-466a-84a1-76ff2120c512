import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'

import { ISalesCommission, updateActivity } from '../../../../logic/apis/sales'
import {
  formatCurrencyToHandleDollar,
  getDataFromLocalStorage,
  isSuccess,
  notify,
} from '../../../../shared/helpers/util'
import * as SharedStyled from '../../../../styles/styled'
import { SalesOppCont } from '../../styles'
import { useState } from 'react'
import { StorageKey } from '../../../../shared/helpers/constants'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import Button from '../../../../shared/components/button/Button'
import { Field, Formik } from 'formik'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import CommissionModal from '../commissionModal/CommissionModal'

const validateInput = (value: string, cb: React.Dispatch<React.SetStateAction<string>>) => {
  const [, decimal] = value.split('.')
  if (decimal?.length >= 3) {
    return
  }
  cb(value)
}

const SalesCommission = ({
  oppData,
  initFetch,
  setAddCommission,
  setCommissionEdit,
  salesCommissionData,
  setCommissionUpdate,
}: {
  oppData: Record<string, any>
  initFetch: (bool: boolean) => void
  setAddCommission: React.Dispatch<React.SetStateAction<boolean>>
  setCommissionEdit: React.Dispatch<React.SetStateAction<boolean>>
  salesCommissionData: never[]
  setCommissionUpdate: React.Dispatch<React.SetStateAction<{}>>
}) => {
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  // const [addCommission, setAddCommission] = useState(false)

  const [totalCommission, setTotalCommission] = useState(oppData?.salesCommission?.total?.toFixed(2) || '')
  const [saleCommission, setSaleCommission] = useState(oppData?.salesCommission?.sale?.toFixed(2) || '')
  const [startCommission, setStartCommission] = useState(oppData?.salesCommission?.start?.toFixed(2) || '')
  const [completedCommission, setCompletedCommission] = useState(oppData?.salesCommission?.completed?.toFixed(2) || '')
  console.log({ salesCommissionData })
  // const handleValue = async (data: Partial<ISalesCommission>, resetCallback: () => void, activityString?: string) => {
  //   const res = await updateSalesCommission(
  //     {
  //       opportunityId: oppData?._id,
  //       companyId: currentCompany?._id,
  //       ...data,
  //     },
  //   )
  //   if (isSuccess(res)) {
  //     notify(res?.data?.data?.message, 'success')
  //     if (activityString) {
  //       await updateActivity(
  //         {
  //           id: oppData?._id!,
  //           memberId: currentMember._id!,
  //           body: activityString,
  //           companyId: currentCompany._id,
  //           currDate: new Date().toISOString(),
  //         },
  //       )
  //     }
  //     setAddCommission(false)
  //     initFetch(false)
  //   } else {
  //     resetCallback()
  //   }
  // }
  const totalAmount = salesCommissionData.reduce((sum: number, data: any) => {
    return sum + (data?.amount || 0) // Default to 0 if amount is undefined
  }, 0)
  return (
    <>
      <SalesOppCont>
        {oppData?.salesCommission && Object.values(oppData?.salesCommission)?.filter(Boolean)?.length ? (
          <>
            <SharedStyled.FlexBox width="100%" alignItems="center">
              <p>
                Commission -{' '}
                <span
                  style={{ cursor: 'pointer', color: '#02a4ff', textTransform: 'lowercase' }}
                  onClick={() => {
                    setAddCommission(true)
                    setCommissionEdit(false)
                  }}
                  className="edit-address"
                >
                  modify
                </span>
              </p>
              <span>${(oppData?.salesCommission?.total + (totalAmount || 0))?.toFixed(2)}</span>
              {/* <span className="bold">
                $
                <input
                  className="commission-input"
                  type="number"
                  defaultValue={oppData?.salesCommission?.total?.toFixed(2)}
                  value={totalCommission}
                  onChange={(e) => {
                    validateInput(e.target.value, setTotalCommission)
                  }}
                  onBlur={(e) => {
                    if (Number(totalCommission) !== oppData?.salesCommission?.total)
                      handleValue(
                        { totalCommission: Number(totalCommission) },
                        () => setTotalCommission(oppData?.salesCommission?.total),
                        `changed Total Commission from $${oppData?.salesCommission?.total} to $${totalCommission}`
                      )
                  }}
                />
              </span> */}
            </SharedStyled.FlexBox>

            <SharedStyled.FlexCol className="nested" gap="4px">
              {!!oppData?.salesCommission?.sale && (
                <SharedStyled.FlexBox width="100%" alignItems="center">
                  <p>Time of Sale</p>
                  <div>${oppData?.salesCommission?.sale?.toFixed(2)}</div>
                  {/* <div>
                    $
                    <input
                      type="number"
                      onChange={(e) => {
                        validateInput(e.target.value, setSaleCommission)
                      }}
                      value={saleCommission}
                      defaultValue={oppData?.salesCommission?.sale}
                      onBlur={(e) => {
                        if (Number(saleCommission) !== oppData?.salesCommission?.sale)
                          handleValue(
                            { saleCommission: Number(saleCommission) },
                            () => setSaleCommission(oppData?.salesCommission?.sale),
                            `changed Time of Sale Commission from $${oppData?.salesCommission?.sale} to $${saleCommission}`
                          )
                      }}
                    />
                  </div> */}
                </SharedStyled.FlexBox>
              )}
              {!!oppData?.salesCommission?.start && (
                <SharedStyled.FlexBox width="100%" alignItems="center">
                  <p>Job Start</p>
                  <div>${oppData?.salesCommission?.start?.toFixed(2)}</div>
                  {/* <div>
                    $
                    <input
                      type="number"
                      defaultValue={oppData?.salesCommission?.start}
                      value={startCommission}
                      onChange={(e) => {
                        validateInput(e.target.value, setStartCommission)
                      }}
                      onBlur={(e) => {
                        if (Number(startCommission) !== oppData?.salesCommission?.start)
                          handleValue(
                            { startCommission: Number(startCommission) },
                            () => setStartCommission(oppData?.salesCommission?.start),
                            `changed Job Start Commission from $${oppData?.salesCommission?.start} to $${startCommission}`
                          )
                      }}
                    />
                  </div> */}
                </SharedStyled.FlexBox>
              )}
              {!!oppData?.salesCommission?.completed && (
                <SharedStyled.FlexBox width="100%" alignItems="center">
                  <p>Job Completion</p>
                  <div>${oppData?.salesCommission?.completed?.toFixed(2)}</div>
                  {/* <div>
                    $
                    <input
                      type="number"
                      value={completedCommission}
                      onChange={(e) => {
                        validateInput(e.target.value, setCompletedCommission)
                      }}
                      defaultValue={oppData?.salesCommission?.completed}
                      onBlur={(e) => {
                        if (Number(completedCommission) !== oppData?.salesCommission?.completed)
                          handleValue(
                            { completedCommission: Number(completedCommission) },
                            () => setCompletedCommission(oppData?.salesCommission?.completed),
                            `changed Job Completion Commission from $${oppData?.salesCommission?.completed} to $${completedCommission}`
                          )
                      }}
                    />
                  </div> */}
                </SharedStyled.FlexBox>
              )}

              {salesCommissionData?.length ? (
                <>
                  {salesCommissionData.map((data: any, index: number) => {
                    return (
                      <SharedStyled.FlexBox
                        onClick={() => {
                          setCommissionUpdate(data)
                          setAddCommission(true)
                          setCommissionEdit(true)
                        }}
                        style={{ cursor: 'pointer' }}
                        width="100%"
                        alignItems="center"
                        key={index}
                      >
                        <p>Modification</p>
                        <div>{formatCurrencyToHandleDollar(data?.amount, 2)}</div>
                      </SharedStyled.FlexBox>
                    )
                  })}
                </>
              ) : null}
            </SharedStyled.FlexCol>
          </>
        ) : null}

        {/* <CustomModal show={addCommission}>
          <CommissionModal setAddCommission={setAddCommission} handleValue={handleValue} />
        </CustomModal> */}
      </SalesOppCont>
    </>
  )
}

export default SalesCommission
