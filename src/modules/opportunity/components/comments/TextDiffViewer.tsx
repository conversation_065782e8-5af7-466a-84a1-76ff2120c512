// @ts-ignore
import DiffMatchPatch from 'diff-match-patch'

const dmp = new DiffMatchPatch()

const getFormattedDiff = (original: string, edited: string) => {
  const diffs = dmp.diff_main(original, edited)
  dmp.diff_cleanupSemantic(diffs)

  return diffs.map(([type, text]: any, index: number) => {
    switch (type) {
      case DiffMatchPatch.DIFF_INSERT:
        return (
          <span key={index} style={{ backgroundColor: '#d4fcdc', color: '#22863a' }}>
            {text}
          </span>
        )
      case DiffMatchPatch.DIFF_DELETE:
        return (
          <span key={index} style={{ backgroundColor: '#fcdcdc', color: '#b31d28', textDecoration: 'line-through' }}>
            {text}
          </span>
        )
      case DiffMatchPatch.DIFF_EQUAL:
      default:
        return <span key={index}>{text}</span>
    }
  })
}

const TextDiffViewer = ({ original = '', edited = '' }) => {
  return <div style={{ whiteSpace: 'pre-wrap', padding: '10px' }}>{getFormattedDiff(original, edited)}</div>
}

export default TextDiffViewer
