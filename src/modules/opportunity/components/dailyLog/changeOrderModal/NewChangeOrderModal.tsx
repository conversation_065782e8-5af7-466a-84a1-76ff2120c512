import { useSelector } from 'react-redux'
import { use<PERSON>arams } from 'react-router-dom'
import { Field, Form, Formik } from 'formik'
import React, { useEffect, useRef, useState } from 'react'
import * as Yup from 'yup'

import * as Styled from './style'
import { CrossIcon } from '../../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../../assets/newIcons/unitModal.svg'
import * as SharedStyled from '../../../../../styles/styled'
import { InputWithValidation } from '../../../../../shared/inputWithValidation/InputWithValidation'
import { addChangeOrder, updateChangeOrder } from '../../../../../logic/apis/sales'
import {
  getDataFromLocalStorage,
  getTaxJurisdictionRateFromId,
  handleWheel,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../../shared/helpers/util'
import Button from '../../../../../shared/components/button/Button'
import { Nue, StorageKey } from '../../../../../shared/helpers/constants'
import { CheckDivContainer } from '../../../../taskSettings/components/createTaskPopUp/style'
import Toggle from '../../../../../shared/toggle/Toggle'
import { SharedDate } from '../../../../../shared/date/SharedDate'
import { getOrderById, getPriceIdByProject } from '../../../../../logic/apis/projects'
import { getTasks } from '../../../../../logic/apis/task'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import { SLoader } from '../../../../../shared/components/loader/Loader'
import { onlyTwoNumbersAfterDecimal } from '../../../../../shared/helpers/regex'

interface I_ConfirmationPopUp {
  setShowChangeOrder: React.Dispatch<React.SetStateAction<boolean>>
  onClose: () => void
  header: string
  inputData?: any
  isEdit: boolean
  data: any
  tax: any
  setOppData: any
  salesPersonId: string
  onComplete?: any
  priceId?: string
}
interface InitialValues {
  name: string
  description: string
  rawMatCost?: number
  materials: number
  labor: number
  addCost: number
  jobCost: number
  tax: number
  signedBySales?: boolean
  isSubContractor?: boolean
  date?: string
  total: number
  mMarkup?: number
  workTask?: string
  manHours?: number
  rawLaborCost?: number
  overrideTotalCost?: boolean
  manHourCost?: number
}

const ValidationSchema = Yup.object().shape({
  name: Yup.string().required('Required'),
  date: Yup.string().required('Required'),
})

const handleDecimal = (val: string, fieldName: string, fn: (field: string, value: any) => void) => {
  const value = val
  if (value) {
    if (onlyTwoNumbersAfterDecimal.test(value)) {
      fn(fieldName, Number(value))
    }
  } else {
    fn(fieldName, '')
  }
}

const NewChangeOrderModal = (props: I_ConfirmationPopUp) => {
  const {
    setShowChangeOrder,
    header,
    onClose,
    inputData,
    isEdit,
    data,
    tax,
    salesPersonId,
    setOppData,
    onComplete,
    priceId,
  } = props

  const globalSelector = useSelector((state: any) => state)
  const { oppId } = useParams()
  const { currentCompany, currentMember } = globalSelector.company
  const [loading, setLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteFlag, setDeleteFlag] = useState(false)
  const [restoreFlag, setRestoreFlag] = useState(false)
  const [addMarkup, setAddMarkup] = useState(inputData?.markupEnabled || false)
  const [priceData, setPriceData] = useState<any>({})
  const [projectPriceData, setProjectPriceData] = useState<any>({})
  const [workTaskData, setWorkTaskData] = useState([])
  const [allWorkTaskData, setAllWorkTaskData] = useState([])
  const [selectedWorkTask, setSelectedWorkTask] = useState<any>({})
  const [priceLoading, setPriceLoading] = useState(false)
  const [totalLaborCalculated, setTotalLaborCalculated] = useState(inputData?.labor ?? 0)
  const [additionalCost, setAdditionalCost] = useState(0)
  const [totalMaterialCost, setTotalMaterialCost] = useState(inputData?.materials ?? 0)

  const prevValue = useRef(0)

  const [initialValues, setInitialValues] = useState<InitialValues>({
    name: inputData ? inputData?.name : '',
    description: inputData ? inputData?.description : '',
    materials: inputData?.materials ?? 0,
    rawMatCost: inputData?.rawMatCost ?? 0,
    labor: inputData?.labor ?? 0,
    rawLaborCost: (inputData?.isSubContractor ? inputData?.manHourCost : inputData?.rawLaborCost) ?? 0,
    addCost: inputData?.addCost ?? 0,
    jobCost: inputData?.jobCost ?? 0,
    tax: inputData?.tax ?? 0,
    total: inputData?.total ?? 0,
    signedBySales: inputData ? inputData?.signedBySales : false,
    date: inputData?.date ? inputData?.date : '',
    mMarkup: 0,
    workTask: '',
    manHours: 0,
    overrideTotalCost: inputData ? inputData?.overrideTotalCost : false,
    manHourCost: 0,
  })

  useEffect(() => {
    if (Object?.values(inputData)?.length) {
      setInitialValues((pre) => ({ ...pre, ...inputData }))
    }
  }, [inputData])

  useEffect(() => {
    const getPriceData = async () => {
      setPriceLoading(true)
      try {
        // const res = await getOrderById(currentCompany._id, false, data?.orderId!)
        const workTaskResponse = await getTasks({}, false)

        const response: any = await getPriceIdByProject(priceId!)
        setPriceData(response?.data?.data?.price?.variables)
        setProjectPriceData(response?.data?.data?.price?.projectType)
        setAllWorkTaskData(workTaskResponse?.data?.data?.workTask)
        const workData = workTaskResponse?.data?.data?.workTask
          ?.filter((wt: { pieceWork: boolean }) => wt?.pieceWork)
          ?.map((item: any) => `${item.name} ($${item.rate || 0}/Hr) :-${item._id}`)
        setWorkTaskData(workData)
      } catch (error) {
        console.log({ error })
      } finally {
        setPriceLoading(false)
      }
    }

    getPriceData()
  }, [data?.orderId])

  console.log({ inputData })
  const handleSubmit = async (val: typeof initialValues) => {
    try {
      restoreFlag ? setDeleteLoading(true) : deleteFlag ? setDeleteLoading(true) : setLoading(true)
      const objData = {
        ...val,
        num: inputData?.num ? inputData.num : data?.changeOrders?.length ? data?.changeOrders?.length + 1 : 1,
        deleted: restoreFlag ? false : deleteFlag ? true : false,
        // changeOrderValue: data?.changeOrderValue ?? 0,
        // changeOrderRRValue: data?.changeOrderRRValue ?? 0,
        oldJobCost: restoreFlag ? 0 : inputData?.jobCost ?? 0,
        oldMaterials: restoreFlag ? 0 : inputData?.materials ?? 0,
        date: val?.date ? startOfDate(val?.date) : '',
        addCost: additionalCost || 0,
        markupEnabled: addMarkup,
        manHours: val?.manHours || 0,
        rawMatCost: val?.rawMatCost || 0,
        rawLaborCost: val?.rawLaborCost || 0,
        mMarkup: addMarkup ? val?.mMarkup : 0,
        workTaskValue: inputData?.workTaskValue || selectedWorkTask?.rate,
        isSubContractor: val?.isSubContractor || false,
        labor: totalLaborCalculated || 0,
        materials: Number(val?.mMarkup)! + Number(val?.rawMatCost)! || 0,
        signedBySales: val?.total < 0 ? true : val?.signedBySales || false,
        overrideTotalCost: val?.overrideTotalCost ?? false,
      }

      if (val?.isSubContractor) {
        objData.manHourCost = objData.rawLaborCost
        objData.labor = 0
        delete objData.rawLaborCost
        delete objData.manHours
        delete objData.workTask
        delete objData.laborBurden
      }

      if (isEdit) {
        const res = await updateChangeOrder(objData, oppId!)
        if (isSuccess(res)) {
          setOppData((pre: any) => ({ ...pre, changeOrders: res?.data?.data?.data }))
          notify(res?.data?.data?.message, 'success')
          setLoading(false)
          setDeleteLoading(false)
          setShowChangeOrder(false)
          console.log({ res }, res?.data?.data?.data)
          onComplete?.()
        }
        setLoading(false)
        setDeleteLoading(false)
      } else {
        const res = await addChangeOrder(objData, oppId!)
        if (isSuccess(res)) {
          setOppData((pre: any) => ({ ...pre, changeOrders: res?.data?.data?.data }))
          notify(res?.data?.data?.message, 'success')
          setLoading(false)
          setShowChangeOrder(false)
          console.log({ res }, res?.data?.data?.data)
          onComplete?.()
        }
        setLoading(false)
        setDeleteLoading(false)
      }
    } catch (error) {
      setLoading(false)
      setDeleteLoading(false)
      console.log(error)
    }
  }

  return (
    <Styled.ConfirmationContainer>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        enableReinitialize={true}
        validateOnChange={true}
        validationSchema={ValidationSchema}
        validateOnBlur={false}
      >
        {({ values, errors, touched, dirty, setFieldValue }) => {
          const calculateJobCost = (values: any) => {
            const { materials, labor, addCost } = values
            return (Number(materials) || 0) + (Number(labor) || 0) + (Number(addCost) || 0)
          }

          const rate = inputData?.workTaskValue || selectedWorkTask?.rate || 0

          const totalMat = Number(values?.rawMatCost) + Number(values.mMarkup) || 0

          const manHourWorkTask = Number(values?.manHours) * rate + Number(values?.rawLaborCost)
          const laborBurden = Number((manHourWorkTask * priceData?.ttlBurden)?.toFixed(2)) || 0
          const manHourCost = Number(values?.manHours) * rate

          useEffect(() => {
            // if (manHourCost) {
            setFieldValue('manHourCost', manHourCost)
            // }
          }, [manHourCost])

          useEffect(() => {
            // if (laborBurden) {
            setFieldValue('laborBurden', laborBurden)
            // }
          }, [laborBurden])

          const totalLabor =
            (values?.isSubContractor
              ? Number(values?.rawLaborCost) || inputData?.labor
              : Number((manHourWorkTask + laborBurden)?.toFixed(2))) || 0

          useEffect(() => {
            if (dirty || (dirty && !!totalMat)) setTotalMaterialCost(totalMat)
          }, [totalMat])

          useEffect(() => {
            if (dirty || (dirty && !!totalLabor)) setTotalLaborCalculated(totalLabor)
          }, [totalLabor])

          let totalOverHead = values?.overrideTotalCost
            ? Number(values?.jobCost) - (totalLabor + totalMat) * (1 + projectPriceData?.markup)
            : (priceData?.modOH + priceData?.modP) * totalLabor * (1 + projectPriceData?.markup)

          const totalCOP = values?.overrideTotalCost ? Number(values?.jobCost) : totalMat + totalLabor + totalOverHead

          useEffect(() => {
            if (totalOverHead) setAdditionalCost(totalOverHead)
          }, [totalOverHead])

          useEffect(() => {
            const newJobCost = totalCOP

            // setFieldValue('jobCost', Number(Number(newJobCost)?.toFixed(2))) // Update the "Job Cost" in the formik values
            setFieldValue(
              'tax',
              Number(
                Number((Number(newJobCost) * getTaxJurisdictionRateFromId(data?.taxJurisdiction, tax)) / 100)?.toFixed(
                  2
                )
              )
            ) // Update the "Job Cost" in the formik values

            setFieldValue('jobCost', Number(totalCOP?.toFixed(2)))
          }, [values.rawMatCost, values.rawLaborCost, values.addCost, setFieldValue, totalCOP])

          const calculateTotal = (values: any) => {
            const { materials, labor, addCost, tax } = values
            return (Number(materials) || 0) + (Number(labor) || 0) + (Number(addCost) || 0) + (Number(tax) || 0)
          }
          useEffect(() => {
            const newTotal = totalCOP + values?.tax
            setFieldValue('total', Number(Number(newTotal)?.toFixed(2))) // Update the "Total" in the formik values
          }, [values.jobCost, values.tax, totalCOP, setFieldValue])

          useEffect(() => {
            if (priceData?.matMarkup && addMarkup) {
              setFieldValue('mMarkup', Number((values?.rawMatCost! * priceData?.matMarkup)?.toFixed(2)))
            }

            if (!addMarkup) {
              setFieldValue('mMarkup', 0)
            }
          }, [values?.rawMatCost, priceData?.matMarkup, addMarkup])

          // useEffect(() => {
          //   if (addMarkup) {
          //     prevValue.current = values?.rawMatCost!
          //     setFieldValue('rawMatCost', values?.rawMatCost! * (1 + priceData?.matMarkup))
          //   } else {
          //     setFieldValue('rawMatCost', prevValue.current)
          //   }
          // }, [addMarkup])

          useEffect(() => {
            if (values?.total < 0) {
              setFieldValue('signedBySales', true)
            }
          }, [values?.total])

          useEffect(() => {
            if (workTaskData?.length && !isEdit) {
              const task = (workTaskData?.[0] as string)?.split(':-')[1]
              setFieldValue('workTask', (workTaskData?.[0] as string)?.split(':-')?.[0]?.trim())

              setSelectedWorkTask(allWorkTaskData?.filter((itm: any) => itm?._id === task)?.[0])
            }
          }, [workTaskData])

          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{header}</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    setShowChangeOrder(false)
                    onClose()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <Form className="form">
                <Styled.ModalBodyContainer>
                  <SharedStyled.FlexRow justifyContent="space-between">
                    <CheckDivContainer>
                      <Toggle
                        customStyles={{
                          flexDirection: 'row-reverse',
                          width: 'max-content',
                          gap: '20px',
                        }}
                        title="Signed by Salesperson"
                        isToggled={values?.signedBySales!}
                        onToggle={() => {
                          setFieldValue('signedBySales', !values?.signedBySales)
                        }}
                      />
                    </CheckDivContainer>
                  </SharedStyled.FlexRow>

                  <SharedDate
                    value={values.date}
                    labelName="Date:"
                    stateName="date"
                    error={touched.date && errors.date ? true : false}
                    setFieldValue={setFieldValue}
                  />
                  <div>
                    {/* <SharedStyled.Text fontSize="18px" fontWeight="700">
                      Name: <SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                    </SharedStyled.Text> */}
                    <InputWithValidation
                      labelName="Name"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                    />
                  </div>
                  {/* <div>
                    <SharedStyled.Text fontSize="18px" fontWeight="700">
                      Describe work to be done: <SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                    </SharedStyled.Text>
                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      name="description"
                      marginTop="8px"
                      height="52px"
                    ></Styled.TextArea>
                  </div> */}

                  <Styled.TextArea
                    component="textarea"
                    placeholder="Description of work to be completed"
                    as={Field}
                    name="description"
                    marginTop="8px"
                    height="70px"
                  />

                  <div>
                    <SharedStyled.FlexRow margin="6px 0" justifyContent="space-between">
                      <SharedStyled.Text fontSize="18px" fontWeight="700">
                        Materials
                      </SharedStyled.Text>

                      <Button
                        type="button"
                        onClick={() => setAddMarkup((p) => !p)}
                        width="max-content"
                        className={addMarkup ? 'delete' : 'success'}
                        padding="6px 12px"
                      >
                        {addMarkup ? 'Remove' : 'Add'} Markup
                      </Button>
                    </SharedStyled.FlexRow>

                    <SharedStyled.FlexRow>
                      {priceLoading ? (
                        <>
                          <SLoader height={52} />
                          <SLoader height={52} />
                        </>
                      ) : (
                        <>
                          <InputWithValidation
                            labelName="Raw material cost"
                            stateName="rawMatCost"
                            forceType="number"
                            isCurrency
                            onChange={(e: any) => handleDecimal(e.target.value, 'rawMatCost', setFieldValue)}
                            defaultValue={values?.rawMatCost}
                            error={touched.rawMatCost && errors.rawMatCost ? true : false}
                          />
                          {/* <div style={{ pointerEvents: 'none' }}> */}
                          <InputWithValidation
                            labelName="Material markup"
                            stateName="mMarkup"
                            forceType="number"
                            isCurrency
                            disabled
                            defaultValue={values?.mMarkup}
                            error={touched.mMarkup && errors.mMarkup ? true : false}
                          />
                        </>
                      )}
                      {/* </div> */}
                    </SharedStyled.FlexRow>
                    {/* <Styled.NameValueUnitContainer>
                      <Styled.UnitDiv>$</Styled.UnitDiv>
                      <Styled.PercentValueInput
                        onWheel={handleWheel}
                        width={'100%'}
                        name="materials"
                        type="number"
                        borderRadius="0px 4px 4px 0px"
                      />
                    </Styled.NameValueUnitContainer> */}
                  </div>

                  <div>
                    <SharedStyled.FlexRow margin="6px 0 16px 0">
                      <SharedStyled.Text fontSize="18px" fontWeight="700">
                        Labor
                      </SharedStyled.Text>
                    </SharedStyled.FlexRow>

                    <SharedStyled.FlexRow margin="10px 0 0 0">
                      <SharedStyled.Text
                        fontSize="16px"
                        fontWeight="500"
                        color="#121619"
                        style={{ fontFamily: Nue.medium }}
                      >
                        Crew
                      </SharedStyled.Text>
                      <CheckDivContainer>
                        <Toggle
                          customStyles={{
                            flexDirection: 'row-reverse',
                            width: 'max-content',
                            gap: '10px',
                          }}
                          title="Sub"
                          isToggled={values?.isSubContractor!}
                          onToggle={() => {
                            setFieldValue('isSubContractor', !values?.isSubContractor)

                            setFieldValue('manHours', '')
                            setFieldValue('workTask', '')
                            setFieldValue('rawLaborCost', '')
                            setFieldValue('laborBurden', '')
                          }}
                          className="order"
                        />
                      </CheckDivContainer>
                    </SharedStyled.FlexRow>
                    {/* <Styled.NameValueUnitContainer>
                      <Styled.UnitDiv>$</Styled.UnitDiv>
                      <Styled.PercentValueInput
                        onWheel={handleWheel}
                        width={'100%'}
                        name="labor"
                        type="number"
                        borderRadius="0px 4px 4px 0px"
                      />
                    </Styled.NameValueUnitContainer> */}
                  </div>
                  {/* <div>
                    <SharedStyled.Text fontSize="18px" fontWeight="700">
                      Additional Cost:
                    </SharedStyled.Text>
                    <Styled.NameValueUnitContainer>
                      <Styled.UnitDiv>$</Styled.UnitDiv>
                      <Styled.PercentValueInput
                        onWheel={handleWheel}
                        width={'100%'}
                        name="addCost"
                        type="number"
                        borderRadius="0px 4px 4px 0px"
                      />
                    </Styled.NameValueUnitContainer>
                  </div> */}

                  {values?.isSubContractor ? (
                    <InputWithValidation
                      labelName="Total labor cost"
                      stateName="rawLaborCost"
                      forceType="number"
                      onChange={(e: any) => handleDecimal(e.target.value, 'rawLaborCost', setFieldValue)}
                      isCurrency
                      defaultValue={values?.rawLaborCost}
                      error={touched.rawLaborCost && errors.rawLaborCost ? true : false}
                    />
                  ) : (
                    <SharedStyled.FlexCol>
                      <SharedStyled.FlexRow>
                        <InputWithValidation
                          labelName="Man-hours"
                          stateName="manHours"
                          forceType="number"
                          onChange={(e: any) => handleDecimal(e.target.value, 'manHours', setFieldValue)}
                          error={touched.manHours && errors.manHours ? true : false}
                        />{' '}
                        <CustomSelect
                          labelName="Work Task"
                          error={touched.workTask && errors.workTask ? true : false}
                          value={values.workTask?.trim()}
                          dropDownData={workTaskData.map((item: any) => item.split(':-')[0]?.trim())}
                          setValue={(val: string) => {
                            const task = workTaskData
                              ?.find((itm: string) => itm?.split(':-')[0]?.trim() === val)
                              ?.split(':-')[1]
                              ?.trim()

                            setSelectedWorkTask(allWorkTaskData?.filter((itm: any) => itm?._id === task)?.[0])
                          }}
                          setFieldValue={setFieldValue}
                          testId={() => {}}
                          dropDownDataForId={workTaskData}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                          stateName="workTask"
                          selectedIdName={`workTask`}
                        />
                      </SharedStyled.FlexRow>
                      <SharedStyled.FlexRow>
                        <InputWithValidation
                          labelName="Raw labor cost"
                          stateName="rawLaborCost"
                          forceType="number"
                          isCurrency
                          onChange={(e: any) => handleDecimal(e.target.value, 'rawLaborCost', setFieldValue)}
                          defaultValue={values?.rawLaborCost}
                          error={touched.rawLaborCost && errors.rawLaborCost ? true : false}
                        />{' '}
                        <InputWithValidation
                          labelName="Labor burden"
                          stateName="laborBurden"
                          forceType="number"
                          disabled
                          isCurrency
                          defaultValue={values?.laborBurden}
                        />
                      </SharedStyled.FlexRow>
                    </SharedStyled.FlexCol>
                  )}

                  <SharedStyled.FlexRow margin="6px 0">
                    <SharedStyled.Text fontSize="18px" fontWeight="700">
                      Summary
                    </SharedStyled.Text>
                  </SharedStyled.FlexRow>

                  <SharedStyled.FlexCol width="max-content" gap="4px" className="cost-breakdown">
                    <SharedStyled.FlexRow>
                      <span>Materials:</span>
                      <span>${totalMaterialCost?.toFixed(2) || '0.00'}</span>
                    </SharedStyled.FlexRow>
                    <SharedStyled.FlexRow>
                      <span>Labor:</span>
                      <span>${totalLaborCalculated?.toFixed(2) || '0.00'}</span>
                    </SharedStyled.FlexRow>
                    <SharedStyled.FlexRow>
                      <span>Overhead:</span>
                      <span>${isNaN(totalOverHead) ? '0.00' : totalOverHead?.toFixed(2)}</span>
                    </SharedStyled.FlexRow>

                    <SharedStyled.FlexRow style={{ borderTop: '1px solid black' }}>
                      <span>Change Order Price:</span>
                      <span>${isNaN(totalCOP) ? '0.00' : totalCOP?.toFixed(2)}</span>
                    </SharedStyled.FlexRow>
                    <SharedStyled.FlexRow>
                      <span>Sales Tax:</span>
                      <span>${(values?.tax || 0)?.toFixed(2)}</span>
                    </SharedStyled.FlexRow>
                    <SharedStyled.FlexRow style={{ borderTop: '1px solid black' }} className="total">
                      <span>TOTAL:</span>
                      <span>${(values?.total || 0)?.toFixed(2)}</span>
                    </SharedStyled.FlexRow>
                  </SharedStyled.FlexCol>

                  <CheckDivContainer>
                    <Toggle
                      customStyles={{
                        width: 'max-content',
                        gap: '10px',
                      }}
                      title="Override Total Cost"
                      isToggled={values?.overrideTotalCost!}
                      onToggle={() => {
                        setFieldValue('overrideTotalCost', !values?.overrideTotalCost)
                      }}
                    />
                  </CheckDivContainer>

                  {values?.overrideTotalCost ? (
                    <InputWithValidation
                      labelName="Change Order Price"
                      stateName="jobCost"
                      forceType="number"
                      onChange={(e: any) => handleDecimal(e.target.value, 'jobCost', setFieldValue)}
                      isCurrency
                      defaultValue={values?.jobCost}
                      error={touched.jobCost && errors.jobCost ? true : false}
                    />
                  ) : null}

                  {/* <div>
                    <SharedStyled.Text fontSize="18px" fontWeight="700">
                      Job Cost:
                    </SharedStyled.Text>
                    <Styled.NameValueUnitContainer>
                      <Styled.UnitDiv>$</Styled.UnitDiv>
                      <Styled.PercentValueInput
                        onWheel={handleWheel}
                        width={'100%'}
                        // value={values.materials + values.labor + values.addCost}
                        name="jobCost"
                        type="number"
                        borderRadius="0px 4px 4px 0px"
                        disabled={true}
                      />
                    </Styled.NameValueUnitContainer>
                  </div> */}
                  {/* <div>
                    <SharedStyled.Text fontSize="18px" fontWeight="700">
                      Sales Tax:
                    </SharedStyled.Text>
                    <Styled.NameValueUnitContainer>
                      <Styled.UnitDiv>$</Styled.UnitDiv>
                      <Styled.PercentValueInput
                        onWheel={handleWheel}
                        width={'100%'}
                        // value={
                        //   data?.taxJurisdiction
                        //     ? values.jobCost * getTaxJurisdictionRateFromId(data?.taxJurisdiction, tax)
                        //     : 0
                        // }
                        name="tax"
                        type="number"
                        borderRadius="0px 4px 4px 0px"
                        disabled={true}
                      />
                    </Styled.NameValueUnitContainer>
                  </div>

                  <div>
                    <SharedStyled.Text fontSize="18px" fontWeight="700">
                      Total:
                    </SharedStyled.Text>
                    <Styled.NameValueUnitContainer>
                      <Styled.UnitDiv>$</Styled.UnitDiv>
                      <Styled.PercentValueInput
                        onWheel={handleWheel}
                        width={'100%'}
                        name="total"
                        // value={values.jobCost + values.tax}
                        type="number"
                        borderRadius="0px 4px 4px 0px"
                        disabled={true}
                      />
                    </Styled.NameValueUnitContainer>
                  </div> */}

                  <SharedStyled.FlexBox marginTop="20px" justifyContent="space-between">
                    <div>
                      <Button width="150px" type="submit" isLoading={loading} disabled={priceLoading}>
                        {isEdit ? 'Update' : 'Add'}
                      </Button>
                      &emsp;
                      {isEdit &&
                        (inputData?.deleted ? (
                          <Button
                            width="150px"
                            type="submit"
                            onClick={() => setRestoreFlag(true)}
                            isLoading={deleteLoading}
                          >
                            Restore
                          </Button>
                        ) : (
                          <Button
                            className="fit delete"
                            width="150px"
                            type="submit"
                            onClick={() => setDeleteFlag(true)}
                            isLoading={deleteLoading}
                          >
                            Delete
                          </Button>
                        ))}
                    </div>

                    <Button onClick={() => setShowChangeOrder(false)} width="150" type="button">
                      Cancel
                    </Button>
                  </SharedStyled.FlexBox>
                </Styled.ModalBodyContainer>
              </Form>
            </>
          )
        }}
      </Formik>
    </Styled.ConfirmationContainer>
  )
}

export default NewChangeOrderModal
