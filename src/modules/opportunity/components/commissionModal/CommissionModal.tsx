import React, { useEffect, useRef, useState } from 'react'
import * as Styled from './styles'

import * as SharedStyled from '../../../../styles/styled'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { ErrorMessage, Field, Form, Formik } from 'formik'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import Button from '../../../../shared/components/button/Button'
import {
  ISalesCommission,
  createOpportunityCommission,
  updateOpportunityCommission,
} from '../../../../logic/apis/sales'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { formatDateymd, isSuccess, notify, simplifyBackendError, startOfDate } from '../../../../shared/helpers/util'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

const CommissionModal = ({
  onComplete,
  isEdit,
  salesPersonId,
  id,
  memberId,
  fetchOpportunityModifiedCommission,
  commissionUpdate,
}: // handleValue,
{
  // handleValue: (data: Partial<ISalesCommission>, resetCallback: () => void, activityString?: string) => Promise<void>
  onComplete: () => void
  isEdit: boolean
  salesPersonId: string
  id: string
  memberId: string
  fetchOpportunityModifiedCommission: () => Promise<void>
  commissionUpdate: any
}) => {
  const [initValues, setInitValues] = useState({
    date: '',
    amount: '',
    reason: '',
  })
  useEffect(() => {
    if (isEdit) {
      setInitValues({
        date: formatDateymd(commissionUpdate?.date) || '',
        amount: commissionUpdate?.amount || 0,
        reason: commissionUpdate?.reason || '',
      })
    }
  }, [isEdit])
  const commissionModalSchema = Yup.object().shape({
    amount: Yup.number().required('Required'),
    date: Yup.string().required('Required'),
    reason: Yup.string().required('Required'),
  })

  const [buttonLoading, setButtonLoading] = useState(false)
  const { oppId } = useParams()

  const onSubmit = async (val: typeof initValues) => {
    try {
      setButtonLoading(true)
      const data: any = {
        ...val,
        date: startOfDate(val.date),
        oppId: oppId,
        salesPersonId: salesPersonId,
        id: isEdit ? commissionUpdate?._id || '' : undefined,
        createdBy: memberId,
      }
      console.log({ data })
      if (isEdit) {
        const res = await updateOpportunityCommission(data)
        if (isSuccess(res)) {
          setButtonLoading(false)
          onComplete()
          fetchOpportunityModifiedCommission()
        }
      } else {
        const res = await createOpportunityCommission(data)
        if (isSuccess(res)) {
          setButtonLoading(false)
          onComplete()
          fetchOpportunityModifiedCommission()
        }
      }
    } catch (error: any) {
      notify(simplifyBackendError(error?.response?.data?.message), 'error')
    } finally {
      setButtonLoading(false)
    }
  }

  const inputRef = useRef<HTMLInputElement>(null)

  // useEffect(() => {
  //   if (inputRef.current) {
  //     inputRef.current.focus()
  //   }
  // }, [inputRef])
  return (
    <>
      <SharedStyled.ModalContainer>
        <SharedStyled.ModalHeaderContainer>
          <SharedStyled.FlexRow>
            <img src={UnitSvg} alt="modal icon" />
            <SharedStyled.FlexCol>
              <SharedStyled.ModalHeader>Modify Commission</SharedStyled.ModalHeader>
            </SharedStyled.FlexCol>
          </SharedStyled.FlexRow>
          <SharedStyled.CrossContainer
            onClick={() => {
              onComplete()
            }}
          >
            <CrossIcon />
          </SharedStyled.CrossContainer>
        </SharedStyled.ModalHeaderContainer>
        <SharedStyled.SettingModalContentContainer flexDirection="column" id="commission">
          <Formik
            initialValues={initValues}
            enableReinitialize={true}
            onSubmit={onSubmit}
            validateOnChange={true}
            validateOnBlur={false}
            validationSchema={commissionModalSchema}
          >
            {({ values, errors, touched, resetForm, setFieldValue, handleSubmit }) => {
              console.log({ values, errors })
              return (
                <Form>
                  <SharedStyled.FlexRow width="100%" flexDirection="column" gap="10px">
                    <SharedDate
                      value={values.date}
                      labelName="Date"
                      stateName="date"
                      error={touched.date && errors.date ? true : false}
                      setFieldValue={setFieldValue}
                      // passRef={inputRef}
                    />

                    <InputWithValidation
                      labelName="Change Amount"
                      stateName="amount"
                      forceType="number"
                      value={values.amount}
                      error={touched.amount && errors.amount ? true : false}
                    />

                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      placeholder="Reason"
                      name="reason"
                      margintop="5px"
                      height="52px"
                      className="textarea"
                      width="100%"
                      error={touched.reason && errors.reason ? true : false}
                      // placeHolder="ie. Remove existing shingle,flashing, and underlayment; then install new ice shield, flashing, and shingle to match"
                    />
                    {touched.reason && errors.reason && (
                      <SharedStyled.ErrorMsg paddingTop="0" style={{ marginRight: 'auto' }}>
                        <ErrorMessage name={'reason'} />
                      </SharedStyled.ErrorMsg>
                    )}
                    {/* <SharedStyled.TextArea
                      component="textarea"
                      placeholder="Reason"
                      as={Field}
                      name="reason"
                      marginTop="8px"
                      height="52px"
                      stateName="notes"
                      error={touched.reason && errors.reason ? true : false}
                    /> */}
                  </SharedStyled.FlexRow>
                  <SharedStyled.FlexRow gap="12px" margin="12px 0 0 0">
                    <Button
                      isLoading={buttonLoading}
                      type="button"
                      onClick={() => {
                        handleSubmit()
                      }}
                    >
                      Save
                    </Button>
                    {true ? (
                      <Button type="button" className="delete" onClick={onComplete}>
                        Cancel
                      </Button>
                    ) : null}
                  </SharedStyled.FlexRow>
                </Form>
              )
            }}
          </Formik>
        </SharedStyled.SettingModalContentContainer>
      </SharedStyled.ModalContainer>
    </>
  )
}

export default CommissionModal
