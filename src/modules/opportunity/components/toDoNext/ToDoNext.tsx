import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../styles/styled'
import { Form, Formik } from 'formik'
import AutoComplete from '../../../../shared/autoComplete/AutoComplete'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { SharedDateAndTime } from '../../../../shared/date/SharedDateAndTime'
import Button from '../../../../shared/components/button/Button'
import Checkbox from '../../../../shared/checkbox/Checkbox'
import { I_NextAction, I_Opportunity } from '../../Opportunity'
import {
  dayjsFormat,
  extractPermissionByName,
  generateUUID,
  getKeysFromObjects,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
} from '../../../../shared/helpers/util'
import { I_Action } from '../assessmentForm/AssessmentForm'
import { useSelector } from 'react-redux'
import {
  completeAction,
  createAction,
  getActionMembers,
  getSalesActionByMemberId,
  updateActivity,
} from '../../../../logic/apis/sales'
import * as Yup from 'yup'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import ActionModal from '../actionModal/ActionModal'
import { getPosition } from '../../../../logic/apis/position'

interface I_FormProps {
  oppData: I_Opportunity
  oppId: string | undefined
  setOppData: React.Dispatch<React.SetStateAction<I_Opportunity | undefined>>
  fetchActivity: () => Promise<void>
  initFetch?: any
}

const ToDoNext: React.FC<I_FormProps> = (props) => {
  const [todoCheck, setTodoCheck] = useState(false)
  const [editTodo, setEditTodo] = useState(false)
  const [actionsForToDoNext, setActionsForToDoNext] = useState<any>([])
  const [toDoLoading, setToDoLoading] = useState(false)
  const [actionModal, setActionModal] = useState(false)
  const [autoFillValues, setAutoFillValues] = useState({ type: '', name: '' })
  const [autoFillValuesFromChild, setAutoFillValuesFromChild] = useState({ type: '', name: '' })
  const [salesPersonDrop, setSalesPersonDrop] = useState<any[]>([])
  const [selfId, setSelfId] = useState('')

  const [initTodoData, setInitTodoData] = useState<I_Action>({
    due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    type: '',
    completedBy: '',
    body: '',
  })
  const { oppData, oppId, setOppData, fetchActivity, initFetch } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, positionDetails } = globalSelector.company

  const hasOppManagedFullPermission =
    hasValues(positionDetails) && extractPermissionByName(positionDetails, 'actions')?.permissions < 3

  useEffect(() => {
    if (currentMember?._id) {
      fetchActions()
      setSelfId(currentMember._id)
      getActivePositionMembers() // for actions
    }
  }, [currentMember])

  const fetchActions = async () => {
    try {
      const res = await getSalesActionByMemberId(currentMember?._id, false)
      if (isSuccess(res)) {
        const { actions } = res.data.data.salesAction
        setActionsForToDoNext(actions)
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const onTodoComplete = async (nextAction: I_Action) => {
    const actionId = actionsForToDoNext?.find((action: { name: string }) => action?.name === nextAction?.body)?._id
    const assignTo = salesPersonDrop?.find((person: any) => person.name === nextAction.assignTo)?._id

    let nextActionData: I_NextAction = {
      _id: actionId ?? generateUUID(),
      body: nextAction.body,
      createdAt: new Date().toISOString(),
      createdBy: currentMember._id,
      due: new Date(nextAction.due).toISOString(),
      type: nextAction.type,
    }

    try {
      setToDoLoading(true)

      if (!todoCheck && oppData?.nextAction) {
        const responseForEdit = await createAction({
          body: nextAction.body,
          currDate: new Date(),
          dueDate: new Date(nextAction.due),
          memberId: currentMember._id!,
          assignTo: assignTo ?? undefined,
          oppId: oppId!,
          type: nextAction.type,
          id: actionId ?? generateUUID(),
        })

        if (isSuccess(responseForEdit)) {
          setOppData((prev: any) => ({ ...prev, nextAction: nextActionData }))
          if (oppData?.nextAction) {
            const res = await updateActivity({
              id: oppId!,
              memberId: currentMember._id!,
              body: `Edited:  ${oppData?.nextAction.body} to ${nextAction.body} | Due date: ${new Date(
                oppData?.nextAction.due
              ).toLocaleDateString('en-US', {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric',
              })} ${
                oppData?.nextAction.due
                  ? new Date(oppData?.nextAction.due).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: 'numeric',
                    })
                  : ''
              } to ${new Date(nextAction.due).toLocaleDateString('en-US', {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric',
              })} ${new Date(nextAction.due).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: 'numeric',
              })}`,
              currDate: new Date().toISOString(),
            })
            if (isSuccess(res)) {
              notify('Action Updated', 'success')
              fetchActivity()
            }
          }
          // if (!actionId) {
          //   fetchActions()
          // }
          // initFetchOpportunity()
        } else {
          throw new Error(responseForEdit?.data?.message)
        }
      } else {
        // action api

        if (oppData?.nextAction) {
          await completeAction({
            body: oppData.nextAction.body,
            currDate: new Date(),
            dueDate: oppData.nextAction.due,
            memberId: currentMember._id!, //UserId##
            oppId: oppId!,
            assignTo: assignTo ?? undefined,
            type: oppData.nextAction.type,
            id: oppData?.nextAction?._id,
          })
        }

        const nextActionId = actionsForToDoNext?.find(
          (action: { name: string }) => action?.name === nextAction?.body
        )?._id

        const response = await createAction({
          body: nextAction.body,
          currDate: new Date(),
          dueDate: nextAction.due,
          memberId: currentMember._id!,
          oppId: oppId!,
          assignTo: assignTo ?? undefined,
          type: nextAction.type,
          id: nextActionId ?? generateUUID(),
        })

        if (isSuccess(response)) {
          setOppData((prev: any) => ({ ...prev, nextAction: nextActionData }))
          if (oppData?.nextAction) {
            const res1 = await updateActivity({
              id: oppId!,
              memberId: currentMember._id!,
              body: `Completed action ${oppData?.nextAction?.body}`,
              currDate: new Date().toISOString(),
            })
            const res2 = await updateActivity({
              id: oppId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextActionData.body}`,
              currDate: new Date().toISOString(),
            })

            if (isSuccess(res1) && isSuccess(res2)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          } else {
            const res = await updateActivity({
              id: oppId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextAction.body}`,
              currDate: new Date().toISOString(),
            })
            if (isSuccess(res)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          }
        } else {
          throw new Error(response?.data?.message)
        }
      }
      setTodoCheck(false)
      setEditTodo(false)
    } catch (err) {
      console.log('ACTIONS ERR', err)
    } finally {
      setToDoLoading(false)
      initFetch(false)
    }
  }

  const onEditCancel = () => {
    setInitTodoData({
      due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
      type: '',
      completedBy: '',
      body: '',
      _id: '',
    })
    if (oppData?.nextAction) {
      setEditTodo(false)
      setTodoCheck(false)
    }
  }

  const getActivePositionMembers = async () => {
    try {
      const response = await getActionMembers({}, false)
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const onEditTodo = () => {
    if (oppData && oppData?.nextAction)
      setInitTodoData({
        due: dayjsFormat(oppData?.nextAction.due, 'YYYY-MM-DDTHH:mm'),
        body: oppData!.nextAction.body,
        type: oppData!.nextAction.type,
        assignTo: salesPersonDrop?.find((person: any) => person._id === oppData?.nextAction?.assignTo)?.name,
        completedBy: currentMember._id!,
      })

    setEditTodo((prev) => !prev)
  }

  const todoSchema = Yup.object().shape({
    body: Yup.string().required('Required'),
    due: Yup.string().required('Required'),
    type: Yup.string().required('Required'),
  })

  return (
    <>
      <div>
        <SharedStyled.ContentHeader textAlign="left" as="h3">
          To Do Next
        </SharedStyled.ContentHeader>
        {toDoLoading ? (
          <SharedStyled.CardSkeleton height="200px"></SharedStyled.CardSkeleton>
        ) : (
          <div className="todo-item">
            {todoCheck || editTodo || !oppData?.nextAction ? (
              <div style={{ width: '100%' }}>
                <Formik
                  initialValues={initTodoData}
                  onSubmit={onTodoComplete}
                  validationSchema={todoSchema}
                  enableReinitialize={true}
                  validateOnChange={true}
                  validateOnBlur={false}
                >
                  {({ touched, errors, resetForm, values, setFieldValue }) => {
                    useEffect(() => {
                      if (autoFillValuesFromChild.type !== '' && autoFillValuesFromChild.name !== '') {
                        setFieldValue('type', autoFillValuesFromChild.type)
                        setFieldValue('body', autoFillValuesFromChild.name)
                      }
                    }, [autoFillValuesFromChild])

                    useEffect(() => {
                      if (values.type) {
                        setAutoFillValues((prev) => ({
                          ...prev,
                          type: values.type,
                        }))
                      }
                    }, [values.type])

                    useEffect(() => {
                      if (salesPersonDrop?.length && hasOppManagedFullPermission && !hasValues(oppData?.nextAction)) {
                        setFieldValue('assignTo', salesPersonDrop?.find((person: any) => person._id === selfId)?.name)
                        return
                      }
                      if (salesPersonDrop?.length && hasOppManagedFullPermission && todoCheck) {
                        setFieldValue('assignTo', salesPersonDrop?.find((person: any) => person._id === selfId)?.name)
                      }
                    }, [salesPersonDrop?.length, todoCheck])

                    return (
                      <Form>
                        {/* stepObject */}
                        <SharedStyled.TwoInputDiv>
                          <SharedStyled.FlexBox width="100%" gap="12px">
                            <AutoComplete
                              value={values?.body}
                              options={getKeysFromObjects(actionsForToDoNext ?? [], 'name') ?? []}
                              dropdownHeight={'300px'}
                              labelName="Next Action"
                              stateName="body"
                              setFieldValue={setFieldValue}
                              error={touched.body && errors.body ? true : false}
                              onAddClick={(val: string) => {
                                setAutoFillValues((prev) => ({ ...prev, name: val }))
                                setActionModal(true)
                              }}
                              showAddOption
                              setValueOnClick={(val: string) => {
                                setFieldValue(`type`, getValueByKeyAndMatch('type', val, `name`, actionsForToDoNext))
                              }}
                              // setTypeForAction={(body: string) =>
                              //   setFieldValue('type', getActivityTypeByStepName(body, stepForToDoNext))
                              // }
                              // showAddOption
                            />
                            {/* ) : (
                          <>
                            <SharedStyled.Skeleton
                              custWidth="100%"
                              custHeight={'51px'}
                              custMarginTop={'50px'}
                            />
                          </>
                        )} */}
                          </SharedStyled.FlexBox>
                        </SharedStyled.TwoInputDiv>
                        <div
                          style={{
                            display: 'grid',
                            gap: '10px',
                            gridTemplateColumns: hasOppManagedFullPermission ? '1fr 1fr 1fr' : '1fr 2fr',
                            alignItems: 'flex-end',
                          }}
                        >
                          <CustomSelect
                            labelName="Select Type"
                            stateName="type"
                            error={touched.type && errors.type ? true : false}
                            value={values.type}
                            dropDownData={['Task', 'Call', 'Email', 'Text']}
                            setValue={() => {}}
                            setFieldValue={setFieldValue}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />

                          <SharedDateAndTime
                            value={values.due}
                            labelName={'Due Date/Time'}
                            stateName="due"
                            setFieldValue={setFieldValue}
                            error={touched.due && errors.due ? true : false}
                          />
                          {hasOppManagedFullPermission && (
                            <CustomSelect
                              labelName="Assign To:"
                              stateName="assignTo"
                              error={touched.assignTo && errors.assignTo ? true : false}
                              value={values.assignTo ? values.assignTo : ''}
                              dropDownData={salesPersonDrop?.map((val) => val.name)}
                              setValue={() => {}}
                              setFieldValue={setFieldValue}
                              innerHeight="52px"
                              margin="10px 0 0 0"
                            />
                          )}

                          {/* <SharedStyled.TwoInputDiv>
                        <SharedDate
                          value={values.completedDate}
                          labelName="Date"
                          stateName="completedDate"
                        />
                        <NormalInput
                          type="time"
                          value={values.completedTime}
                          onChange={(e: any) => {
                            setFieldValue('completedTime', e.target.value)
                          }}
                          labelName="Time"
                          stateName="completedTime"
                        />
                      </SharedStyled.TwoInputDiv> */}
                        </div>

                        <SharedStyled.FlexBox width="100%" gap="12px" margin="24px 0 0 0">
                          <Button type="submit" className="fit">
                            Save Action
                          </Button>
                          <Button onClick={() => onEditCancel()} className="fit outline">
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>
                      </Form>
                    )
                  }}
                </Formik>
              </div>
            ) : null}
            {oppData?.nextAction && !editTodo ? (
              <div className="todo-container">
                <SharedStyled.FlexRow>
                  <Checkbox
                    onChange={() => {
                      setTodoCheck((prev) => !prev)
                    }}
                    value={todoCheck}
                    cursor="pointer"
                  />
                </SharedStyled.FlexRow>

                <div className="checkbox-item">
                  <div className={todoCheck ? 'strike' : ''}>
                    <SharedStyled.FlexRow className="adjust-box" gap="12px" justifyContent="space-between">
                      {/* <SharedStyled.FlexRow width="50%"> */}
                      <div>
                        <SharedStyled.Text familyTypeMedium fontSize="13px">
                          {oppData?.nextAction?.body}
                        </SharedStyled.Text>
                      </div>
                      {/* </SharedStyled.FlexRow> */}
                      {/* <SharedStyled.FlexRow justifyContent="space-between"> */}
                      <div>
                        {' '}
                        <SharedStyled.Text familyTypeMedium fontSize="13px">
                          {oppData.nextAction?.type}
                        </SharedStyled.Text>
                        {/* </SharedStyled.FlexRow> */}
                        {oppData.nextAction?.due ? (
                          // <SharedStyled.FlexRow justifyContent="space-between">
                          <SharedStyled.Text familyTypeMedium fontSize="13px">
                            &emsp;
                            {new Date(oppData?.nextAction?.due).toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric',
                            })}
                          </SharedStyled.Text>
                        ) : (
                          // </SharedStyled.FlexRow>
                          ''
                        )}
                        {oppData.nextAction?.due ? (
                          <SharedStyled.Text familyTypeMedium fontSize="13px">
                            &nbsp;
                            {new Date(oppData?.nextAction?.due).toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: 'numeric',
                            })}
                          </SharedStyled.Text>
                        ) : (
                          ''
                        )}
                      </div>
                    </SharedStyled.FlexRow>
                  </div>
                </div>

                {!todoCheck && (
                  <span
                    className="link"
                    onClick={() => {
                      onEditTodo()
                    }}
                  >
                    Edit
                  </span>
                )}
              </div>
            ) : null}
          </div>
        )}
      </div>
      <CustomModal show={actionModal}>
        <ActionModal
          onClose={() => setActionModal(false)}
          onComplete={fetchActions}
          values={autoFillValues}
          setAutoFillValuesFromChild={setAutoFillValuesFromChild}
        />
      </CustomModal>
    </>
  )
}

export default ToDoNext
