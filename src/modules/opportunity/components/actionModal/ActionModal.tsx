import React, { useState } from 'react'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './styles'
import { onlyNumber, onlyText, onlyTextWithSpaces } from '../../../../shared/helpers/regex'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'

import { I_Opportunity } from '../../Opportunity'
import {
  createCompanySalesAction,
  createSalesAction,
  updateCompanySalesActionApi,
  updateSalesActionApi,
} from '../../../../logic/apis/sales'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { generateUUID, getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_ActionModal {
  onClose: any
  onComplete: () => void
  actionData?:
    | {
        name: string
        type: string
        _id: string
      }
    | any
  isDefault?: boolean
  values?: {
    type: string
    name: string
  }
  setAutoFillValuesFromChild?: React.Dispatch<
    React.SetStateAction<{
      type: string
      name: string
    }>
  >
}

interface I_initialValues {
  name: string
  type: string
}

const ActionModal: React.FC<I_ActionModal> = (props) => {
  const { onClose, onComplete, actionData, isDefault, setAutoFillValuesFromChild, values: valuesFromParent } = props
  const initialValues: I_initialValues = {
    name: actionData?.name ?? valuesFromParent?.name ?? '',
    type: actionData?.type ?? valuesFromParent?.type ?? '',
  }
  const { oppId } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentMember, currentCompany } = globalSelector.company
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)
    console.log(values)
    try {
      if (isDefault) {
        if (actionData && Object?.entries?.(actionData)?.length) {
          const response = await updateCompanySalesActionApi({
            name: values.name,
            type: values.type,
            actionId: actionData._id,
            memberId: currentCompany._id,
          })

          if (isSuccess(response)) {
            notify('Action edited successfully', 'success')
            onComplete()
          }
        } else {
          const response = await createCompanySalesAction({
            action: {
              name: values.name,
              type: values.type,
              _id: generateUUID()!,
            },
          })

          if (isSuccess(response)) {
            notify('Action created successfully', 'success')
            onComplete()
          }
        }
      } else {
        if (actionData && Object?.entries?.(actionData)?.length) {
          const response = await updateSalesActionApi({
            name: values.name,
            type: values.type,
            actionId: actionData._id,
            memberId: currentMember._id,
          })

          if (isSuccess(response)) {
            notify('Action edited successfully', 'success')
            onComplete()
          }
        } else {
          const response = await createSalesAction({
            action: {
              name: values.name,
              type: values.type,
              _id: generateUUID()!,
            },
            memberId: currentMember._id!,
          })

          if (isSuccess(response)) {
            if (setAutoFillValuesFromChild) {
              setAutoFillValuesFromChild({
                name: values.name,
                type: values.type,
              })
            }
            notify('Action created successfully', 'success')
            onComplete()
          }
        }
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setLoading(false)
      onClose()
    }
  }

  const newActionSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyTextWithSpaces, 'Enter Valid Name'),
    type: Yup.string().required('Required').matches(onlyText, 'Select Valid Name'),
  })

  return (
    <Styled.StepModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={newActionSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange, handleSubmit }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <Styled.ModalHeader>Add New Action</Styled.ModalHeader>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer padding="0px 20px 20px 20px">
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Name*"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                    />
                    <CustomSelect
                      dropDownData={['Task', 'Call', 'Email', 'Text']}
                      setValue={() => {}}
                      stateName="type"
                      value={values.type}
                      error={touched.type && errors.type ? true : false}
                      setFieldValue={setFieldValue}
                      labelName="Type*"
                      innerHeight="52px"
                      margin="10px 0 0 0"
                    />
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <SharedStyled.Button type="submit" maxWidth="150px">
                        {loading ? (
                          <>
                            <SharedStyled.Loader />
                          </>
                        ) : (
                          'Add'
                        )}
                      </SharedStyled.Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.StepModalContainer>
  )
}

export default ActionModal
