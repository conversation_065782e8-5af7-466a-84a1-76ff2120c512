import React, { useState } from 'react'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './styles'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { lostOpportunity, unlostOpportunity } from '../../../../logic/apis/sales'
import { getDataFromLocalStorage, isSuccess, notify, startOfDate } from '../../../../shared/helpers/util'
import But<PERSON> from '../../../../shared/components/button/Button'
import { I_Opportunity } from '../../Opportunity'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_LostModal {
  onClose: any
  isLost: boolean
  setIsLost: any
  setOppData: any
  checkpointSymbol: string[]
  oppData: I_Opportunity
  fetchActivity: () => Promise<void>
  initFetchOpportunity: () => Promise<void>
}

interface I_initialValues {
  reason: string
}

const lostSchema = Yup.object().shape({
  reason: Yup.string().required('Required'),
})

const LostModal: React.FC<I_LostModal> = (props) => {
  const { onClose, isLost, setIsLost, setOppData, checkpointSymbol, oppData, fetchActivity, initFetchOpportunity } =
    props
  console.log({ oppData, checkpointSymbol })
  const initialValues: I_initialValues = {
    reason: '',
  }

  const { oppId } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [loading, setLoading] = useState(false)
  interface CheckpointDates {
    [key: string]: string
  }

  function checkFutureDates(datesObj: CheckpointDates): boolean {
    const today = new Date()
    const dateKeys = Object.keys(datesObj)
    const futureDates = []

    for (const key of dateKeys) {
      const date = new Date(datesObj[key])
      if (date > today) {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())
        futureDates.push(formattedKey)
      }
    }
    console.log({ futureDates })
    if (futureDates.length > 1) {
      const message = `You have the following dates:\n${futureDates.join(
        '\n'
      )}\n\nThat haven't happened yet, if you lose this lead, it will delete those dates.`
      return confirm(message)
    } else if (futureDates.length === 1) {
      const message = `You have a ${futureDates.join(
        ''
      )} that hasn't happened yet, if you lose this lead, it will delete that date.`
      return confirm(message)
    } else {
      return true // No future dates found
    }
  }

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)
    try {
      const availableCheckpointDates = checkpointSymbol.reduce((acc, key): any => {
        if (oppData.hasOwnProperty(key)) {
          acc[key] = oppData[key]
        }
        return acc
      }, {})
      const result = !isLost && checkFutureDates(availableCheckpointDates)
      console.log({ result, availableCheckpointDates })
      const response = await (!isLost
        ? result
          ? lostOpportunity({
              memberId: currentMember._id!,
              id: oppId!,
              reason: values.reason,
              date: new Date().toISOString(),
            })
          : notify(`Failed to update status!`, 'error')
        : unlostOpportunity({
            memberId: currentMember._id!,
            id: oppId!,
            reason: values.reason,
            date: new Date().toISOString(),
          }))
      if (isSuccess(response)) {
        setIsLost((prev: boolean) => !prev)
        if (!isLost) {
          initFetchOpportunity()
        }
        fetchActivity()
        notify(`${isLost ? 'Unlost' : 'Lost'} opportunity!`, 'success')
        setLoading(false)
        !isLost
          ? setOppData((pre: any) => ({ ...pre, lostDate: new Date(), lostReason: values.reason }))
          : setOppData((pre: any) => {
              const { lostDate, lostReason, ...newObj } = pre
              return newObj
            })
        onClose()
      } else throw new Error(response?.data?.message)
    } catch (err) {
      onClose()
      setLoading(false)
      console.log('makeLost err', err)
    } finally {
      initFetchOpportunity()
    }
  }
  console.log({ isLost })

  return (
    <Styled.StepModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={lostSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange, handleSubmit }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{isLost ? 'Unlost' : 'Lost'} Opportunity</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Reason*"
                      stateName="reason"
                      error={touched.reason && errors.reason ? true : false}
                    />

                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button type="submit" maxWidth="150px" isLoading={loading}>
                        Submit
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.StepModalContainer>
  )
}

export default LostModal
