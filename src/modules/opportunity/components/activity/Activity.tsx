import React, { useEffect, useState } from 'react'
import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { Formik } from 'formik'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { AnyKey, I_Activity } from '../assessmentForm/AssessmentForm'
import { getStageNameFromId, isSuccess } from '../../../../shared/helpers/util'
import { useParams } from 'react-router-dom'
import { SLoader } from '../../../../shared/components/loader/Loader'

interface I_AllActivity {
  body: string
  createdBy?: string
  createdAt: string
  userName?: string
  name: string
  _id: string
}

const Activity: React.FC<{
  activities?: I_Activity[]
  activityLoading: boolean
  inputKeys?: AnyKey
  stages?: any
  getAtivity: I_AllActivity[]
}> = (props) => {
  const { inputKeys, stages, getAtivity, activityLoading } = props

  function replaceFirstWord(inputString: string): string {
    const words = inputString.split(' ')
    // if (words[0] in inputKeys) {
    //   words[0] = inputKeys[words[0]]
    // }
    if (inputKeys && typeof inputKeys === 'object' && words[0] in inputKeys) {
      words[0] = inputKeys[words[0]]
    }
    return words.join(' ')
  }
  console.log({ getAtivity })
  return (
    <>
      <SharedStyled.FlexCol gap="10px">
        <SharedStyled.ContentHeader textAlign="start" as={'h3'}>
          Activity
        </SharedStyled.ContentHeader>
        <SharedStyled.FlexBox overflow="auto" flexDirection="column" width="100%" maxHeight="540px">
          <SharedStyled.HorizontalDivider />

          {activityLoading ? (
            renderLoader()
          ) : (
            <>
              {getAtivity
                ?.sort((a, b) => new Date(b.createdAt)?.getTime() - new Date(a.createdAt)?.getTime())
                ?.map((activity, idx) => (
                  <Styled.ActivityWrapper key={idx}>
                    <span className="small-text">
                      <span className="action">
                        {' '}
                        <span className="bold">{activity.name}</span>{' '}
                        {activity?.body?.includes('Stage changed to')
                          ? `Stage changed to ${getStageNameFromId(activity?.body?.split('changed to ')[1], stages)}`
                          : replaceFirstWord(activity.body)}
                      </span>
                      <span>
                        {`${new Date(activity.createdAt).toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: 'numeric',
                          hour12: true,
                        })} ${new Date(activity.createdAt).toLocaleDateString('en-US', {
                          month: '2-digit',
                          day: '2-digit',
                          year: 'numeric',
                        })}`}
                      </span>
                    </span>
                  </Styled.ActivityWrapper>
                ))}
            </>
          )}

          {getAtivity?.length === 0 && !activityLoading ? (
            <SharedStyled.Text>No activity found!</SharedStyled.Text>
          ) : null}
        </SharedStyled.FlexBox>
      </SharedStyled.FlexCol>
    </>
  )
}

export default React.memo(Activity)

const renderLoader = () => {
  return (
    <SharedStyled.FlexCol gap="16px">
      {new Array(6)?.fill('_')?.map((_, key) => (
        <SharedStyled.FlexRow justifyContent="space-between" key={key} padding="0 12px">
          <SLoader width={300} height={30} />

          <SharedStyled.FlexRow margin="0 0 0 auto" width="max-content">
            <SLoader width={90} height={30} />
          </SharedStyled.FlexRow>
        </SharedStyled.FlexRow>
      ))}
    </SharedStyled.FlexCol>
  )
}
