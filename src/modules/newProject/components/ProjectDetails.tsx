import React, { useState } from 'react'
import { ErrorMessage } from 'formik'
import {
  Gap,
  NameDiv,
  NameValueUnitContainer,
  SaperatorDiv,
  SaperatorDivThree,
  SaperatorDivTwo,
  ValueInput,
} from '../style'
import * as SharedStyled from '../../../styles/styled'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import RadioButtonGroup from '../../../shared/radioButtonGroup/RadioButtonGroup'
import { InputLabelWithValidation } from '../../../shared/inputLabelWithValidation/InputLabelWithValidation'
import { handleWheel } from '../../../shared/helpers/util'
import Button from '../../../shared/components/button/Button'

interface IType {
  id: string
  name: string
  value: string
  label: string
}
interface I_RoofAreas {
  name: string
  pitch: number
  size: number
  layers: string
  ventArea: string
  rmvPly: string
  instPly: string
  noAccess: string
  twoStory: string
}
interface I_Chimneys {
  width: string
  length: string
}
interface I_SkyLights {
  width: string
  length: string
  skylightType: string
}
interface InitialValues {
  name: string
  notes: string
  customData: {
    roofAreas: I_RoofAreas[]
    bVents: string[]
    stemVents: string[]
    sunTunnels: string[]
    chimneys: I_Chimneys[]
    skylights: I_SkyLights[]
  }
}
interface I_ProjectDetailsPorps {
  setSelectedLayer: React.Dispatch<React.SetStateAction<string>>
  setFieldValue: (field: string, value: any, shouldValidate?: boolean | undefined) => void
  selectedLayer: string
  index: number
  action: IType[]
  values: InitialValues
  data: any
  errors: any
  touched: any
  remove: <X = any>(index: number) => X | undefined
}
const ProjectDetails: React.FC<I_ProjectDetailsPorps> = (props) => {
  const {
    setSelectedLayer,
    setFieldValue,
    index: indexProps,
    action,
    selectedLayer,
    values,
    data,
    touched,
    errors,
    remove,
  } = props
  const [selectedValues, setSelectedValues] = useState<{ [key: string]: string }>({})
  const constFields = [
    {
      name: 'Install Sheeting:',
      options: ['Yes', 'No'],
      type: 'instPly',
    },
    {
      name: 'Remove Sheeting:',
      options: ['Yes', 'No'],
      type: 'rmvPly',
    },
    {
      name: 'Dump Access:',
      options: ['Yes', 'No'],
      type: 'noAccess',
    },
    {
      name: '2+ Story:',
      options: ['Yes', 'No'],
      type: 'twoStory',
    },
    {
      name: 'Venting Needed:',
      options: ['Yes', 'No'],
      type: 'ventArea',
    },
  ]

  const handleRadioChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    setFieldValue?: (name: string, value: string) => void
  ) => {
    const { name, value } = event.target
    if (!!setFieldValue) {
      setFieldValue(name, value)
    }
    setSelectedValues((prevSelectedValues) => ({
      ...prevSelectedValues,
      [name]: value,
    }))
  }
  // if (!values.customData.roofAreas[indexProps].name) {
  //   setFieldValue(`customData.roofAreas.${indexProps}.name`, `Roof Area ${indexProps + 1}`)
  // }

  return (
    <SaperatorDivTwo>
      <div>
        <SaperatorDiv>
          <Gap>
            <SharedStyled.FlexBox flexDirection="column" marginTop="10px" gap="4px">
              {/* <SharedStyled.ValueInputLabel>Area Name 1</SharedStyled.ValueInputLabel> */}
              <InputLabelWithValidation
                labelName="Area Name"
                stateName={`customData.roofAreas.${indexProps}.name`}
                error={
                  touched?.customData?.roofAreas?.[indexProps]?.name &&
                  errors?.customData?.roofAreas?.[indexProps]?.name
                    ? true
                    : false
                }
                noLabel
              />
            </SharedStyled.FlexBox>

            {/* <SharedStyled.ErrorContainer>
              <SharedStyled.ErrorMsg>
                <ErrorMessage
                  name={`customData.roofAreas.${indexProps}.name`}
                  component="div"
                  className="error-message"
                />
              </SharedStyled.ErrorMsg>
            </SharedStyled.ErrorContainer> */}
          </Gap>

          <Gap>
            <SharedStyled.FlexBox flexDirection="column" marginTop="10px" gap="4px">
              {/* <SharedStyled.ValueInputLabel>Size (SQ)</SharedStyled.ValueInputLabel> */}
              <InputLabelWithValidation
                stateName={`customData.roofAreas.${indexProps}.size`}
                labelName="Size (SQ)"
                inputType="number"
                // isSmall
                error={
                  errors?.customData?.roofAreas?.[indexProps]?.size &&
                  touched?.customData?.roofAreas?.[indexProps]?.size
                    ? true
                    : false
                }
                labelUnit="SQ"
                onWheel={handleWheel}
              />
            </SharedStyled.FlexBox>
          </Gap>
        </SaperatorDiv>

        <SaperatorDiv>
          <Gap>
            <SharedStyled.FlexBox flexDirection="column" gap="4px">
              {/* <SharedStyled.ValueInputLabel>Pitch</SharedStyled.ValueInputLabel> */}
              <InputLabelWithValidation
                stateName={`customData.roofAreas.${indexProps}.pitch`}
                labelName="Pitch"
                inputType="number"
                // isSmall
                error={
                  errors?.customData?.roofAreas?.[indexProps]?.pitch &&
                  touched?.customData?.roofAreas?.[indexProps]?.pitch
                    ? true
                    : false
                }
                labelUnit="/12"
                onWheel={handleWheel}
              />
            </SharedStyled.FlexBox>
          </Gap>
          <Gap>
            <SharedStyled.FlexBox flexDirection="column" gap="4px">
              {/* <SharedStyled.Text>Layers</SharedStyled.Text> */}
              <CustomSelect
                labelName="# of layers"
                // error={false}
                value={values.customData.roofAreas[indexProps].layers}
                dropDownData={['1', '2', '3', '4', '5', 'No Tear Off']}
                setValue={setSelectedLayer}
                setFieldValue={setFieldValue}
                innerHeight="45px"
                margin="10px 0 0 0"
                stateName={`customData.roofAreas.${indexProps}.layers`}
                className="top"
                error={
                  errors?.customData?.roofAreas?.[indexProps]?.layers &&
                  touched?.customData?.roofAreas?.[indexProps]?.layers
                    ? true
                    : false
                }
              />
            </SharedStyled.FlexBox>
          </Gap>
        </SaperatorDiv>
      </div>

      <SaperatorDiv gap={'0px'} className="column">
        <SharedStyled.FlexBox alignItems="center">
          <div>
            {constFields.map(({ name, options, type }: { name: string; options: string[]; type: string }, index) => (
              <>
                <Gap alignItem="center" className="row" key={name}>
                  <SharedStyled.Text fontSize="16px" fontWeight="bold">
                    {name}
                    {/* <br></br> */}
                    {
                      <SharedStyled.ErrorContainer>
                        <SharedStyled.ErrorMsg paddingTop="0">
                          <ErrorMessage
                            name={`customData.roofAreas.${indexProps}.${type}`}
                            component="div"
                            className="error-message"
                          />
                        </SharedStyled.ErrorMsg>
                      </SharedStyled.ErrorContainer>
                    }
                  </SharedStyled.Text>

                  <RadioButtonGroup
                    // disableLabelClick
                    radioButtons={options.map((option, optionIndex) => {
                      return {
                        id: `${type}${optionIndex + 1}${indexProps}`,
                        name: `customData.roofAreas.${indexProps}.${type}`,
                        // value: values.roofAreas[indexProps][type as keyof I_RoofAreas],
                        value: option === 'Yes' ? '1' : '0',
                        label: option,
                      }
                    })}
                    onChange={(e) => handleRadioChange(e, setFieldValue)}
                    defaultValue={
                      values?.customData?.roofAreas[indexProps][type] !== ''
                        ? Number(values?.customData?.roofAreas[indexProps][type]) > 0
                          ? '1'
                          : '0'
                        : null
                    }
                    // selectedType={selectedValues[`option${index + 1}`]}
                  />
                </Gap>
              </>
            ))}
          </div>
          {values.customData?.roofAreas?.length > 1 && (
            <div style={{ margin: '0 0 0 10px' }}>
              <Button className="fit delete" type="button" onClick={() => remove(indexProps)}>
                X
              </Button>
            </div>
          )}
        </SharedStyled.FlexBox>
      </SaperatorDiv>
    </SaperatorDivTwo>
  )
}

export default ProjectDetails
