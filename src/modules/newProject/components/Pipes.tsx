import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../styles/styled'

import CustomSelect from '../../../shared/customSelect/CustomSelect'
import { ErrorMessage } from 'formik'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import { handleWheel } from '../../../shared/helpers/util'

interface I_values {
  customData: {
    pipes: string[] | number[]
  }
}

interface I_PipesProps {
  setFieldValue: (field: string, value: any, shouldValidate?: boolean | undefined) => void
  values: I_values
  errors: any
  touched: any
}

export const Pipes: React.FC<I_PipesProps> = (props) => {
  const { setFieldValue, values, errors, touched } = props

  return (
    <>
      {' '}
      <SharedStyled.FlexBox marginTop="2px" flexDirection="column" alignItems="center">
        {/* <CustomSelect
          labelName="Pipes"
          value={values?.pipesCount}
          dropDownData={Array.from({ length: 45 }, (_, index) => `${index}`)}
          setValue={() => {}}
          setFieldValue={setFieldValue}
          innerHeight="45px"
          margin="10px 0 0 0"
          stateName="pipesCount"
          error={errors?.pipesCount && touched?.pipesCount ? true : false}
        /> */}
        <InputWithValidation
          labelName="Pipes"
          height="45px"
          stateName="pipesCount"
          inputType="number"
          onChange={(e) => {
            const { value } = e.target
            const parsedValue = parseInt(value, 10)
            if (!isNaN(parsedValue) && parsedValue <= 75 && parsedValue >= 0) {
              setFieldValue('pipesCount', parsedValue.toString())
            } else if (isNaN(parsedValue) || parsedValue < 0) {
              setFieldValue('pipesCount', '') // Clear the field or provide appropriate feedback
            } else {
              setFieldValue('pipesCount', '75') // Set the field to the maximum allowed value
            }
          }}
          onWheel={handleWheel}
          // setFieldValue={setFieldValue}
          error={errors?.pipesCount && touched?.pipesCount}
        />
      </SharedStyled.FlexBox>
      {/* <FieldArray name="customData.pipes">
        {({ push, remove }) => {
          const selectedNumber = parseInt(values?.pipesCount)
          for (let i = values?.customData?.pipes?.length; i < selectedNumber; i++) {
            push('')
          }

          for (let i = values.customData.pipes.length; i > selectedNumber; i--) {
            remove(values?.customData?.pipes?.length - 1)
          }

          return ( */}
      <>
        {new Array(parseInt(values?.pipesCount) || 0)?.fill('')?.map((_, index: number) => (
          <>
            <SharedStyled.AlignItem key={index} flexDirection="column">
              <span style={{ marginTop: '10px' }}>#{index + 1}:&nbsp;</span>
              <CustomSelect
                labelName={`Pipes #${index + 1} size`}
                error={false}
                value={values?.customData?.pipes[index] || ''}
                dropDownData={['1 1/4"', '1 1/2"', '2"', '3"', '4"', '2" split boot']}
                setValue={() => {}}
                setFieldValue={setFieldValue}
                innerHeight="45px"
                margin="10px 0 0 0"
                stateName={`customData.pipes.${index}`}
              />
            </SharedStyled.AlignItem>
          </>
        ))}
      </>
      {/* )
        }}
      </FieldArray> */}
    </>
  )
}
