import * as Yup from 'yup'

export enum skylightTypeEnum {
  CUSTOM = 'Custom Size',
  CRUB2x2 = 'Curb Mount 2x2 (curb: 25.5" x 25.5")',
  CRUB2x4 = 'Curb Mount 2x4 (curb: 25.5" x 49.5")',
}

export interface I_ExistingVenting {
  option: string
  lf: string
  ea: string
}
export interface I_ProjectInputs {
  name: string
  value: number | undefined
  unit: string
  _id: string
  desc?: string
}
export interface I_RoofAreas {
  name: string
  pitch: any
  size: any
  layers: string
  ventArea: string
  rmvPly: string
  instPly: string
  noAccess: string
  twoStory: string
}
export interface I_Chimneys {
  width: string
  length: string
}
export interface I_SkyLights {
  width: string | number
  length: string | number
  skylightType: string
}
export interface InitialValues {
  // companyId: string | undefined
  oppId: string | undefined
  createdBy: string | undefined
  name: string
  notes: string
  newProject: boolean
  projectType: string
  typeName: string
  inputValue: number[]
  existingVenting: I_ExistingVenting
  projectInputs: I_ProjectInputs[]
  bVentCount: string
  stemVentsCount: string
  sunTunnelsCount: string
  chimneysCount: string
  skylightsCount: string
  pipesCount: string | number
  // roofAreas: I_RoofAreas[]
  customData: {
    roofAreas: I_RoofAreas[]
    bVents: string[] | number[]
    stemVents: string[] | number[]
    sunTunnels: string[] | number[]
    pipes: string[] | number[]
    chimneys: I_Chimneys[]
    skylights: I_SkyLights[]
    // otherDetails: I_ProjectInputs[]
    eaves: string
    sideWall: string
    endWall: string
    ridges: string
    rakes: string
    valleys: string
    hips: string
    pitchChange: string
    splitPipe2: string
    pipeFlash4: string
    pipeFlash123: string
    canVentsExist: string | number
    ridgeVentExist: string | number
  }
}

export const mapping: any = {
  '1 1/4': '1 1/4"',
  '1 1/2': '1 1/2"',
  '2': '2"',
  '3': '3"',
  '4': '4"',
  '2 split boot': '2" split boot',
}

export const projectData = {
  // companyId: '',
  oppId: '',
  createdBy: '',
  name: '',
  notes: '',
  newProject: false,
  projectType: '',
  typeName: '',
  inputValue: [],
  existingVenting: { option: '', lf: '', ea: '' },
  projectInputs: [],
  bVentCount: '',
  stemVentsCount: '',
  sunTunnelsCount: '',
  chimneysCount: '',
  skylightsCount: '',
  pipesCount: '',
  customData: {
    pitch: '',
    roofAreas: [
      {
        name: 'Roof Area 1',
        pitch: '',
        size: '',
        layers: '',
        ventArea: '',
        rmvPly: '',
        instPly: '',
        noAccess: '',
        twoStory: '',
      },
    ],
    bVents: [],
    stemVents: [],
    sunTunnels: [],
    pipes: [],
    chimneys: [],
    skylights: [],
    // otherDetails: [],
    eaves: '',
    sideWall: '',
    endWall: '',
    ridges: '',
    rakes: '',
    valleys: '',
    hips: '',
    pitchChange: '',
    splitPipe2: '',
    pipeFlash4: '',
    pipeFlash123: '',
    canVentsExist: '',
    ridgeVentExist: '',
  },
}

export const projectDataNonReplacement = {
  oppId: '',
  clientId: '',
  createdBy: '',
  name: '',
  notes: '',
  newProject: false,
  projectType: '',
  inputValue: [],
  existingVenting: { option: '', lf: '', ea: '' },
  projectInputs: [],
  customData: {
    pitch: '',
  },
}

export const ProjectSchema = Yup.object().shape({
  name: Yup.string().min(2, 'Too Short!').max(200, 'Too Long!').required('Required'),
  notes: Yup.string(),
  projectType: Yup.string(),
  inputValue: Yup.array(),
  existingVenting: Yup.object().shape({
    option: Yup.string().required('Option is required'),
    lf: Yup.string(),
    ea: Yup.string(),
  }),
  // projectInputs: Yup.array(),
  bVentCount: Yup.string().required('Required'),
  stemVentsCount: Yup.string().required('Required'),
  sunTunnelsCount: Yup.string().required('Required'),
  chimneysCount: Yup.string().required('Required'),
  skylightsCount: Yup.string().required('Required'),
  pipesCount: Yup.string().required('Required'),
  customData: Yup.object().shape({
    roofAreas: Yup.array().of(
      Yup.object().shape({
        name: Yup.string().required('Required'),
        pitch: Yup.number().required('Required'),
        size: Yup.number().required('Required'),
        layers: Yup.string().required('Required'),
        ventArea: Yup.string().required('Required'),
        rmvPly: Yup.string().required('Required'),
        instPly: Yup.string().required('Required'),
        noAccess: Yup.string().required('Required'),
        twoStory: Yup.string().required('Required'),
      })
    ),
    bVents: Yup.array(),
    pipes: Yup.array(),
    stemVents: Yup.array(),
    sunTunnels: Yup.array(),
    chimneys: Yup.array(),
    skylights: Yup.array(),
    // otherDetails: Yup.array(),
    eaves: Yup.string().required('Required'),
    sideWall: Yup.string().required('Required'),
    endWall: Yup.string().required('Required'),
    ridges: Yup.string().required('Required'),
    rakes: Yup.string().required('Required'),
    valleys: Yup.string().required('Required'),
    hips: Yup.string().required('Required'),
    pitchChange: Yup.string().required('Required'),
    // splitPipe2: Yup.string().required('Required'),
    // pipeFlash4: Yup.string().required('Required'),
    // pipeFlash123: Yup.string().required('Required'),
    canVentsExist: Yup.string(),
    ridgeVentExist: Yup.string(),
  }),
})

export const ProjectSchemaRepair = Yup.object().shape({
  name: Yup.string().min(2, 'Too Short!').max(200, 'Too Long!').required('Required'),
  notes: Yup.string(),
  projectType: Yup.string(),
  inputValue: Yup.array(),
  existingVenting: Yup.object().shape({
    option: Yup.string(),
    lf: Yup.string(),
    ea: Yup.string(),
  }),
  // projectInputs: Yup.array(),
  customData: Yup.object().shape({
    pitch: Yup.string(),
  }),
})

export const reorderResponse = (response1: any, response2: any) => {
  // Ensure 'value' key is present in objects from response2
  const response2WithDefaultValue: any = response2.map((item: any) => ({
    ...item,
    value: item.value || 0,
  }))
  const idMap = response1.reduce((acc: any, obj: any) => {
    // Add 'value' key with default value of 0 if missing in objects from response1
    acc[obj._id] = { ...obj, value: obj.value || 0 }
    return acc
  }, {})

  // const reorderedResponse1 = response2WithDefaultValue.map((item: any) => idMap[item._id] || item)

  const reorderedResponse1 = response2WithDefaultValue.map((item: any) => ({
    ...(idMap[item._id] || { _id: item._id, value: 0 }),
    name: item.name || '', // Include name from response2
    desc: item.desc || '',
    unit: item.unit || '',
  }))
  console.log({ response1, response2 }, idMap, reorderedResponse1, response2WithDefaultValue)

  return reorderedResponse1
}
