import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../styles/styled'
import { Formik, Form, Field, FieldArray, ErrorMessage } from 'formik'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import RadioButtonGroup from '../../shared/radioButtonGroup/RadioButtonGroup'
import {
  convertKeyToStr,
  getDataFromLocalStorage,
  getMemberId,
  getNameFromId,
  getNameFrom_Id,
  getUnitSymbolFromId,
  handleWheel,
  isSuccess,
  notify,
  updateArrays,
} from '../../shared/helpers/util'
import * as Styled from './style'
import ProjectDetails from './components/ProjectDetails'
import RoofMeasurements from './components/RoofMeasurements'
import { useNavigate, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import {
  checkProject,
  createProject,
  deleteProject,
  getInputsApi,
  getProjectById,
  getProjectInputsApi,
  getProjectTypes,
  getUnitsApi,
} from '../../logic/apis/projects'
import Vents from './components/Vents'
import WLDropdown from './components/WLDropdown'
import { IFullUnit } from '../units/Units'
import Button from '../../shared/components/button/Button'
import { InputLabelWithValidation } from '../../shared/inputLabelWithValidation/InputLabelWithValidation'
import { loadReroofAreaArray } from './components/loadReroofAreaArrayMethod'
import { Pipes } from './components/Pipes'
import { projectMethod } from '../../shared/helpers/projectCalculations'
import {
  I_ProjectInputs,
  InitialValues,
  mapping,
  projectData,
  projectDataNonReplacement,
  ProjectSchema,
  reorderResponse,
} from './types'
import * as Yup from 'yup'
import { onlyNumber } from '../../shared/helpers/regex'
import { SLoader } from '../../shared/components/loader/Loader'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { StorageKey } from '../../shared/helpers/constants'
import { ErrorMsg } from '../contract/style'

const TYPES = ['roof-replacement', 'roof-repair']

const Action = ['Yes', 'No']
export interface IType {
  id: string
  name: string
  value: string
  label: string
  groups?: string[]
}
interface INewProjectProps {
  onClose?: () => void
  onSuccess?: () => void
}

export const Project = (props: INewProjectProps) => {
  const { onClose, onSuccess } = props
  const [types, setTypes] = useState<IType[]>([])
  const [action, setAction] = useState<IType[]>([])
  const [selectedType, setSelectedType] = useState('')
  const [selectedTypeName, setSelectedTypeName] = useState('')
  const [selectedLayer, setSelectedLayer] = useState('')
  const [selecteVenting, setSelecteVenting] = useState('')
  const [btnLoading, setBtnLoading] = useState(false)
  const [btnCopyLoading, setCopyBtnLoading] = useState(false)
  const [selectSchema, setSelectSchema] = useState(false)
  const [selectType, setSelectType] = useState<any>({})
  const [inputType, setinputType] = useState(true)
  const [ProjectType, setProjectType] = useState('')
  const [btnLoadingDelete, setBtnLoadingDelete] = useState(false)

  const [inputData, setInputData] = useState([])
  const [allInputData, setAllInputData] = useState([])
  const [projectTypesDrop, setProjectTypesDrop] = useState([])
  const [projectTypes, setProjectTypes] = useState([])
  const [replacementId, setReplacementId] = useState('')
  const [replacementType, setReplacementType] = useState(false)
  const [currentProjectId, setcurrentProjectID] = useState('')
  const [memberId, setMemberId] = useState('')
  const [isAccepted, setIsAccepted] = useState(false)
  const [projectById, setProjectProjectById] = useState<any>([])

  const [units, setUnits] = useState<IFullUnit[]>([])
  const [taskSearchTerm, setTaskSearchTerm] = useState('')
  const operationsFlag = location.pathname.includes('operations')
  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [initialValues, setInitialValues] = useState<InitialValues>(projectData)
  // const [initialValuesNonReplacement, setInitialValuesNonReplacement] = useState<any>(projectDataNonReplacement)

  let { oppId, contactId, projectId } = useParams()

  const [loading, setLoading] = useState(false)
  const [loadingProject, setLoadingProject] = useState(projectId ? true : false)

  const ProjectSchemaRepair = Yup.object().shape({
    name: Yup.string().min(2, 'Too Short!').max(200, 'Too Long!').required('Required'),
    notes: Yup.string(),
    projectType: Yup.string(),
    inputValue: Yup.array(),
    existingVenting: Yup.object().shape({
      option: Yup.string(),
      lf: Yup.string(),
      ea: Yup.string(),
    }),
    projectInputs: Yup.array()
      .of(
        Yup.object().shape({
          value: Yup.number()
            .typeError('Value must be a number') // Ensures value is a number
            .nullable(true), // Allows null/undefined
        })
      )
      .test('at-least-one-value', 'At least one input field is required', function (inputs) {
        // Ensure inputs exist and are an array
        if (!Array.isArray(inputs)) return false

        // Check if at least one object has a non-empty value
        return inputs.some((input) => typeof input?.value === 'number' && !isNaN(input.value) && input.value !== 0)
      }),

    customData: Yup.object().shape({
      pitch: Yup.string().when([], {
        is: () => selectType?.usesPitch,
        then: Yup.string()
          .required('Required')
          .test('maxDigits', 'Value should not be greater than 20', (value) => {
            const maxDigit = Math.max(...Object.keys(selectType?.pitchMod).map((key) => parseFloat(key))) // Assuming maxDigit is a variable containing the maximum value
            return value ? parseInt(value, 10) <= maxDigit : true
          })
          .matches(onlyNumber, 'Number should not contain decimal values'),
        otherwise: Yup.string(),
      }),
    }),
  })

  useEffect(() => {
    initData()
    initAction()
  }, [])
  const initAction = () => {
    Action.forEach((type) => {
      setAction((prev) => {
        prev.push({
          id: type,
          label: convertKeyToStr(type),
          name: type,
          value: type === 'Yes' ? '1' : '0',
        })
        return prev
      })
    })
  }

  const initData = () => {
    TYPES.forEach((type) => {
      setTypes((prev) => {
        prev.push({
          id: type,
          label: convertKeyToStr(type),
          name: type,
          value: type,
        })
        return prev
      })
    })
  }

  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false })
      if (isSuccess(res)) {
        const { unit } = res.data.data
        const tableData = unit.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              symbol: cur.symbol,
            },
          ]
        }, [])
        setUnits(unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const getTypeFromId = (id: string, options: any) => {
    const selectedOption = options.find((option: any) => option._id === id)
    return selectedOption ? selectedOption.typeReplacement : ''
  }

  const onTypeChange = (obj: any) => {
    console.log({ obj })
    setSelectedType(obj.value)
    setSelectedTypeName(obj.name)
    setcurrentProjectID(obj.id)
    setSelectSchema(getTypeFromId(obj.id, projectTypes))
    setSelectType(projectTypes.find((v) => v._id === obj.id))
    setinputType(true)
  }

  useEffect(() => {
    initFetch()
    getMemberId(setMemberId)
    fetchUnitsData()
  }, [])

  useEffect(() => {
    if (projectId) {
      initFetchProjectById()
    }
  }, [])

  const getCheckboxValueForRoofAreas = (value: number) => {
    return value === 0 ? '1' : '0'
  }

  useEffect(() => {
    if (projectId) {
      const type = getNameFromId(projectById.projectType, projectTypesDrop)
      setSelectedType(projectById.projectType)
      setProjectType(type)
      setSelectSchema(getTypeFromId(projectById.projectType, projectTypes))
      setSelectType(projectTypes.find((v) => v._id === projectById.projectType))
    }
  }, [projectById, projectTypesDrop, projectId])

  useEffect(() => {
    if (
      inputData?.length &&
      projectById?.projectInputs?.length &&
      currentProjectId !== '' &&
      replacementId !== '' &&
      // currentProjectId !== replacementId &&
      projectId
    ) {
      const reorderedResponse1 = reorderResponse(projectById?.projectInputs, inputData)
      setInitialValues((prevValues) => ({
        ...prevValues,
        projectInputs: reorderedResponse1,
      }))
    }
  }, [inputData, projectById?.projectInputs, currentProjectId, replacementId, projectId])

  const initFetchProjectById = async () => {
    try {
      const res = await getProjectById({ projectId: projectId, deleted: false })
      const res1 = await checkProject(projectId!)
      if (isSuccess(res) && isSuccess(res1)) {
        const { project } = res?.data?.data
        setIsAccepted(res1?.data)
        setProjectProjectById(project)
        setcurrentProjectID(project?.projectType)
        setInitialValues((prevValues) => ({
          ...prevValues,
          oppId: project.oppId,
          contactId: project.contactId,
          createdBy: project.createdBy,
          name: project.name,
          notes: project.notes,
          newProject: false,
          projectType: project?.projectType,
          inputValue: project?.projectInputs,
          pipesCount: `${project?.customData?.pipes?.length || 0}`,
          bVentCount: `${project?.customData?.bVents?.length || 0}`,
          stemVentsCount: `${project?.customData?.stemVents?.length || 0}`,
          sunTunnelsCount: `${project?.customData?.sunTunnels?.length || 0}`,
          chimneysCount: `${project?.customData?.chimneys?.length || 0}`,
          skylightsCount: `${project?.customData?.skylights?.length || 0}`,
          // inputValue: project?.projectInputs.map(({ value }: { value: number }) => value),
          existingVenting: {
            option:
              project?.customData?.canVentsExist &&
              (project?.customData?.ridgeVentExist === '' || project?.customData?.ridgeVentExist === 0)
                ? 'Cans'
                : (project?.customData?.canVentsExist === '' || project?.customData?.canVentsExist === 0) &&
                  project?.customData?.ridgeVentExist
                ? 'Ridge Vent'
                : project?.customData?.canVentsExist && project?.customData?.ridgeVentExist
                ? 'Ridge Vent and Cans'
                : 'No venting',
            lf: '',
            ea: '',
          },
          projectInputs: [],
          // projectInputs: project?.projectInputs,
          customData: {
            pitch: project?.customData?.pitch || 0,
            roofAreas: project?.customData?.reroofAreas?.map((item: any) => {
              return {
                ...item,
                size: item?.size ? item?.size : item?.install,
                noAccess: getCheckboxValueForRoofAreas(Number(item?.noAccess)),
                layers: item.layers ?? 'No Tear Off',
              }
            }) || [
              {
                name: '',
                pitch: '',
                size: '',
                layers: '',
                ventArea: '',
                rmvPly: '',
                instPly: '',
                noAccess: '',
                twoStory: '',
              },
            ],
            bVents: project?.customData?.bVents?.map((item: any) => item + '"') || [],
            stemVents: project?.customData?.stemVents?.map((item: any) => item + '"') || [],
            pipes: project?.customData?.pipes?.map((item: any) => mapping[item] || item) || [],
            sunTunnels: project?.customData?.sunTunnels?.map((item: any) => item + '"') || [],
            chimneys: project?.customData?.chimneys || [],
            skylights: project?.customData?.skylights || [],
            // otherDetails: project?.projectInputs,
            eaves: project?.customData?.eaves,
            sideWall: project?.customData?.sideWall,
            endWall: project?.customData?.endWall,
            ridges: project?.customData?.ridges,
            rakes: project?.customData?.rakes,
            valleys: project?.customData?.valleys,
            hips: project?.customData?.hips,
            pitchChange: project?.customData?.pitchChange,
            splitPipe2: project?.customData?.splitPipe2,
            pipeFlash4: project?.customData?.pipeFlash4,
            pipeFlash123: project?.customData?.pipeFlash123,
            canVentsExist: project?.customData?.canVentsExist,
            ridgeVentExist: project?.customData?.ridgeVentExist,
          },
          projectId: project._id,
        }))
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('Failed init fetch ProjectById', error)
    } finally {
      setLoadingProject(false)
    }
  }

  const handleDelete = async () => {
    setBtnLoadingDelete(true)
    try {
      const res = await deleteProject({ id: projectById._id })
      if (isSuccess(res)) {
        notify('Project deleted successfully!', 'success')
        navigate(-1)
        setBtnLoadingDelete(false)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      notify('Failed to Delete project!', 'error')
      console.log('Submit error', err)
      setBtnLoadingDelete(false)
    }
  }

  const initFetch = async () => {
    try {
      setLoading(true)
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(
          ({ _id, name, typeReplacement }: { _id: string; name: string; typeReplacement: boolean }) => ({
            name: name,
            id: _id,
            value: _id,
            label: name,
            typeReplacement: typeReplacement,
          })
        )
        const filteredData = projectType.filter(
          ({ typeReplacement }: { typeReplacement: boolean }) => typeReplacement === true
        )
        const typeReplacementTrueId = filteredData[0] ? filteredData[0]._id : null
        setReplacementId(typeReplacementTrueId)
        setProjectTypes(projectType)
        setLoading(false)
        setProjectTypesDrop(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (selectedType) {
      fetchProjectInputType()
      fetchInputsData()
      setTaskSearchTerm('')
    }
  }, [selectedType])

  const fetchProjectInputType = async () => {
    try {
      const res = await getProjectInputsApi(selectedType!, projectId)
      if (isSuccess(res)) {
        const { inputArray } = res?.data?.data
        const tableData = inputArray.map(
          ({
            name,
            unit,
            _id,
            desc,
            code,
            value,
          }: {
            name: string
            unit: string
            _id: string
            desc: string
            code: string
            value: string
          }) => ({
            name,
            unit,
            _id,
            desc,
            code,
            value: value ?? '',
          })
        )
        setinputType(false)
        setInputData(tableData)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      setinputType(false)
      console.log(error)
    }
  }

  const fetchInputsData = async () => {
    try {
      const res = await getInputsApi({
        deleted: false,
        type: selectedType,
      })
      if (isSuccess(res)) {
        const { input } = res.data.data
        setAllInputData(input)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const handleSubmit = async (submittedValues: InitialValues) => {
    if (contactId) {
      submittedValues.oppId = oppId
      submittedValues.contactId = contactId
      submittedValues.createdBy = memberId
      submittedValues.newProject = true
      submittedValues.projectType = selectedType
    }

    if (!submittedValues.newProject) {
      const confirmed = window.confirm(
        'Are you sure you want to edit the project ? \n All existing pricing will be lost. You can copy to a new project to save this pricing'
      )
      if (!confirmed) {
        return 0
      }
    }

    if (projectId && submittedValues.newProject) {
      setCopyBtnLoading(true)
    } else {
      setBtnLoading(true)
    }
    if (currentProjectId === replacementId) {
      try {
        const {
          inputValue,
          existingVenting,
          skylightsCount,
          // projectInputs,
          chimneysCount,
          sunTunnelsCount,
          bVentCount,
          pipesCount,
          stemVentsCount,
          ...restObject
        } = submittedValues

        const formatedRoofArea = loadReroofAreaArray(submittedValues.customData.roofAreas)
        restObject.customData.roofAreas = formatedRoofArea

        console.log({ restObject }, bVentCount, pipesCount, stemVentsCount)
        updateArrays(restObject, 'pipes', false, Number(pipesCount))
        updateArrays(restObject, 'bVents', true, Number(bVentCount))
        updateArrays(restObject, 'stemVents', true, Number(stemVentsCount))
        updateArrays(restObject, 'sunTunnels', true, Number(sunTunnelsCount))

        restObject.customData.chimneys = restObject.customData.chimneys?.slice(0, Number(chimneysCount))
        restObject.customData.skylights = restObject.customData.skylights?.slice(0, Number(skylightsCount))

        if (restObject.customData.skylights.length) {
          const updatedSkylights = restObject.customData.skylights.map((skylight) => {
            if (skylight.skylightType === 'Curb Mount 2x2 (curb: 25.5" x 25.5")') {
              return {
                ...skylight,
                width: 25.5,
                length: 25.5,
              }
            } else if (skylight.skylightType === 'Curb Mount 2x4 (curb: 25.5" x 49.5")') {
              return {
                ...skylight,
                width: 25.5,
                length: 49.5,
              }
            }
            return skylight
          })
          restObject.customData.skylights = updatedSkylights
        }

        if (restObject.customData.roofAreas.length) {
          const updatedInNumbers = restObject.customData.roofAreas.map((items) => ({
            ...items,
            layers: items.layers === 'No Tear Off' ? null : Number(items.layers),
          }))
          restObject.customData.roofAreas = updatedInNumbers
        }

        if (existingVenting.option === 'Ridge Vent') {
          restObject.customData.canVentsExist = 0
        } else if (existingVenting.option === 'Cans') {
          restObject.customData.ridgeVentExist = 0
        } else if (existingVenting.option === 'No venting') {
          restObject.customData.canVentsExist = 0
          restObject.customData.ridgeVentExist = 0
        }

        const selectedTypeObject = projectTypes?.find((v) => v._id === selectedType)
        const finalObject = projectMethod(restObject, selectedTypeObject, allInputData)
        // const finalObject = projectMethod(restObject, selectedTypeObject, currentCompany._id, allInputData, inputData)
        console.log({ finalObject, restObject })

        const res = await createProject(finalObject)
        if (isSuccess(res)) {
          notify('Project created successfully!', 'success')
          setBtnLoading(false)
          navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/contract/${res?.data?.data?.id}`)
          onSuccess?.()
        } else {
          notify(res?.data?.message, 'error')
          setBtnLoading(false)
        }
      } catch (err) {
        console.log('Submit error', err)
      } finally {
        setBtnLoading(false)
        setCopyBtnLoading(false)
      }
    } else {
      try {
        submittedValues.customData = { pitch: submittedValues.customData.pitch }
        const projectFilteredInputs: any = submittedValues.projectInputs
          .map(({ _id, value }) => ({ _id, value }))
          .filter((v) => v.value > 0)
        // .map(({ _id, value }) => ({ _id, value }))

        const {
          inputValue,
          existingVenting,
          // customData,
          projectInputs,
          skylightsCount,
          stemVentsCount,
          sunTunnelsCount,
          pipesCount,
          chimneysCount,
          bVentCount,
          ...restObject
        } = submittedValues
        const finalObject = { ...restObject, projectInputs: projectFilteredInputs, currDate: new Date().toISOString() }
        console.log({ inputData, finalObject, restObject })
        const res = await createProject(finalObject)
        if (isSuccess(res)) {
          notify('Project created successfully!', 'success')
          setBtnLoading(false)
          navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/contract/${res?.data?.data?.id}`)
        } else {
          notify(res?.data?.message, 'error')
          // setInitialValues((prevValues) => ({
          //   ...prevValues,
          //   customData: {
          //     ...prevValues.customData,
          //     bVents: finalObject?.customData?.bVents?.map((item) => item + '"') || [],
          //     stemVents: finalObject?.customData?.stemVents?.map((item) => item + '"') || [],
          //     sunTunnels: finalObject?.customData?.sunTunnels?.map((item) => item + '"') || [],
          //     pipes: finalObject?.customData?.pipes?.map((item) => mapping[item] || item) || [],
          //   },
          // }))
          setBtnLoading(false)
        }
      } catch (error) {
        console.log(error, 'Submit error')
      } finally {
        setBtnLoading(false)
        setCopyBtnLoading(false)
      }
    }
  }
  console.log({ initialValues })
  return loadingProject ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
    </>
  ) : (
    <>
      <Styled.NewProjectCont>
        <SharedStyled.Content borderRadius="0" width="100%" disableBoxShadow={true} noPadding={true}>
          <SharedStyled.ContentHeader style={{ fontSize: '1.75rem' }} textAlign="left">
            {contactId ? 'New' : 'Edit'} Project {projectId && `For ${initialValues.name}`}
          </SharedStyled.ContentHeader>
          <SharedStyled.FlexBox flexDirection="column" gap="10px" width="100%">
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              validateOnChange={true}
              validateOnBlur={false}
              enableReinitialize={true}
              validationSchema={selectSchema ? ProjectSchema : ProjectSchemaRepair}
            >
              {({ touched, errors, resetForm, values, setFieldValue }) => {
                const scrollToFirstError1 = () => {
                  const errorFields = Object.keys(errors) // Get fields with errors
                  const firstErrorOrEmptyField = errorFields.find((field: any) => errors[field])
                  if (firstErrorOrEmptyField) {
                    const element = document.querySelector(`[name="${firstErrorOrEmptyField}"]`)
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                    }
                  }
                }
                const customError = () => {
                  if (
                    !values.projectInputs.some(
                      (input) => typeof input?.value === 'number' && !isNaN(input.value) && input.value !== 0
                    )
                  ) {
                    notify('At least one task field is required', 'error')
                  }
                }
                // console.log({ errors, touched, values })
                const scrollToFirstError = () => {
                  const allErrors = errors || {} // Get all errors in the form

                  const errorFields = Object.keys(allErrors) // Get all fields with errors

                  // Check for errors within customData
                  const userInputDataErrors = allErrors.customData || {}
                  const userInputDataFields = Object.keys(userInputDataErrors)

                  for (let i = 0; i < userInputDataFields.length; i++) {
                    const field = `customData.${userInputDataFields[i]}`
                    const element = document.querySelector(`[name="${field}"]`)

                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                      return // Exit after scrolling to the first missing field within customData
                    }
                  }

                  // Check for errors outside customData
                  for (let i = 0; i < errorFields.length; i++) {
                    const field = errorFields[i]
                    if (!field.startsWith('customData.')) {
                      const element = document.querySelector(`[name="${field}"]`)

                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                        return // Exit after scrolling to the first missing field outside customData
                      }
                    }
                  }
                }

                useEffect(() => {
                  if (contactId) {
                    setInitialValues((prev) => ({
                      ...prev,
                      name: values.name,
                      notes: values.notes,
                      typeName: values.typeName,
                      projectInputs: inputData,
                    }))
                  }
                }, [inputData])

                return (
                  <Form className="form">
                    <InputWithValidation
                      labelName="Project Name"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                    />
                    <Styled.TextArea
                      name="notes"
                      placeholder="Project  Notes"
                      component="textarea"
                      as={Field}
                      marginTop="8px"
                      height="52px"
                    />

                    <CustomSelect
                      labelName="Project Type"
                      value={getNameFrom_Id(values?.projectType, projectTypes) || values.typeName}
                      dropDownData={projectTypesDrop?.map((v) => v?.name)}
                      setValue={(e: any) => onTypeChange(projectTypesDrop?.find((v) => v?.name === e))}
                      setFieldValue={setFieldValue}
                      innerHeight="45px"
                      margin="10px 0 0 0"
                      stateName="typeName"
                      disabled={!!projectId}
                    />

                    <SharedStyled.FlexCol>
                      {currentProjectId !== '' && replacementId !== '' && currentProjectId === replacementId ? (
                        <SharedStyled.FlexBox width="100%" flexDirection="column">
                          {inputData?.length ? (
                            <SharedStyled.ContentHeader textAlign="left">Project Details</SharedStyled.ContentHeader>
                          ) : null}

                          <FieldArray name="customData.roofAreas">
                            {({ remove, push }) => (
                              <>
                                {values.customData?.roofAreas?.length > 0 &&
                                  values.customData?.roofAreas?.map((data: any, index: number) => (
                                    <Styled.SaperatorDiv>
                                      <ProjectDetails
                                        setSelectedLayer={setSelectedLayer}
                                        setFieldValue={setFieldValue}
                                        selectedLayer={selectedLayer}
                                        index={index}
                                        action={action}
                                        values={values}
                                        data={data}
                                        errors={errors}
                                        touched={touched}
                                        remove={remove}
                                      />
                                    </Styled.SaperatorDiv>
                                  ))}
                                <SharedStyled.ButtonContainer justifyContent="start" margin="24px 0 16px 0">
                                  <Button
                                    type="button"
                                    className="fit outline"
                                    onClick={() =>
                                      push({
                                        name: `Roof Area ${values.customData?.roofAreas?.length + 1}`,
                                        pitch: '',
                                        size: '',
                                        layers: '',
                                        ventArea: '',
                                        rmvPly: '',
                                        instPly: '',
                                        noAccess: '',
                                        twoStory: '',
                                      })
                                    }
                                  >
                                    Add roof area
                                  </Button>
                                  {/* &emsp; */}
                                  {/* {values?.customData?.roofAreas?.length > 1 && (
                                    <Button
                                      className="fit delete"
                                      type="button"
                                      onClick={() => {
                                        values?.customData?.roofAreas?.length > 1
                                          ? remove(values?.customData?.roofAreas?.length - 1)
                                          : null
                                      }}
                                    >
                                      Remove roof area
                                    </Button>
                                  )} */}
                                </SharedStyled.ButtonContainer>
                              </>
                            )}
                          </FieldArray>

                          <Styled.SaperatorDivThree className="select">
                            <RoofMeasurements
                              values={values}
                              setSelecteVenting={setSelecteVenting}
                              setFieldValue={setFieldValue}
                              inputData={inputData}
                              errors={errors}
                              touched={touched}
                            />
                            <div>
                              <SharedStyled.Text>Roof Details</SharedStyled.Text>
                              <Pipes setFieldValue={setFieldValue} values={values} errors={errors} touched={touched} />

                              <Vents setFieldValue={setFieldValue} values={values} errors={errors} touched={touched} />

                              <WLDropdown
                                setFieldValue={setFieldValue}
                                values={values}
                                errors={errors}
                                touched={touched}
                              />
                            </div>
                            <div>
                              <SharedStyled.FlexCol gap="8px">
                                <SharedStyled.Text>Other Details</SharedStyled.Text>
                                {inputType ? (
                                  <>
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                    <SLoader width={100} height={43} isPercent={true} />
                                  </>
                                ) : (
                                  <SharedStyled.FlexRow margin="4px 0 0 0" className="other-details">
                                    {values?.projectInputs?.map(
                                      (
                                        { name, unit, desc }: { name: string; unit: string; desc: string },
                                        index: number
                                      ) => (
                                        <div key={index}>
                                          <InputLabelWithValidation
                                            // stateName={`inputValue[${index}]`}
                                            stateName={`projectInputs[${index}].value`}
                                            inputType="number"
                                            labelName={name}
                                            labelUnit={getUnitSymbolFromId(unit, units)?.split(' ')[0]}
                                            info={true}
                                            desc={desc}
                                            onWheel={handleWheel}
                                          />
                                        </div>
                                      )
                                    )}
                                  </SharedStyled.FlexRow>
                                )}
                              </SharedStyled.FlexCol>
                            </div>
                          </Styled.SaperatorDivThree>

                          {/* {inputData?.length ? ( */}
                          {contactId && (
                            <SharedStyled.FlexRow margin="34px 0 0 0">
                              <Button
                                width="max-content"
                                type="submit"
                                onClick={() => {
                                  scrollToFirstError()
                                  scrollToFirstError1()
                                }}
                                isLoading={btnLoading}
                                // onClick={customError}
                              >
                                Save & Calculate
                              </Button>
                              <Button width="max-content" onClick={() => navigate(-1)} type="button">
                                Go Back
                              </Button>
                            </SharedStyled.FlexRow>
                          )}

                          {/* ) : (
                            <p>No inputs found</p>
                          )} */}

                          {projectId && (
                            <SharedStyled.ButtonContainer marginTop="10px">
                              <Styled.SaperatorDiv>
                                <Button
                                  width="max-content"
                                  type="submit"
                                  disabled={isAccepted}
                                  tooltip={!isAccepted ? '' : `Can’t edit a project that is in an accepted Order`}
                                  tooltipWidth="150px"
                                  isLoading={btnLoading}
                                  // onClick={customError}
                                >
                                  Save & Calculate
                                </Button>
                                {/* <Styled.Gap></Styled.Gap> */}
                                <Button width="max-content" onClick={() => navigate(-1)} type="button">
                                  Go Back
                                </Button>
                                <Button
                                  type="submit"
                                  isLoading={btnCopyLoading}
                                  onClick={() => {
                                    setInitialValues((prev) => ({
                                      ...prev,
                                      name: `${initialValues?.name} Copy`,
                                      newProject: true,
                                      projectId: null,
                                      createdBy: currentMember._id!,
                                    }))
                                  }}
                                  bgColor="#1aa41a"
                                  width="max-content"
                                >
                                  Copy Project
                                </Button>
                              </Styled.SaperatorDiv>
                              {!operationsFlag && (
                                <Button
                                  onClick={handleDelete}
                                  className="fit delete"
                                  type="button"
                                  disabled={isAccepted}
                                  isLoading={btnLoadingDelete}
                                >
                                  Delete Project
                                </Button>
                              )}
                            </SharedStyled.ButtonContainer>
                          )}
                        </SharedStyled.FlexBox>
                      ) : currentProjectId === '' ? (
                        <></>
                      ) : inputType ? (
                        <SharedStyled.FlexCol gap="8px">
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                          <SLoader width={50} height={43} isPercent={true} />
                        </SharedStyled.FlexCol>
                      ) : (
                        <SharedStyled.FlexBox width="100%" flexDirection="column" className="roof-repair">
                          {selectType?.usesPitch && (
                            <>
                              <SharedStyled.FlexBox width="40%" flexDirection="column" marginTop="10px" gap="4px">
                                {/* <SharedStyled.ValueInputLabel>Pitch</SharedStyled.ValueInputLabel> */}
                                <InputLabelWithValidation
                                  stateName={`customData.pitch`}
                                  labelName="Pitch"
                                  inputType="number"
                                  // isSmall
                                  error={errors?.customData?.pitch ? true : false}
                                  labelUnit="/12"
                                  onWheel={handleWheel}
                                />
                              </SharedStyled.FlexBox>
                            </>
                          )}

                          <br />

                          {inputData?.length ? (
                            <>
                              <SharedStyled.ContentHeader textAlign="left">Project Details</SharedStyled.ContentHeader>
                              <SharedStyled.FlexBox width="80%" marginTop="10px">
                                <SharedStyled.SearchInput
                                  border="1px solid lightgrey"
                                  bgColor="#FFFFF"
                                  color="#000000"
                                  borderRadius="5px"
                                  padding="0 5px 0 15px"
                                  width="50%"
                                  height="45px"
                                  fontSize="14px"
                                  type="search"
                                  placeholder="Search tasks..."
                                  onChange={(e) => setTaskSearchTerm(e.target.value)}
                                />
                              </SharedStyled.FlexBox>
                            </>
                          ) : null}

                          <SharedStyled.FlexCol gap="8px">
                            {values?.projectInputs
                              ?.filter(
                                (item) =>
                                  !taskSearchTerm || item.name.toLowerCase().includes(taskSearchTerm.toLowerCase())
                              )
                              .map((item, filteredIndex) => {
                                const originalIndex = values.projectInputs.findIndex(
                                  (original) => original._id === item._id
                                )
                                return (
                                  <SharedStyled.SingleFieldNameContainer
                                    alignItem="self-start"
                                    marginTop="10px"
                                    key={item._id || filteredIndex}
                                    flexDirection="column"
                                  >
                                    <InputLabelWithValidation
                                      stateName={`projectInputs[${originalIndex}].value`}
                                      labelName={item.name}
                                      inputType="number"
                                      labelUnit={getUnitSymbolFromId(item.unit, units)?.split(' ')[0]}
                                      info={true}
                                      onWheel={handleWheel}
                                      desc={item.desc}
                                      error={
                                        !!errors?.projectInputs && touched?.projectInputs?.[originalIndex]?.value
                                          ? true
                                          : false
                                      }
                                    />
                                  </SharedStyled.SingleFieldNameContainer>
                                )
                              })}
                          </SharedStyled.FlexCol>

                          {contactId && (
                            <SharedStyled.FlexRow margin="34px 0 0 0">
                              <Button
                                type="submit"
                                isLoading={btnLoading}
                                onClick={() => scrollToFirstError1()}
                                className="fit"
                                onClick={customError}
                              >
                                Save & Calculate
                              </Button>
                              <Button width="max-content" onClick={() => navigate(-1)} type="button">
                                Go Back
                              </Button>
                            </SharedStyled.FlexRow>
                          )}

                          {projectId && (
                            <SharedStyled.ButtonContainer marginTop="10px">
                              <Styled.SaperatorDiv>
                                <Button
                                  width="max-content"
                                  type="submit"
                                  disabled={isAccepted}
                                  isLoading={btnLoading}
                                  tooltip={!isAccepted ? '' : `Can’t edit a project that is in an accepted Order`}
                                  tooltipWidth="150px"
                                  onClick={customError}
                                >
                                  Save & Calculate
                                </Button>
                                {/* <Styled.Gap></Styled.Gap> */}
                                <Button width="max-content" onClick={() => navigate(-1)} type="button">
                                  Go Back
                                </Button>
                                <Button
                                  type="submit"
                                  isLoading={btnCopyLoading}
                                  onClick={() => {
                                    setInitialValues((prev) => ({
                                      ...prev,
                                      name: `${initialValues?.name} Copy`,
                                      newProject: true,
                                      projectId: null,
                                      createdBy: currentMember._id!,
                                    }))
                                  }}
                                  bgColor="#1aa41a"
                                  width="max-content"
                                >
                                  Copy Project
                                </Button>
                              </Styled.SaperatorDiv>
                              {!operationsFlag && (
                                <Button
                                  onClick={handleDelete}
                                  className="fit delete"
                                  type="button"
                                  disabled={isAccepted}
                                  isLoading={btnLoadingDelete}
                                >
                                  Delete Project
                                </Button>
                              )}
                            </SharedStyled.ButtonContainer>
                          )}
                        </SharedStyled.FlexBox>
                      )}
                    </SharedStyled.FlexCol>
                  </Form>
                )
              }}
            </Formik>
          </SharedStyled.FlexBox>
        </SharedStyled.Content>
      </Styled.NewProjectCont>
    </>
  )
}

export default Project
