import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../styles/styled'
import { I_Opportunity } from '../opportunity/Opportunity'
import { Formik, Form, Field, FieldArray, ErrorMessage } from 'formik'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import RadioButtonGroup from '../../shared/radioButtonGroup/RadioButtonGroup'
import {
  convertKeyToStr,
  getDataFromLocalStorage,
  getMemberId,
  getUnitSymbolFromId,
  handleWheel,
  isSuccess,
  notify,
  updateArrays,
} from '../../shared/helpers/util'
import * as Styled from './style'
import ProjectDetails from './components/ProjectDetails'
import RoofMeasurements from './components/RoofMeasurements'
import { useNavigate, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import {
  createProject,
  getInputsApi,
  getProjectInputs<PERSON>pi,
  getProjectTypes,
  getUnits<PERSON>pi,
} from '../../logic/apis/projects'
import Vents from './components/Vents'
import WLDropdown from './components/WLDropdown'
import { IFullUnit } from '../units/Units'
import Button from '../../shared/components/button/Button'
import { InputLabelWithValidation } from '../../shared/inputLabelWithValidation/InputLabelWithValidation'
import { loadReroofAreaArray } from './components/loadReroofAreaArrayMethod'
import { Pipes } from './components/Pipes'
import { projectMethod } from '../../shared/helpers/projectCalculations'
import { InitialValues, projectData, ProjectSchema, ProjectSchemaRepair } from './types'
import { StorageKey } from '../../shared/helpers/constants'

const TYPES = ['roof-replacement', 'roof-repair']

const Action = ['Yes', 'No']
export interface IType {
  id: string
  name: string
  value: string
  label: string
  groups?: string[]
  typeReplacement?: boolean
  usesPitch?: boolean
}
interface INewProjectProps {
  onClose?: () => void
  onSuccess?: () => void
}

export const NewProject = (props: INewProjectProps) => {
  const { onClose, onSuccess } = props
  const [oppData, setOppData] = useState<I_Opportunity>()
  const [types, setTypes] = useState<IType[]>([])
  const [action, setAction] = useState<IType[]>([])
  const [selectedType, setSelectedType] = useState('')
  const [selectedTypeName, setSelectedTypeName] = useState('')
  const [selectedLayer, setSelectedLayer] = useState('')
  const [selecteVenting, setSelecteVenting] = useState('')
  const [selecteChimneys, setSelecteChimneys] = useState('')
  const [selecteSkylights, setSelecteSkylights] = useState('')
  const [loading, setLoading] = useState(false)
  const [btnLoading, setBtnLoading] = useState(false)
  const [selectSchema, setSelectSchema] = useState(false)
  const [inputType, setinputType] = useState(true)

  const [inputData, setInputData] = useState([])
  const [allInputData, setAllInputData] = useState([])
  const [additionalDropdownsSunTunnel, setAdditionalDropdownsSunTunnel] = useState([])
  const [projectTypesDrop, setProjectTypesDrop] = useState([])
  const [projectTyperes, setProjectTypes] = useState([])
  const [stemSunTunnel, setSelecteSunTunnel] = useState('')
  const [replacementId, setReplacementId] = useState('')
  const [currentProjectId, setcurrentProjectID] = useState('')
  const [memberId, setMemberId] = useState('')
  const [units, setUnits] = useState<IFullUnit[]>([])
  const operationsFlag = location.pathname.includes('operations')
  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [initialValues, setInitialValues] = useState<InitialValues>(projectData)

  let { oppId, clientId } = useParams()

  useEffect(() => {
    initData()
    initAction()
  }, [])
  const initAction = () => {
    Action.forEach((type) => {
      setAction((prev) => {
        prev.push({
          id: type,
          label: convertKeyToStr(type),
          name: type,
          value: type === 'Yes' ? '1' : '0',
        })
        return prev
      })
    })
  }

  const initData = () => {
    TYPES.forEach((type) => {
      setTypes((prev) => {
        prev.push({
          id: type,
          label: convertKeyToStr(type),
          name: type,
          value: type,
        })
        return prev
      })
    })
  }

  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false })
      if (isSuccess(res)) {
        const { unit } = res.data.data
        const tableData = unit.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              symbol: cur.symbol,
            },
          ]
        }, [])
        setUnits(unit)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const handleNumberChangeForSkylights = (value: string) => {
    setSelecteSkylights(value)
    const dropdowns: any = Array.from({ length: Number(value) }, (_, index) => index)
  }

  const handleNumberChangeForChimney = (value: string) => {
    setSelecteChimneys(value)
    const dropdowns: any = Array.from({ length: Number(value) }, (_, index) => index)
  }
  const getTypeFromId = (id: string, options: any) => {
    const selectedOption = options.find((option: any) => option._id === id)
    return selectedOption ? selectedOption.typeReplacement : ''
  }

  const onTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedType(event.target.value)
    setSelectedTypeName(event.target.name)
    setcurrentProjectID(event.target.id)
    setSelectSchema(getTypeFromId(event.target.id, projectTyperes))

    setinputType(true)
  }
  useEffect(() => {
    initFetch()
    getMemberId(setMemberId)
    fetchUnitsData()
  }, [])

  const initFetch = async () => {
    try {
      setLoading(true)
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        const filteredData = projectType.filter(
          ({ typeReplacement }: { typeReplacement: boolean }) => typeReplacement === true
        )
        const typeReplacementTrueId = filteredData[0] ? filteredData[0]._id : null
        setReplacementId(typeReplacementTrueId)
        setProjectTypes(projectType)
        setLoading(false)
        setProjectTypesDrop(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProjectInputType()
    fetchInputsData()
  }, [selectedTypeName])

  const fetchProjectInputType = async () => {
    try {
      const res = await getProjectInputsApi(selectedType!, projectId)
      if (isSuccess(res)) {
        const { inputArray } = res?.data?.data
        const tableData = inputArray.map(
          ({
            name,
            unit,
            _id,
            desc,
            code,
            value,
          }: {
            name: string
            unit: string
            _id: string
            desc: string
            code: string
            value: string
          }) => ({
            name,
            unit,
            _id,
            desc,
            code,
            value,
          })
        )
        setinputType(false)
        setInputData(tableData)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      setinputType(false)
      console.log(error)
    }
  }

  const fetchInputsData = async () => {
    try {
      const res = await getInputsApi({
        deleted: false,
        type: selectedType,
        // isNew: currentProjectId !== '' && replacementId !== '' && currentProjectId === replacementId ? true : false,
        isNew: false,
      })
      if (isSuccess(res)) {
        const { input } = res.data.data
        // const filteredOptions = input.filter((option: any) => !option.hidden)
        // const tableData = filteredOptions.map(
        //   ({
        //     name,
        //     unit,
        //     _id,
        //     desc,
        //     code,
        //     value,
        //   }: {
        //     name: string
        //     unit: string
        //     _id: string
        //     desc: string
        //     code: string
        //     value: string
        //   }) => ({
        //     name,
        //     unit,
        //     _id,
        //     desc,
        //     code,
        //     value,
        //   })
        // )
        // setinputType(false)
        setAllInputData(input)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      // setinputType(false)
      console.log('Failed init fetch', err)
    }
  }
  const dropdownValues: string[] = Array.from({ length: 21 }, (_, index) => `${index}`)

  const handleNumberChangeForSunTunnels = (value: string) => {
    setSelecteSunTunnel(value)
    const dropdowns: any = Array.from({ length: Number(value) }, (_, index) => index)
    setAdditionalDropdownsSunTunnel(dropdowns)
  }

  const handleSubmit = async (submittedValues: InitialValues) => {
    setBtnLoading(true)
    if (currentProjectId === replacementId) {
      try {
        const inputObject = inputData.map(
          ({ name, _id, unit, desc }: { name: string; _id: string; unit: string; desc: String }, index) => ({
            name: name,
            _id: _id,
            unit: unit,
            value: submittedValues.inputValue[index] || 0,
            desc: desc,
          })
        )

        // submittedValues.customData.bVents = filterAndReplaceDoubleQuotes(submittedValues.customData.bVents)
        // submittedValues.customData.stemVents = filterAndReplaceDoubleQuotes(submittedValues.customData.stemVents)
        // submittedValues.customData.sunTunnels = filterAndReplaceDoubleQuotes(
        //   submittedValues.customData.sunTunnels
        // )
        // submittedValues.customData.pipes = filterAndReplaceDoubleQuotes(submittedValues.customData.pipes)

        // submittedValues.projectInputs = inputObject
        submittedValues.customData.otherDetails = inputObject
        if (selecteVenting === 'Ridge Vent') {
          submittedValues.customData.canVentsExist = ''
        } else if (selecteVenting === 'Cans') {
          submittedValues.customData.ridgeVentExist = ''
        }

        submittedValues.oppId = oppId
        submittedValues.clientId = clientId
        submittedValues.createdBy = memberId
        submittedValues.newProject = true
        submittedValues.projectType = selectedType

        const {
          inputValue,
          existingVenting,
          skylightsCount,
          projectInputs,
          chimneysCount,
          sunTunnelsCount,
          bVentCount,
          pipesCount,
          stemVentsCount,
          ...restObject
        } = submittedValues

        const formatedRoofArea = loadReroofAreaArray(submittedValues.customData.roofAreas)
        restObject.customData.roofAreas = formatedRoofArea
        if (restObject.customData.skylights.length) {
          const updatedSkylights = restObject.customData.skylights.map((skylight) => {
            if (skylight.skylightType === 'Curb Mount 2x2 (curb: 25.5" x 25.5")') {
              return {
                ...skylight,
                width: 25.5,
                length: 25.5,
              }
            } else if (skylight.skylightType === 'Curb Mount 2x4 (curb: 25.5" x 49.5")') {
              return {
                ...skylight,
                width: 25.5,
                length: 49.5,
              }
            }
            return skylight
          })
          restObject.customData.skylights = updatedSkylights
        }

        if (restObject.customData.roofAreas.length) {
          const updatedInNumbers = restObject.customData.roofAreas.map((items) => ({
            ...items,
            layers: items.layers === 'No Tear Off' ? null : Number(items.layers),
          }))
          restObject.customData.roofAreas = updatedInNumbers
        }

        updateArrays(restObject, 'pipes', false, Number(pipesCount))

        updateArrays(restObject, 'bVents', true, Number(bVentCount))

        updateArrays(restObject, 'stemVents', true, Number(stemVentsCount))

        updateArrays(restObject, 'sunTunnels', true, Number(sunTunnelsCount))

        if (existingVenting.option === 'Ridge Vent') {
          restObject.customData.canVentsExist = 0
        } else if (existingVenting.option === 'Cans') {
          restObject.customData.ridgeVentExist = 0
        } else if (existingVenting.option === 'No venting') {
          restObject.customData.canVentsExist = 0
          restObject.customData.ridgeVentExist = 0
        }
        // const finalObject = restObject
        // console.log('finalObject', finalObject)

        const selectedTypeObject = projectTyperes?.find((v) => v._id === selectedType)
        const finalObject = projectMethod(restObject, selectedTypeObject, allInputData, inputData)
        console.log({ finalObject, restObject })

        const res = await createProject(finalObject)
        if (isSuccess(res)) {
          notify('Project created successfully!', 'success')
          setBtnLoading(false)
          navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/contract/${res?.data?.data?.id}`)
          onSuccess?.()
        } else {
          notify(res?.data?.message, 'error')
          setBtnLoading(false)
        }
      } catch (err) {
        console.log('Submit error', err)
        setBtnLoading(false)
      }
    } else {
      try {
        const inputObject = inputData.map(
          ({ name, _id, unit, desc }: { name: string; _id: string; unit: string; desc: string }, index) => ({
            name: name,
            _id: _id,
            unit: unit,
            value: submittedValues.inputValue[index] || 0,
            desc: desc,
          })
        )
        submittedValues.projectInputs = inputObject
        submittedValues.oppId = oppId
        submittedValues.clientId = clientId
        submittedValues.createdBy = memberId
        submittedValues.newProject = true
        submittedValues.projectType = selectedType
        const {
          inputValue,
          existingVenting,
          customData,
          skylightsCount,
          stemVentsCount,
          sunTunnelsCount,
          pipesCount,
          chimneysCount,
          bVentCount,
          ...restObject
        } = submittedValues

        const finalObject = restObject

        const res = await createProject(finalObject)
        if (isSuccess(res)) {
          notify('Project created successfully!', 'success')
          setBtnLoading(false)
          navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/contract/${res?.data?.data?.id}`)
        } else {
          notify(res?.data?.message, 'error')
          setBtnLoading(false)
        }
      } catch (error) {
        console.log(error, 'Submit error')
        setBtnLoading(false)
      }
    }
  }

  return (
    <>
      <Styled.NewProjectCont>
        <SharedStyled.Content borderRadius="0" width="100%" disableBoxShadow={true} noPadding={true}>
          <SharedStyled.ContentHeader style={{ fontSize: '1.75rem' }} textAlign="left">
            New Project {oppData ? ` for PO${oppData.PO}` : ''}
          </SharedStyled.ContentHeader>
          <SharedStyled.FlexBox flexDirection="column" gap="10px" width="100%">
            <Formik
              initialValues={initialValues}
              onSubmit={handleSubmit}
              validateOnChange={true}
              validateOnBlur={false}
              enableReinitialize={true}
              validationSchema={selectSchema ? ProjectSchema : ProjectSchemaRepair}
            >
              {({ touched, errors, resetForm, values, setFieldValue }) => {
                const scrollToFirstError1 = () => {
                  const errorFields = Object.keys(errors) // Get fields with errors
                  const firstErrorOrEmptyField = errorFields.find((field: any) => errors[field])
                  if (firstErrorOrEmptyField) {
                    const element = document.querySelector(`[name="${firstErrorOrEmptyField}"]`)
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                    }
                  }
                }

                const scrollToFirstError = () => {
                  const allErrors = errors || {} // Get all errors in the form

                  const errorFields = Object.keys(allErrors) // Get all fields with errors

                  // Check for errors within customData
                  const userInputDataErrors = allErrors.customData || {}
                  const userInputDataFields = Object.keys(userInputDataErrors)

                  for (let i = 0; i < userInputDataFields.length; i++) {
                    const field = `customData.${userInputDataFields[i]}`
                    const element = document.querySelector(`[name="${field}"]`)

                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                      return // Exit after scrolling to the first missing field within customData
                    }
                  }

                  // Check for errors outside customData
                  for (let i = 0; i < errorFields.length; i++) {
                    const field = errorFields[i]
                    if (!field.startsWith('customData.')) {
                      const element = document.querySelector(`[name="${field}"]`)

                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                        return // Exit after scrolling to the first missing field outside customData
                      }
                    }
                  }
                }
                return (
                  <Form className="form">
                    <InputWithValidation
                      labelName="Project Name"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                    />
                    <Styled.TextArea
                      name="notes"
                      placeholder="Project  Notes"
                      component="textarea"
                      as={Field}
                      marginTop="8px"
                      height="52px"
                    />
                    <SharedStyled.FlexBox flexDirection="column" overflow="hidden" gap="10px" className="project-type">
                      <SharedStyled.Text fontSize="14px">Project Types</SharedStyled.Text>
                      {loading ? (
                        <SharedStyled.RadioSkeleton></SharedStyled.RadioSkeleton>
                      ) : (
                        <RadioButtonGroup onChange={(e) => onTypeChange(e)} radioButtons={projectTypesDrop} />
                      )}
                    </SharedStyled.FlexBox>

                    <SharedStyled.FlexCol>
                      {currentProjectId !== '' && replacementId !== '' && currentProjectId === replacementId ? (
                        <SharedStyled.FlexBox width="100%" flexDirection="column">
                          {inputData?.length ? (
                            <SharedStyled.ContentHeader textAlign="left">Project Details</SharedStyled.ContentHeader>
                          ) : null}

                          <FieldArray name="customData.roofAreas">
                            {({ remove, push }) => (
                              <>
                                {values.customData.roofAreas.length > 0 &&
                                  values.customData.roofAreas.map((data: any, index: number) => (
                                    <Styled.SaperatorDiv>
                                      <ProjectDetails
                                        setSelectedLayer={setSelectedLayer}
                                        setFieldValue={setFieldValue}
                                        selectedLayer={selectedLayer}
                                        index={index}
                                        action={action}
                                        values={values}
                                        data={data}
                                        errors={errors}
                                        touched={touched}
                                      />
                                    </Styled.SaperatorDiv>
                                  ))}
                                <SharedStyled.ButtonContainer justifyContent="start" margin="24px 0 16px 0">
                                  <Button
                                    type="button"
                                    className="fit outline"
                                    onClick={() =>
                                      push({
                                        name: '',
                                        pitch: '',
                                        size: '',
                                        layers: '',
                                        ventArea: '',
                                        rmvPly: '',
                                        instPly: '',
                                        noAccess: '',
                                        twoStory: '',
                                      })
                                    }
                                  >
                                    Add roof area
                                  </Button>
                                  &emsp;
                                  {values.customData.roofAreas.length > 1 && (
                                    <Button
                                      className="fit delete"
                                      type="button"
                                      onClick={() => {
                                        values.customData.roofAreas.length > 1
                                          ? remove(values.customData.roofAreas.length - 1)
                                          : null
                                      }}
                                    >
                                      Remove roof area
                                    </Button>
                                  )}
                                </SharedStyled.ButtonContainer>
                              </>
                            )}
                          </FieldArray>

                          <Styled.SaperatorDivThree className="select">
                            <RoofMeasurements
                              values={values}
                              setSelecteVenting={setSelecteVenting}
                              setFieldValue={setFieldValue}
                              inputData={inputData}
                              errors={errors}
                              touched={touched}
                            />
                            <div>
                              <Pipes setFieldValue={setFieldValue} values={values} errors={errors} touched={touched} />
                              <Vents setFieldValue={setFieldValue} values={values} errors={errors} touched={touched} />
                              {/* <SharedStyled.FlexCol>
                                <SharedStyled.Text>Pipes</SharedStyled.Text>
                                <SharedStyled.FlexRow
                                  margin="13px 0 0 0"
                                  className="pipe-row"
                                  alignItems="start"
                                  flexDirection="column"
                                >
                                  <div style={{ width: '100%' }}>
                                    <SharedStyled.NameValueUnitContainer margin="-1px 0 0 0">
                                      <InputLabelWithValidation
                                        stateName={`customData.pipeFlash123`}
                                        labelName='1"-3" pipes'
                                        inputType="number"
                                        error={
                                          errors?.customData?.pipeFlash123 && touched?.customData?.pipeFlash123
                                            ? true
                                            : false
                                        }
                                        labelUnit={'EA'}
                                        onWheel={handleWheel}
                                      />
                                    </SharedStyled.NameValueUnitContainer>
                                  </div>
                                  <div style={{ width: '100%' }}>
                                    {' '}
                                    <SharedStyled.NameValueUnitContainer margin="-1px 0 0 0">
                                      <InputLabelWithValidation
                                        stateName={`customData.pipeFlash4`}
                                        labelName='4" pipes'
                                        inputType="number"
                                        error={
                                          errors?.customData?.pipeFlash4 && touched?.customData?.pipeFlash4
                                            ? true
                                            : false
                                        }
                                        labelUnit={'EA'}
                                        onWheel={handleWheel}
                                      />
                                    </SharedStyled.NameValueUnitContainer>
                                  </div>
                                  <div style={{ width: '100%' }}>
                                    <SharedStyled.NameValueUnitContainer margin="-1px 0 0 0">
                                      <InputLabelWithValidation
                                        stateName={`customData.splitPipe2`}
                                        labelName='2" split boots'
                                        inputType="number"
                                        error={
                                          errors?.customData?.splitPipe2 && touched?.customData?.splitPipe2
                                            ? true
                                            : false
                                        }
                                        labelUnit={'EA'}
                                        onWheel={handleWheel}
                                      />
                                    </SharedStyled.NameValueUnitContainer>
                                  </div>
                                </SharedStyled.FlexRow>
                                <Vents
                                  setFieldValue={setFieldValue}
                                  values={values}
                                  errors={errors}
                                  touched={touched}
                                />
                              </SharedStyled.FlexCol> */}

                              <WLDropdown
                                setFieldValue={setFieldValue}
                                values={values}
                                handleNumberChangeForChimney={handleNumberChangeForChimney}
                                handleNumberChangeForSkylights={handleNumberChangeForSkylights}
                                selecteChimneys={selecteChimneys}
                                selecteSkylights={selecteSkylights}
                                stemSunTunnel={stemSunTunnel}
                                additionalDropdownsSunTunnel={additionalDropdownsSunTunnel}
                                handleNumberChangeForSunTunnels={handleNumberChangeForSunTunnels}
                                errors={errors}
                                touched={touched}
                              />
                            </div>
                            <div>
                              <SharedStyled.FlexCol gap="8px">
                                <SharedStyled.Text>Other Details</SharedStyled.Text>
                                {inputType ? (
                                  <></>
                                ) : (
                                  <SharedStyled.FlexRow margin="4px 0 0 0" className="other-details">
                                    {inputData.map(
                                      (
                                        { name, unit, desc }: { name: string; unit: string; desc: string },
                                        index: number
                                      ) => (
                                        <div key={index}>
                                          <InputLabelWithValidation
                                            stateName={`inputValue[${index}]`}
                                            inputType="number"
                                            labelName={name}
                                            labelUnit={getUnitSymbolFromId(unit, units)?.split(' ')[0]}
                                            info={true}
                                            desc={desc}
                                            onWheel={handleWheel}
                                          />
                                        </div>
                                      )
                                    )}
                                  </SharedStyled.FlexRow>
                                )}
                              </SharedStyled.FlexCol>
                            </div>
                          </Styled.SaperatorDivThree>

                          {inputData?.length ? (
                            <SharedStyled.FlexRow margin="34px 0 0 0">
                              <Button
                                width="max-content"
                                type="submit"
                                onClick={() => {
                                  scrollToFirstError()
                                  scrollToFirstError1()
                                }}
                                isLoading={btnLoading}
                              >
                                Save & Calculate
                              </Button>
                            </SharedStyled.FlexRow>
                          ) : (
                            <p>No inputs found</p>
                          )}
                        </SharedStyled.FlexBox>
                      ) : currentProjectId === '' ? (
                        <></>
                      ) : inputType ? (
                        <></>
                      ) : (
                        <SharedStyled.FlexBox width="100%" flexDirection="column" className="roof-repair">
                          {inputData?.length ? (
                            <SharedStyled.ContentHeader textAlign="left">Project Details</SharedStyled.ContentHeader>
                          ) : null}
                          <SharedStyled.FlexCol gap="8px">
                            {inputData.map(
                              ({ name, unit, desc }: { name: string; unit: string; desc: string }, index: number) => (
                                <SharedStyled.SingleFieldNameContainer
                                  alignItem="self-start"
                                  marginTop="10px"
                                  key={index}
                                  flexDirection="column"
                                >
                                  <InputLabelWithValidation
                                    stateName={`inputValue[${index}]`}
                                    labelName={name}
                                    inputType="number"
                                    labelUnit={getUnitSymbolFromId(unit, units)?.split(' ')[0]}
                                    info={true}
                                    onWheel={handleWheel}
                                    desc={desc}
                                  />
                                </SharedStyled.SingleFieldNameContainer>
                              )
                            )}
                          </SharedStyled.FlexCol>
                          {inputData?.length ? (
                            <Button
                              type="submit"
                              isLoading={btnLoading}
                              onClick={() => scrollToFirstError1()}
                              className="fit"
                            >
                              Save & Calculate
                            </Button>
                          ) : (
                            <p>No inputs found</p>
                          )}
                        </SharedStyled.FlexBox>
                      )}
                    </SharedStyled.FlexCol>
                  </Form>
                )
              }}
            </Formik>
          </SharedStyled.FlexBox>
        </SharedStyled.Content>
      </Styled.NewProjectCont>
    </>
  )
}

export default NewProject
