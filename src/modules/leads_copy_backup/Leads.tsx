import { <PERSON>, useNavigate, useParams } from 'react-router-dom'
// @ts-ignore
import { useEffect, useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import {
  extractPermissionByName,
  getDataFromLocalStorage,
  getKeysFromObjects,
  getPercentCompleteFromId,
  getSalesPersonIdFromName,
  isSuccess,
  nextAction,
  notify,
} from '../../shared/helpers/util'
import { getLeads, getLeadsCompletion, getPositionMembersById, getStages, searchOpps } from '../../logic/apis/sales'
import useDebounce from '../../shared/hooks/useDebounce'
import { default as HorizontalScrollableDiv } from '../../shared/scrollableDiv/ScrollableDiv'

import { useClickOutside } from '../../shared/hooks/useClickOutside'
import { ButtonCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { getPosition } from '../../logic/apis/position'
import { I_Opportunity } from '../opportunity/Opportunity'
import { AnyKey } from '../opportunity/components/assessmentForm/AssessmentForm'
import AddNewLead, { I_Position } from './AddNewLead'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { useAppDispatch } from '../../logic/redux/reduxHook'
import { setFilterSaleBy } from '../../logic/redux/actions/ui'
import { getReferres, getSalesPersonAndPM } from '../../logic/apis/company'
import ReferrerModal from '../Refferer/components/referrerModal/ReferrerModal'
import { colors } from '../../styles/theme'
import { RenderData, SLoader } from '../../shared/components/loader/Loader'
import { StageGroupEnum, StorageKey } from '../../shared/helpers/constants'
import { getDepartments } from '../../logic/apis/department'

function getSortTime(column: any, item: any): number {
  return column?.sortField
    ? new Date(item?.checkpointActivity?.[column?.sortField]?.created)?.getTime()
    : new Date(item?.newLeadDate)?.getTime()
}

export const initialBoard = {
  columns: [
    {
      id: 1,
      title: 'Backlog',
      cards: [],
    },
    {
      id: 2,
      title: 'Doing',
      cards: [],
    },
    {
      id: 3,
      title: 'Q&A',
      cards: [],
    },
    {
      id: 4,
      title: 'Production',
      cards: [],
    },
    {
      id: 5,
      title: 'Production2',
      cards: [],
    },
  ],
}

const Sales = () => {
  const navigate = useNavigate()
  // const [shimmerLoading, setShimmerLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [searchData, setSearchData] = useState([])
  const debouncedSearch = useDebounce(searchTerm, 500)
  const [referrerDropdownData, setReferrerDropdownData] = useState<any>([])
  const [referrerModal, setShowReferrerModal] = useState(false)
  const [refererres, setRefererres] = useState<any>([])
  const [referrerValue, setReferrerValue] = useState<any>([])
  const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [clientName, setClientName] = useState<any>('')
  const [createdClient, setCreatedClient] = useState({})

  const [boardValue, setBoardValue] = useState(initialBoard)

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, position, positionDetails, positionPermissions } = globalSelector.company
  const {
    filterSaleBy: { name: client, _id },
  } = globalSelector.ui
  // const [client, setClient] = useState('')
  const [opps, setOpps] = useState<I_Opportunity[]>([])
  const [leads, setLeads] = useState<any[]>([])
  const [stage, setStages] = useState<I_Opportunity[]>([])
  const [percentComplete, setPercentComplete] = useState<any>([])
  const [officeDrop, setOfficeDrop] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  const [showDropdown, setShowDropdown] = useState<boolean>(false)
  const dropdownRef = useRef(null)
  const dispatch = useAppDispatch()
  useClickOutside(dropdownRef, setShowDropdown)

  const [filteredLeads, setFilteredLeads] = useState<{ columns: any[] }>({ columns: [] })

  useEffect(() => {
    if (_id && !loading && _id !== 'none') {
      console.log({ client, _id }, boardValue.columns)

      const filter = boardValue.columns?.map((stage: any) => {
        return {
          ...stage,
          cards: stage?.cards?.filter((card: any) => {
            return card?.csrId === _id
          }),
        }
      })

      setFilteredLeads({ columns: filter })
    }
    if (client === 'None') {
      const availableSalesPerson = [...getKeysFromObjects(officeDrop, '_id')]

      const filter = boardValue.columns?.map((stage: any) => {
        return {
          ...stage,
          cards: stage?.cards?.filter((card: any) => {
            return !availableSalesPerson?.includes(card?.csrId)
          }),
        }
      })

      setFilteredLeads({ columns: filter })
    }
  }, [_id, loading])

  const StagesBoard = () => {
    const handleItemClick = (item: any) => {
      navigate(`/leads/newLead/${item._id}`, {
        state: {
          isDeleted: false,
        },
      })
    }

    const renderData = filteredLeads?.columns?.length ? filteredLeads : boardValue
    console.log({ renderData, filteredLeads }, officeDrop)
    const stageLoading = { columns: new Array(5).fill({ cards: new Array(10).fill('1') }) }

    return (
      <>
        {loading
          ? stageLoading.columns.map((column, idx) => {
              return (
                <Styled.StagesBoard key={idx} className="loading">
                  <h2>
                    <SLoader />
                  </h2>
                  <ul className="list-container">
                    {column.cards?.map((card: any, idx2: number) => (
                      <SLoader height={48} key={idx2} />
                    ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })
          : renderData.columns.map((column, idx) => {
              const isDescending = column?.sortOrder === -1
              return (
                <Styled.StagesBoard key={column.id}>
                  <SharedStyled.FlexBox margin="10px 0 0 10px" gap="5px" alignItems="center">
                    <SharedStyled.TooltipContainer
                      width="180px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="90px"
                      positionBottomDecs="unset"
                      // positionRightDecs="20px"
                      positionTopDecs="20px"
                      fontSize="14px"
                    >
                      <span className="tooltip-content">{column?.description ?? ''}</span>
                      <SharedStyled.Text fontSize="14px" fontWeight="600">
                        {column.title}
                      </SharedStyled.Text>
                    </SharedStyled.TooltipContainer>
                  </SharedStyled.FlexBox>

                  <ul className="list-container">
                    {column.cards
                      ?.sort((a: any, b: any) => {
                        const dateA = new Date(a?.createdAt)?.getTime()
                        const dateB = new Date(b?.createdAt)?.getTime()
                        return dateB - dateA
                      })
                      ?.sort((a: any, b: any) => {
                        const timeA = getSortTime(column, a)
                        const timeB = getSortTime(column, b)
                        return isDescending ? timeB - timeA : timeA - timeB
                      })
                      ?.map((card: any, idx2: number) => (
                        <Styled.ListItem
                          key={idx2}
                          percent={getPercentCompleteFromId(card._id, percentComplete)}
                          className="list-item"
                          onClick={() => handleItemClick(card)}
                          borderColor={nextAction(card)}
                        >
                          <h3>
                            {card?.firstName} {card?.lastName}
                          </h3>

                          {card?.agingVal ? (
                            <SharedStyled.FlexCol alignItems="flex-end" className="aging-value">
                              <span>{card?.agingVal}d</span>
                            </SharedStyled.FlexCol>
                          ) : null}
                        </Styled.ListItem>
                      ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })}
      </>
    )
  }
  useEffect(() => {
    initFetchReferrers()
  }, [])

  useEffect(() => {
    if (referrerValue === '--Add New--') {
      setShowReferrerModal(true)
    }
  }, [referrerValue])

  useEffect(() => {
    const filterWithNameAndSymbol = refererres?.map((item: any) => item.name)
    filterWithNameAndSymbol.push('--Add New--')
    setReferrerDropdownData(filterWithNameAndSymbol)
  }, [refererres])

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, true)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    if (searchTerm !== '') {
      setShowDropdown(true)
    } else {
      setShowDropdown(false)
      setSearchData([])
    }
  }, [searchTerm])

  // useEffect(() => {
  //   ;(async () => {
  //     if (debouncedSearch && leads?.length) {
  //       try {
  //         const res = await searchOpps({
  //           companyId: currentCompany?._id,
  //           search: debouncedSearch,
  //           csrId: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : _id,
  //           projectManager: positionDetails.symbol === 'ProjectManager' ? positionDetails.memberId : '',
  //         })

  //         setSearchData(res?.data?.data?.leads)
  //       } catch (error) {
  //         console.error('Search Error', error)
  //       } finally {
  //       }
  //     }
  //   })()
  // }, [debouncedSearch, leads?.length])

  // useEffect(() => {
  //   initFetchLead()
  // }, [])
  // const initFetchLead = async () => {
  //   try {
  //     const res = await getLeads({
  //       deleted: false,
  //       status: 'active',
  //       lost: false,
  //     })
  //     if (isSuccess(res)) {
  //       const { leads } = res?.data?.data
  //       setLeads(leads)
  //     }
  //   } catch (error) {
  //     console.log({ error })
  //   }
  // }

  console.log({ leads })
  const initFetch = async () => {
    setLoading(true)
    try {
      const [stagesRes, resLeads, resCompleted] = await Promise.all([
        getStages({}, false, StageGroupEnum?.Leads),
        getLeads({
          deleted: false,
          status: 'active',
          lost: false,
        }),
        getLeadsCompletion({
          deleted: false,
          // csrId: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : '',
          // projectManager: positionDetails.symbol === 'ProjectManager' ? positionDetails.memberId : '',
          stageGroup: StageGroupEnum.Leads,
          lost: false,
          status: 'active',
        }),
      ])

      if (isSuccess(resLeads) && isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const { completed: completePercent } = resCompleted?.data?.data
        let newBoard = new Array(stages?.length - 1)
        setStages(stages)
        setPercentComplete(completePercent)
        // handle opportunity response
        const { leads } = resLeads?.data?.data
        let stageNames: AnyKey = {}
        stages.forEach((stage: any) => {
          let item = {
            title: stage.name,
            cards: [],
            id: stage.sequence, // TODO: change to id
            description: stage.description,
            sortField: stage?.sortingField ? Object.keys(stage?.sortingField)[0] : undefined,
            sortOrder: stage?.sortingField ? Object.values(stage?.sortingField)[0] : undefined,
          }
          newBoard[stage.sequence - 1] = item
          stageNames[stage._id] = stage
        })
        leads.forEach((lead: any, idx: number) => {
          stages.forEach((stage: any) => {
            if (stage._id === lead.stage) {
              const cards = newBoard[stage.sequence - 1].cards
              newBoard[stage.sequence - 1].cards = [
                ...cards,
                {
                  ...lead,
                  id: cards?.length + 1,
                },
              ]
            }
          })
        })
        console.log({ newBoard }, leads)
        setLeads(leads)
        setBoardValue({
          columns: newBoard,
        })
        setLoading(false)
      } else {
        // if (!isSuccess(stagesRes)) throw new Error(stagesRes?.data.message)
        if (!isSuccess(resLeads)) throw new Error(resLeads?.data.message)
      }
    } catch (error: any) {
      console.error('getAllStages error', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (Object.entries(positionDetails)?.length > 0) {
      getPositions()
      initFetch()
    }
  }, [positionDetails])

  const getPositionMembers = async (departmentId: string) => {
    try {
      // const response = await getPositionMembersById({ departmentId }, false) // NHR-1567

      const response = await getSalesPersonAndPM(departmentId) // NHR-1567
      if (isSuccess(response)) {
        // setOfficeDrop(response?.data?.data?.memberData) // NHR-1567
        setOfficeDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getPositions = async () => {
    try {
      const response = await getDepartments({ deleted: false }, false)
      if (isSuccess(response)) {
        console.log({ response })
        const departments: I_Position[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })
        getPositionMembers(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    } finally {
      // setShimmerLoading(false)
    }
  }

  const hasCreateLeadAccess =
    Object.keys(positionPermissions)?.length && extractPermissionByName(positionDetails, 'leads')?.crud?.write

  return (
    <>
      <Styled.SalesContainer>
        <SharedStyled.FlexRow justifyContent="space-between">
          <SharedStyled.SectionTitle className="opportunity">Leads</SharedStyled.SectionTitle>
        </SharedStyled.FlexRow>

        <SharedStyled.FlexRow margin="10px 0 0 0" justifyContent="space-between" className="filter">
          <SharedStyled.FlexRow className="filterCont">
            {hasCreateLeadAccess ? (
              <RenderData loader={<SLoader width={104} height={40} />} loading={loading}>
                <ButtonCont>
                  <Button
                    onClick={() => {
                      // handleNewLeadClick()
                      setShowAddModal(true)
                    }}
                  >
                    New Lead
                  </Button>
                </ButtonCont>
              </RenderData>
            ) : null}

            <RenderData loader={<SLoader width={104} height={40} />} loading={loading}>
              <CustomSelect
                value={client}
                dropDownData={['Show All', ...getKeysFromObjects(officeDrop, 'name'), 'None']}
                stateName="client"
                setValue={(val: string) => {
                  const id = val === 'None' ? 'none' : getSalesPersonIdFromName(val, officeDrop)
                  if (val === 'Show All') {
                    setFilteredLeads({ columns: [] })
                  }
                  dispatch(setFilterSaleBy({ _id: id, name: val }))
                }}
                innerHeight="40px"
                className="sales"
              />
            </RenderData>
          </SharedStyled.FlexRow>
        </SharedStyled.FlexRow>

        <Styled.BoardContainer marginTop="10px">
          <HorizontalScrollableDiv>
            <StagesBoard />
          </HorizontalScrollableDiv>
        </Styled.BoardContainer>
        <SharedStyled.FlexBox width="100%" gap="5px" wrap="wrap" justifyContent="flex-end">
          <Button
            padding="4px"
            type="button"
            maxWidth="200px"
            className="gray"
            height="35px"
            onClick={() => {
              navigate(`/leads/deleted`)
            }}
          >
            Deleted Leads
          </Button>
          <Button
            padding="4px"
            type="button"
            maxWidth="200px"
            bgColor={colors.yellow}
            height="35px"
            onClick={() => {
              navigate(`/leads/newLead/inactive`)
            }}
          >
            {/* <Link to={`/sales/opportunities/inactive`} style={{ color: 'black' }}> */}
            Inactive Leads
            {/* </Link> */}
          </Button>
          <Button
            padding="4px"
            type="button"
            maxWidth="200px"
            bgColor={colors.errorRed}
            height="35px"
            onClick={() => {
              navigate(`/leads/newLead/lost`)
            }}
          >
            {/* <Link to={`/sales/opportunities/lost`} style={{ color: 'white' }}> */}
            Lost Leads
            {/* </Link> */}
          </Button>
        </SharedStyled.FlexBox>

        <CustomModal show={showAddModal}>
          <AddNewLead
            onClose={() => {
              setShowAddModal(false)
            }}
            onComplete={() => {
              setShowAddModal(false)
              initFetch()
              getPositions()
            }}
            referrerDropdownData={referrerDropdownData}
            setReferrerValue={setReferrerValue}
            refererres={refererres}
            setShowAddNewClientModal={setShowAddNewClientModal}
            detailsUpdate={detailsUpdate}
            addNewClientModal={addNewClientModal}
            setClientName={setClientName}
            createdClient={createdClient}
            officeDrop={officeDrop}
          />
        </CustomModal>

        <CustomModal show={referrerModal}>
          <ReferrerModal
            onClose={() => {
              setShowReferrerModal(false)
              // setEditReferrerVals(null)
            }}
            onComplete={() => {
              initFetchReferrers()
            }}
          />
        </CustomModal>
      </Styled.SalesContainer>
    </>
  )
}

export default Sales
