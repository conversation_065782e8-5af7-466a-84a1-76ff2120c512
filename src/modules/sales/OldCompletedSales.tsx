import { useCallback, useEffect, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { Formik } from 'formik'

import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { AddressWrap } from '../client/style'
import { OldCompletedCont } from '../operations/style'
import { getOldOpportunities } from '../../logic/apis/sales'
import { dayjsFormat, getDataFromLocalStorage, getIdFromName, isSuccess, notify } from '../../shared/helpers/util'
import { getProjectTypes } from '../../logic/apis/projects'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { StorageKey } from '../../shared/helpers/constants'

const OldCompletedSales = () => {
  const navigate = useNavigate()

  const [oldSales, setOldSales] = useState<any>([])
  const [loading, setLoading] = useState(true)
  const [type, setType] = useState('')
  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  const globalSelector = useSelector((state: any) => state)
  const loadMoreRef = useRef(null)
  const fetchIdRef = useRef(0)
  const [initDropDown, _setInitTodoData] = useState<any>({
    type: '',
  })
  const { currentCompany, positionDetails } = globalSelector.company

  const completedColumns = [
    {
      Header: 'Client Name',
      accessor: (row: any) => `${row?.contactId?.fullName}`,
    },
    {
      Header: 'PO#',
      accessor: (row: any) => `${row?.PO}-${row?.num}`,
    },
    {
      Header: 'Type',
      accessor: (row: any) => `${row?.oppType?.name}`,
    },
    {
      Header: 'Volume',
      accessor: (row: any) => `$${row?.soldValue?.toFixed(2)}`,
    },
    {
      Header: 'Material Color',
      accessor: (row: any) => {
        return row?.orderId?.projects?.[0]?.colors?.['44bb8a4e-7562-456f-8a05-40ab7da8e1e6@Shingle Color'] ?? '--'
      },
    },
    {
      Header: 'Address',
      accessor: (row: any) => {
        return (
          <AddressWrap>
            {row?.street ? <p>{row?.street},</p> : null}

            <p>
              {row?.city ? `${row?.city},` : null} {row?.state} {row?.zip}
            </p>
          </AddressWrap>
        )
      },
    },
    {
      Header: 'Completed',
      accessor: (row: any) => `${dayjsFormat(row?.jobCompletedDate, 'M-D-YY')}`,
    },
    {
      Header: 'Days on Job',
      accessor: (row: any) => {
        const jobCompletedDate: any = new Date(row?.jobCompletedDate)
        const jobStartedDate: any = new Date(row?.jobStartedDate)

        // Calculate the time difference in milliseconds
        const timeDifferenceInMillis = jobCompletedDate - jobStartedDate

        // Convert to days
        const daysDifference = timeDifferenceInMillis / (1000 * 60 * 60 * 24)

        return Math.ceil(daysDifference) // Return the difference in days with two decimal places
      },
    },
  ]

  useEffect(() => {
    initFetch()
  }, [])

  useEffect(() => {
    fetchOldOpportunities()
  }, [type])

  const fetchOldOpportunities = async () => {
    try {
      const res = await getOldOpportunities({
        type: type ? type : undefined,
        salesPerson: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : undefined,
      })
      if (isSuccess(res)) {
        const { opps } = res?.data?.data
        setOldSales(opps)
        setLoading(false)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      setLoading(false)
      console.log(error)
    }
  }

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypesDrop(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        let currentCompanyData: any = getDataFromLocalStorage('currentCompany')
        const res = await getOldOpportunities({
          limit: pageSize,
          type: type ? type : undefined,
          salesPerson: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : undefined,
        })

        if (res?.data?.statusCode === 200) {
          const { opps } = res?.data?.data

          opps.forEach((res: any, index: number) => {
            receivedData.push({
              ...res,
            })
          })
        } else {
          notify(res?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize

          setOldSales(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          setLoading(false)
        }
        // }, 1000)
        // setLoading(false)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
        setLoading(false)
      }
    },
    [type]
  )
  return (
    <OldCompletedCont>
      <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap" margin="0 0 20px 0">
        <SharedStyled.SectionTitle>Completed Sales</SharedStyled.SectionTitle>

        <Formik
          initialValues={initDropDown}
          onSubmit={() => {}}
          validateOnChange={true}
          validateOnBlur={false}
          enableReinitialize={true}
        >
          {({ touched, errors, resetForm, values, setFieldValue }) => {
            useEffect(() => {
              setType(getIdFromName(values.type, projectTypesDrop))
            }, [values.type])
            return (
              <SharedStyled.FlexRow flexWrap="wrap" justifyContent="space-between" width="max-content">
                <div className="filter">
                  <CustomSelect
                    labelName="Filter by type"
                    stateName="type"
                    error={touched.type && errors.type ? true : false}
                    value={values.type}
                    dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name), 'All']}
                    setValue={() => {}}
                    setFieldValue={setFieldValue}
                    innerHeight="52px"
                    margin="10px 0 0 0"
                    maxWidth="100%"
                  />
                </div>
              </SharedStyled.FlexRow>
            )
          }}
        </Formik>
      </SharedStyled.FlexRow>

      <Table
        columns={completedColumns}
        data={oldSales}
        loading={loading}
        fetchData={fetchData}
        onRowClick={(vals) => navigate(`/sales/opportunity/${vals?._id}`)}
        noSearch
        minWidth=""
        noBorder
        ref={loadMoreRef}
        isLoadMoreLoading={loading}
      />
    </OldCompletedCont>
  )
}

export default OldCompletedSales
