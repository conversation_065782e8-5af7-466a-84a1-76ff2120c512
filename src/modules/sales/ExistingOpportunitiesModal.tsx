import React from 'react'
import { CustomModal } from '../../shared/customModal/CustomModal'
import Modal from '../../shared/customModal/Modal'
import Button from '../../shared/components/button/Button'
import * as SharedStyled from '../../styles/styled'
import styled from 'styled-components'
import { colors } from '../../styles/theme'

interface Opportunity {
  id: string
  poNum: string
  projectType: string
}

interface ExistingOpportunitiesModalProps {
  onClose: () => void
  onContinue: () => void
  name: string
  opportunities: Opportunity[]
}

const OpportunityList = styled.div`
  margin: 30px 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
`

const OpportunityItem = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
`

const ProjectNumber = styled.span`
  font-size: 20px;
  font-weight: 700;
  color: ${colors.darkGrey};
`

const ProjectType = styled.span`
  font-size: 18px;
  color: ${colors.grey};
`

const ExistingOpportunitiesModal: React.FC<ExistingOpportunitiesModalProps> = ({
  onClose,
  onContinue,
  name,
  opportunities,
}) => {
  return (
    <Modal title="Existing Opportunities" onClose={onClose}>
      <SharedStyled.FlexCol alignItems="center" gap="20px">
        <SharedStyled.Text fontSize="20px" textAlign="center" fontWeight="600">
          {name} already has {opportunities.length} open {opportunities.length === 1 ? 'opportunity' : 'opportunities'}:
        </SharedStyled.Text>

        <OpportunityList>
          {opportunities.map((opp) => (
            <OpportunityItem key={opp.id}>
              <ProjectNumber>{opp.poNum}</ProjectNumber>
              <ProjectType>{opp.projectType}</ProjectType>
            </OpportunityItem>
          ))}
        </OpportunityList>

        <SharedStyled.Text fontSize="24px" textAlign="center" fontWeight="600">
          Continue?
        </SharedStyled.Text>

        <SharedStyled.FlexRow gap="20px" width="100%" justifyContent="space-between">
          <Button onClick={onClose} className="gray" width="48%">
            Cancel
          </Button>
          <Button onClick={onContinue} width="48%">
            Continue
          </Button>
        </SharedStyled.FlexRow>
      </SharedStyled.FlexCol>
    </Modal>
  )
}

export default ExistingOpportunitiesModal
