import React, { useEffect, useState } from 'react'
import {
  AddressCont,
  AddressContainer,
  CrossContainer,
  GoogleSearchBox,
  ModalContainer,
  ModalHeaderContainer,
  NewLeadContainer,
  TextArea,
} from './style'
import * as SharedStyled from '../../../styles/styled'
import UnitSvg from '../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../assets/icons/CrossIcon'
import { Field, Form, Formik } from 'formik'
import { LoadScript } from '@react-google-maps/api'
import { getConfig } from '../../../config'
import { SLoader } from '../../../shared/components/loader/Loader'
import { SharedDate } from '../../../shared/date/SharedDate'
import Button from '../../../shared/components/button/Button'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import {
  formatPhoneNumber,
  getIdFromName,
  getKeysFromObjects,
  hasValues,
  isSuccess,
  notify,
  startOfDate,
} from '../../../shared/helpers/util'
import AutoComplete from '../../../shared/autoComplete/AutoComplete'
import { LabelDiv } from '../../sales/style'
import { I_Client } from '../../opportunity/Opportunity'
import { Nue, StageGroupEnum } from '../../../shared/helpers/constants'
import { useSelector } from 'react-redux'
import useDebounce from '../../../shared/hooks/useDebounce'
import { getPosition } from '../../../logic/apis/position'
import { I_Position } from '../../sales/AddOpportunityModal'
import { createWarranty, getStages } from '../../../logic/apis/sales'
import { getProjectTypes } from '../../../logic/apis/projects'
import * as Yup from 'yup'
import AutoCompleteAddress from '../../../shared/autoCompleteAdress/AutoCompleteAddress'
import { getDistanceAndDuration, getLatLongFromAddress } from '../../../shared/helpers/map'
import { getSalesPersonAndPM } from '../../../logic/apis/company'
import { getSearchedContact } from '../../../logic/apis/contact'

interface ILeadWarrantyModal {
  onClose: () => void
  onComplete: () => void
  setClientName: React.Dispatch<any>
  clientData: any
  setClientData: React.Dispatch<any>
  setShowEditClientModal?: React.Dispatch<React.SetStateAction<boolean>>
  noLoadScript?: boolean
  contactWarranty?: boolean
  clientInfo?: {
    fullName: string
    phone: string
    email: string
    firstName: string
    lastName: string
    _id: string
    street: string
    city: string
    isBusiness: boolean
    businessName: string
    state: string
    zip: string
    projectManager: string
  }
}

const AddLeadWarrantyModal = (props: ILeadWarrantyModal) => {
  const {
    onClose,
    onComplete,
    setClientName,
    clientData,
    setClientData,
    contactWarranty,
    setShowEditClientModal,
    noLoadScript,
    clientInfo,
  } = props
  const [clientDropdown, setClientDrop] = useState<I_Client[]>([])
  const [clientloader, setClientLoader] = useState<boolean>(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [projectManagerData, setProjectManagerData] = useState<any[]>([])
  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  const [projectTypes, setProjectTypes] = useState<any>([])
  const [defaultStage, setDefaultStage] = useState('') // will be stage id
  const [loading, setLoading] = useState(false)
  const [toggleGoogleAddressInput, setToggleGoogleAddressInput] = useState<boolean>(false)
  const [addressInputType, setAddressInputType] = useState<'custom' | 'google'>()
  const [addressLoading, setAddressLoading] = useState<boolean>(true)
  const [lat, setLat] = useState('')
  const [long, setLong] = useState('')
  const [distance, setDistance] = useState(0)
  const [duration, setDuration] = useState(0)
  const [clientAddress, setClientAddress] = useState('')
  const [clientLoading, setClientLoading] = useState(false)
  const [contactData, setContactData] = useState<any>({})

  const initialvalues = {
    newLeadDate: new Date().toISOString(),
    oppType: '',
    oppNotes: '',
    notes: '',
    client: contactWarranty ? clientInfo?.fullName : '',
    contactId: '',
    street: '',
    city: '',
    state: '',
    zip: '',
    distance: 0,
    duration: 0,
    oppLat: '',
    oppLong: '',
    projectManager: contactWarranty ? clientInfo?.projectManager : '',
    leadCost: '0',
    questions: [],
    naDate: '',
    naTime: '',
  }

  const globalSelector = useSelector((state: any) => state)
  const debouncedSearch = useDebounce(searchValue, 500)

  const { currentMember, companySettingForAll } = globalSelector.company

  useEffect(() => {
    getPositions()
    initFetch()
    getStagesData()
  }, [])

  useEffect(() => {
    if (!contactWarranty) getClientsData()
  }, [debouncedSearch])

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false, StageGroupEnum.Operations)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const stage = stages.find((v) => v.sequence === 1)
        console.log('defaultStage', stage)
        setDefaultStage(stage?._id || '')
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }
  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypes(projectType)
        setProjectTypesDrop(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const getClientsData = async () => {
    try {
      setClientLoader(true)
      let receivedData: any = []
      const response = await getSearchedContact(debouncedSearch, {
        fields: {
          fullName: 1,
          firstName: 1,
          lastName: 1,
          leadSourceId: 1,
          campaignId: 1,
          distance: 1,
          isBusiness: 1,
          businessName: 1,
          referredBy: 1,
          street: 1,
          city: 1,
          state: 1,
          zip: 1,
          phone: 1,
          email: 1,
          type: 1,
        },
        type: 'client',
      })

      if (isSuccess(response)) {
        let statusRes = response?.data?.data?.contacts
        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            ...res,
            name: `${res?.fullName?.trim()}`,
            status: '-',
            address: res?.street,
            phone: formatPhoneNumber(res?.phone, ''),
            isBusiness: res?.isBusiness,
            clientId: res._id,
          })
        })
        setClientDrop(receivedData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      notify('Failed to fetch clients!', 'error')
      console.log('Contact fetch failed', err)
    } finally {
      setAddressLoading(false)
      setClientLoader(false)
    }
  }

  const getPositions = async () => {
    try {
      const response = await getPosition({ deleted: false }, false)
      if (isSuccess(response)) {
        const positions: I_Position[] = response?.data?.data?.position
        let projectManagerIdx: string[] = []
        positions.forEach((position: any, idx) => {
          if (position.symbol === 'ProjectManager') {
            projectManagerIdx.push(position?._id)
            return
          }
        })
        getPositionMembers(projectManagerIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    } finally {
      // setShimmerLoading(false)
    }
  }

  const getPositionMembers = async (positionId: string) => {
    try {
      // const response = await getPositionMembersById({ positionId }, false) // NHR-1634

      const response = await getSalesPersonAndPM(undefined, true) // NHR-1634

      if (isSuccess(response)) {
        // setProjectManagerData(response?.data?.data?.memberData) // NHR-1634
        setProjectManagerData(response?.data?.data?.members) // NHR-1634
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const newLeadSchema = Yup.object().shape({
    newLeadDate: Yup.string().required('Required'),
    oppType: Yup.string().required('Required'),
    notes: Yup.string(),
    client: Yup.string().required('Required'),
    duration: Yup.number().when(['street', 'city', 'state', 'zip'], {
      is: (street, city, state, zip) => !street && !city && !state && !zip,
      then: Yup.number().notRequired(),
      otherwise: Yup.number().required('Please enter Duration').min(1, 'Please enter Duration'),
    }),
    projectManager: Yup.string().required('Required'),
  })

  const handleSubmit = async (values: typeof initialvalues) => {
    const type = getIdFromName(values.oppType, projectTypesDrop)
    // const contactData = clientDropdown?.find((client: any) => client?.name === values?.client)
    const formattedData = values?.questions?.length
      ? values?.questions
          ?.map((question) => {
            const stateName: any = question
              ?.replace(/\s+/g, '-') // Removes all spaces
              ?.replace(/\?/g, '') // Removes all question marks

            // Get the corresponding value from Formik's state
            const answer = values?.[stateName]?.trim() || ''

            // Format the question and answer as needed
            return answer ? `${question} **${answer}**\n` : `${question} \n`
          })
          ?.join('')
      : ''
    // Join all strings with a newline character
    values.oppNotes =
      (formattedData ? `${formattedData}\n` : '') +
      `Comments/Notes: ${values?.notes ? `**${values?.notes?.trim()}**` : ''}\n` +
      `${contactData?.notes ? `Contact Notes: **${contactData?.notes?.trim()}**` : ''}`
    // Do something with formattedData, like sending it to an API or displaying it
    const { notes, questions, naDate, naTime, newLeadDate, contactId, ...restObject }: any = values
    if (questions?.length) {
      // Extract question keys (stateNames) and remove them from restObject
      const questionKeys = questions.map((question) => {
        return question
          ?.replace(/\s+/g, '-') // Replace spaces with hyphens
          ?.replace(/\?/g, '') // Remove question marks
      })

      // Remove the question keys from restObject
      questionKeys.forEach((key) => {
        delete restObject?.[key]
      })
    }

    console.log({ restObject })
    setLoading(true)
    try {
      // const [city, state] = values.addrCityState.split(',')

      values.distance = values.distance ? Number(values.distance) : 0
      values.duration = values.duration ? Number(values.duration) : 0
      let projectManagerId = ''

      if (values?.street && values?.city && values?.zip && values?.state) {
        const latLong = await getLatLongFromAddress(
          `${values?.street || ''}, ${values?.city || ''}, ${values?.state || ''} ${values?.zip || ''}, USA`
        )
        values.oppLat = latLong?.lat || ''
        values.oppLong = latLong?.lng || ''
      }

      projectManagerData.forEach((sp) => {
        if (values?.projectManager === sp.name) {
          projectManagerId = sp._id
        }
      })
      {
        /* hardcoded code */
      }
      const response = await createWarranty({
        ...restObject,
        contactId: clientData?._id,
        comments: [],
        oppDate: startOfDate(new Date().toISOString()),
        newLeadDate: startOfDate(values.newLeadDate),
        leadSource: 'Warranty',
        leadSourceId: 'f2af9385-a84e-42d6-b8e2-70531f5466ae',
        companyAddress: companySettingForAll?.address,
        companyLang: companySettingForAll?.longitude,
        companyLat: companySettingForAll?.latitude,
        oppLat: `${values?.oppLat}` || undefined,
        oppLong: `${values?.oppLong}` || undefined,
        createdBy: currentMember._id,
        fullName: clientData?.fullName?.trim() || '',
        firstName: clientData?.isBusiness ? clientData?.businessName : clientData?.firstName?.trim() || '',
        lastName: clientData?.isBusiness ? undefined : clientData?.lastName?.trim() || '',
        oppType: type,
        projectManager: projectManagerId,
        stage: defaultStage,
        currentDate: new Date().toISOString(),
      })

      setLoading(false)
      if (isSuccess(response)) {
        console.log({ response }, response?.data?.data?.oppId)
        notify('Created new warranty!', 'success')
        onComplete()
      } else throw new Error(response?.data?.message)
    } catch (err: any) {
      setLoading(false)
      notify(err?.message ?? 'Something went wrong!', 'error')
      console.log('failed new lead submit', err)
    }
  }

  return (
    <>
      <ModalContainer>
        <ModalHeaderContainer>
          <SharedStyled.FlexRow>
            <img src={UnitSvg} alt="modal icon" />
            <SharedStyled.FlexCol>
              <SharedStyled.Text fontSize="20px" fontWeight="600">
                Add Warranty
              </SharedStyled.Text>
            </SharedStyled.FlexCol>
          </SharedStyled.FlexRow>
          <CrossContainer
            onClick={() => {
              onClose()
            }}
          >
            <CrossIcon />
          </CrossContainer>
        </ModalHeaderContainer>
        <SharedStyled.SettingModalContentContainer>
          <>
            <Formik
              initialValues={initialvalues}
              onSubmit={handleSubmit}
              validationSchema={newLeadSchema}
              validateOnChange={true}
              validateOnBlur={false}
              enableReinitialize={false}
            >
              {({ errors, touched, values, setFieldValue }) => {
                useEffect(() => {
                  if (contactWarranty) {
                    setClientData(clientInfo)
                  } else {
                    setClientName(values.client)
                    setSearchValue(values.client)
                    if (values.client) {
                      const clientObj = clientDropdown?.find(
                        (client: any) => client?.name?.trim() === values?.client?.trim()
                      )
                      setClientData(clientObj)
                    }
                  }
                }, [values.client])

                useEffect(() => {
                  if (duration > 0) {
                    setFieldValue('duration', duration)
                  }
                }, [duration])

                useEffect(() => {
                  if (Object?.entries?.(clientData || {})?.length) {
                    setFieldValue('street', clientData?.street)
                    setFieldValue('city', clientData?.city)
                    setFieldValue('state', clientData?.state)
                    setFieldValue('zip', clientData?.zip)
                    setClientAddress(
                      `${clientData?.street || ''}, ${clientData?.city || ''}, ${clientData?.state || ''} ${
                        clientData?.zip || ''
                      }, USA`
                    )
                  }
                }, [clientData])

                useEffect(() => {
                  if (clientAddress !== '') {
                    getDistanceAndDuration(companySettingForAll?.address, clientAddress)
                      .then(({ distance, duration }: any) => {
                        const distanceInfo = Math.ceil(Number(distance / 1609.34))
                        const durationInfo = Math.round(duration / 60) ?? 0

                        setFieldValue('distance', distanceInfo)
                        setFieldValue('duration', durationInfo)
                      })
                      .catch((error) => {
                        console.error(error)
                      })
                  } else {
                    setFieldValue('distance', 0)
                    setFieldValue('duration', 0)
                  }
                  // }
                }, [clientAddress])

                useEffect(() => {
                  if (values.oppType) {
                    const result = projectTypes?.find((v) => v.name === values.oppType)?.questions || []
                    setFieldValue('questions', result)
                  }
                }, [values.oppType, projectTypes])

                return (
                  // <LoadScript
                  //   googleMapsApiKey={getConfig().googleAddressApiKey}
                  //   //  @ts-ignore
                  //   libraries={['places']}
                  //   loadingElement={<SLoader height={35} width={100} isPercent />}
                  // >
                  <NewLeadContainer>
                    {/* <SharedStyled.ContentContainer> */}
                    <Form className="form">
                      <SharedStyled.Content
                        overflow={'unset'}
                        disableBoxShadow={true}
                        noPadding={true}
                        gap="8px"
                        width="100%"
                      >
                        <SharedDate
                          value={values.newLeadDate}
                          labelName="Date *"
                          stateName="newLeadDate"
                          setFieldValue={setFieldValue}
                          error={touched.newLeadDate && errors.newLeadDate ? true : false}
                        />

                        <SharedStyled.Text fontSize="16px" fontWeight="700" textAlign="left" margin="8px auto 0px 0">
                          Contact Info
                        </SharedStyled.Text>

                        <>
                          <AutoComplete
                            labelName="Select a contact *"
                            stateName="client"
                            error={touched.client && errors.client ? true : false}
                            setFieldValue={setFieldValue}
                            options={getKeysFromObjects(clientDropdown, 'name').map((client) => client.trim())}
                            value={values.client || ''}
                            setValueOnClick={() => {}}
                            className="clientHeight"
                            refererres={[]}
                            leadSrcData={[]}
                            setClientAddress={setClientAddress}
                            apiSearch={true}
                            searchLoader={clientloader}
                            disabled={contactWarranty}
                          />
                        </>
                        {clientLoading ? (
                          <SharedStyled.FlexCol gap="5px">
                            <SLoader height={15} width={100} isPercent />
                            <SLoader height={5} width={100} isPercent />
                            <SLoader height={5} width={100} isPercent />
                            <SLoader height={5} width={100} isPercent />
                          </SharedStyled.FlexCol>
                        ) : hasValues(clientData) ? (
                          <SharedStyled.FlexCol gap="10px">
                            <SharedStyled.FlexRow justifyContent="space-between">
                              <div>
                                <SharedStyled.Text fontSize="14px">
                                  {formatPhoneNumber(clientData.phone, '') || '--'}
                                </SharedStyled.Text>
                                <br /> <SharedStyled.Text fontSize="14px">{clientData.email || '--'}</SharedStyled.Text>
                              </div>
                              <div>
                                <SharedStyled.Text fontSize="14px">
                                  {clientData.street !== '' ? clientData.street : ''}
                                  <br />
                                  {/* <b>City : </b> */}
                                  {clientData.city},&nbsp;
                                  {/* <b>State : </b> */}
                                  {clientData.state},&nbsp;
                                  {/* <b>Zip : </b> */}
                                  {clientData.zip}
                                </SharedStyled.Text>
                              </div>
                              {!contactWarranty && (
                                <div>
                                  <Button type="button" onClick={() => setShowEditClientModal?.(true)}>
                                    Edit
                                  </Button>
                                </div>
                              )}
                            </SharedStyled.FlexRow>

                            {clientData?.contacts?.length > 0 ? (
                              <>
                                <SharedStyled.FlexCol>
                                  {clientData?.contacts?.map(
                                    (
                                      {
                                        firstName,
                                        lastName,
                                        phone,
                                        email,
                                      }: {
                                        firstName: string
                                        lastName: string
                                        phone: string
                                        email: string
                                      },
                                      index: number
                                    ) => (
                                      <SharedStyled.FlexRow
                                        width="95%"
                                        margin="0 0 0 auto"
                                        justifyContent="space-between"
                                      >
                                        <div>
                                          <SharedStyled.Text fontSize="12px">
                                            {index + 1}. {firstName || ''} {lastName || ''}
                                          </SharedStyled.Text>
                                        </div>
                                        <div>
                                          <SharedStyled.Text fontSize="12px">
                                            {formatPhoneNumber(phone, '')}
                                          </SharedStyled.Text>
                                        </div>
                                        <div>
                                          <SharedStyled.Text fontSize="12px">{email}</SharedStyled.Text>
                                        </div>
                                      </SharedStyled.FlexRow>
                                    )
                                  )}
                                </SharedStyled.FlexCol>
                              </>
                            ) : (
                              <></>
                            )}
                          </SharedStyled.FlexCol>
                        ) : (
                          <></>
                        )}

                        <AddressContainer>
                          {
                            <h2 className="sub-heading">
                              Address:{' '}
                              <SharedStyled.BlueEdit
                                onClick={() => {
                                  setToggleGoogleAddressInput(true)
                                  setAddressInputType('google')
                                }}
                              >
                                {toggleGoogleAddressInput ? '' : 'Edit'}
                              </SharedStyled.BlueEdit>
                            </h2>
                          }

                          {toggleGoogleAddressInput ? (
                            <GoogleSearchBox>
                              {addressInputType === 'custom' ? (
                                <AddressCont gap="12px" id="custom">
                                  <SharedStyled.FlexCol gap="4px">
                                    <SharedStyled.FlexRow className="input">
                                      <div title="Street" id="street">
                                        <InputWithValidation
                                          labelName="Street"
                                          stateName="street"
                                          error={touched.street && errors.street ? true : false}
                                          twoInput={true}
                                        />
                                      </div>
                                    </SharedStyled.FlexRow>

                                    <SharedStyled.FlexRow className="input" justifyContent="space-between">
                                      <div id="city">
                                        <InputWithValidation
                                          labelName="City"
                                          stateName="city"
                                          error={touched.city && errors.city ? true : false}
                                          twoInput={true}
                                        />
                                      </div>

                                      <div id="state">
                                        <CustomSelect
                                          dropDownData={companySettingForAll?.workingStates || []}
                                          setValue={() => {}}
                                          stateName="state"
                                          value={values.state}
                                          // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                                          setFieldValue={setFieldValue}
                                          labelName="State"
                                          innerHeight="52px"
                                          margin="10px 0 0 0"
                                        />
                                      </div>

                                      <div id="zip">
                                        <InputWithValidation
                                          labelName="Zip"
                                          stateName="zip"
                                          error={touched.zip && errors.zip ? true : false}
                                          twoInput={true}
                                        />
                                      </div>
                                    </SharedStyled.FlexRow>

                                    <SharedStyled.FlexRow
                                      style={{ width: '100%' }}
                                      margin="4px 0 0 0"
                                      flexWrap="wrap"
                                      justifyContent="space-between"
                                    >
                                      <SharedStyled.FlexRow width="max-content">
                                        <Button
                                          // className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                            setAddressInputType('google')
                                          }}
                                        >
                                          Save
                                        </Button>
                                        <Button
                                          className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                            setAddressInputType('google')
                                          }}
                                        >
                                          Cancel
                                        </Button>
                                      </SharedStyled.FlexRow>
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          setAddressInputType('google')
                                          setToggleGoogleAddressInput(true)
                                        }}
                                        className="gray"
                                        width="max-content"
                                      >
                                        Google
                                      </Button>
                                    </SharedStyled.FlexRow>
                                  </SharedStyled.FlexCol>
                                </AddressCont>
                              ) : (
                                <AddressCont gap="12px" className="google" id="google">
                                  <SharedStyled.FlexCol margin="10px 0 0 0" width="100%">
                                    <AutoCompleteAddress
                                      setFieldValue={setFieldValue}
                                      street={'street'}
                                      city={'city'}
                                      state={'state'}
                                      zip={'zip'}
                                      distance={'distance'}
                                      duration={'duration'}
                                      sourceAddress={companySettingForAll?.address}
                                      companyLatLong={companySettingForAll}
                                      setLat={setLat}
                                      setLong={setLong}
                                      setDistance={setDistance}
                                      setDuration={setDuration}
                                      noLoadScript={noLoadScript ? noLoadScript : true}
                                    />
                                    <SharedStyled.FlexRow
                                      style={{ width: '100%' }}
                                      margin="4px 0 0 0"
                                      flexWrap="wrap"
                                      justifyContent="space-between"
                                    >
                                      <SharedStyled.FlexRow width="max-content">
                                        <Button
                                          // className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                            setAddressInputType('google')
                                          }}
                                        >
                                          Save
                                        </Button>
                                        <Button
                                          className="delete"
                                          type="button"
                                          width="max-content"
                                          onClick={() => {
                                            setToggleGoogleAddressInput(false)
                                          }}
                                        >
                                          Cancel
                                        </Button>
                                      </SharedStyled.FlexRow>
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          setAddressInputType('custom')
                                          setToggleGoogleAddressInput(true)
                                        }}
                                        className="gray"
                                        width="max-content"
                                      >
                                        Custom
                                      </Button>
                                    </SharedStyled.FlexRow>
                                  </SharedStyled.FlexCol>
                                </AddressCont>
                              )}
                            </GoogleSearchBox>
                          ) : (
                            !addressLoading &&
                            (values.street !== '' ||
                              values.city !== '' ||
                              values.state !== '' ||
                              values.zip !== '') && (
                              <div style={{ width: '100%' }}>
                                <SharedStyled.Text fontWeight="400">
                                  <span style={{ fontFamily: Nue.regular }}>
                                    {values.street !== '' ? values.street : ''}
                                  </span>
                                </SharedStyled.Text>
                                <br />
                                <SharedStyled.Text fontWeight="400">
                                  <span style={{ fontFamily: Nue.regular }}>{values.city},&nbsp;</span>
                                  <span style={{ fontFamily: Nue.regular }}>{values.state},&nbsp;</span>
                                  <span style={{ fontFamily: Nue.regular }}>{values.zip}</span>
                                </SharedStyled.Text>
                                &emsp;
                              </div>
                            )
                          )}
                        </AddressContainer>

                        {values.street && values.city && values.state && values.zip && (
                          <SharedStyled.TwoInputDiv>
                            <InputWithValidation
                              labelName="Drive Time"
                              stateName="duration"
                              twoInput={true}
                              disabled={addressInputType !== 'custom'}
                              error={touched.duration && errors.duration ? true : false}
                            />
                            <InputWithValidation
                              labelName="Distance"
                              stateName="distance"
                              twoInput={true}
                              disabled={addressInputType !== 'custom'}
                              error={touched.distance && errors.distance ? true : false}
                            />
                          </SharedStyled.TwoInputDiv>
                        )}

                        <SharedStyled.Text fontSize="16px" fontWeight="700" textAlign="left" margin="8px auto -10px 0">
                          Project Info
                        </SharedStyled.Text>
                        <CustomSelect
                          labelName="Project Type *"
                          stateName="oppType"
                          error={touched.oppType && errors.oppType ? true : false}
                          setFieldValue={setFieldValue}
                          value={values.oppType}
                          dropDownData={projectTypesDrop.map(({ name }: { name: string }) => name)}
                          setValue={() => {}}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        <SharedStyled.FlexCol width="96%" margin="0 0 0 auto">
                          {values?.questions?.map((question, index) => {
                            const stateName = question?.replace(/\s+/g, '-').replace(/\?/g, '') // Removes spaces and question marks to create a valid name
                            return (
                              <InputWithValidation
                                key={index}
                                labelName={question}
                                stateName={stateName}
                                error={false}
                              />
                            )
                          })}

                          <LabelDiv textAlign="left" width="100%" marginTop="8px">
                            Warranty Notes
                          </LabelDiv>
                          <TextArea
                            component="textarea"
                            placeholder="Comments/Notes"
                            as={Field}
                            name="notes"
                            marginTop="8px"
                            height="52px"
                            stateName="notes"
                            labelName="Warranty Notes"
                            error={touched.notes && errors.notes ? true : false}
                          />
                        </SharedStyled.FlexCol>

                        {/* TODO: Change later */}
                        <SharedStyled.FlexRow width="100%" alignItems="flex-start" gap="20px"></SharedStyled.FlexRow>

                        <div style={{ width: '100%' }}>
                          <SharedStyled.Text
                            fontSize="16px"
                            fontWeight="700"
                            textAlign="left"
                            margin="8px auto -10px 0"
                          >
                            Set Appointment
                          </SharedStyled.Text>

                          <CustomSelect
                            labelName="Project Manager Assigned *"
                            stateName="projectManager"
                            error={touched.projectManager && errors.projectManager ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.projectManager}
                            dropDownData={getKeysFromObjects(projectManagerData, 'name')}
                            setValue={() => {}}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />
                        </div>

                        <SharedStyled.FlexBox width="100%" alignItems="center" gap="20px" wrap="wrap" marginTop="25px">
                          <Button disabled={loading} isLoading={loading} type="submit" className="fit">
                            Save Warranty
                          </Button>
                          <Button type="button" className="fit delete" onClick={onClose}>
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>
                      </SharedStyled.Content>
                    </Form>
                    {/* </SharedStyled.ContentContainer> */}
                  </NewLeadContainer>
                  // </LoadScript>
                )
              }}
            </Formik>
          </>
        </SharedStyled.SettingModalContentContainer>
      </ModalContainer>
    </>
  )
}

export default AddLeadWarrantyModal
