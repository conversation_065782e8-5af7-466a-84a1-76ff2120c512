import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { updateJobNotes } from '../../../logic/apis/sales'
import {
  getPercentCompleteFromId,
  isSuccess,
  notify,
  nextAction,
  getDataFromLocalStorage,
} from '../../../shared/helpers/util'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from '.././style'
import { SLoader } from '../../../shared/components/loader/Loader'
import { StorageKey } from '../../../shared/helpers/constants'

function getSortTime(column: any, item: any): number {
  return column?.sortField
    ? new Date(item?.checkpointActivity?.[column?.sortField]?.created)?.getTime()
    : item?.placeInLine
}

const columnsForSubcontractor = ['Ready To Start', 'Current Job'] //  {/* hardcoded code */}

export const StagesBoard = ({
  boardValue,
  percentComplete,
  loading,
}: {
  loading?: boolean
  boardValue: any
  percentComplete: any
}) => {
  const [inputValues, setInputValues] = useState({})
  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, positionDetails } = globalSelector.company
  const handleItemClick = (item: any) => {
    navigate(`/operations/opportunity/${item?._id}`)
  }
  //  Hardcoded
  const isRestrictedOpp =
    positionDetails?.symbol === 'subContractor' ||
    positionDetails?.symbol === 'Foreman' ||
    positionDetails?.symbol === 'CrewMember'

  const handleInputChange = (e: any, cardId: string) => {
    const { value } = e.target
    if (e.nativeEvent.inputType === 'deleteContentBackward' && value.trim() === '') {
      const { [cardId]: _, ...updatedInputValues } = inputValues
      setInputValues(updatedInputValues)
    } else {
      setInputValues((prevState) => ({
        ...prevState,
        [cardId]: value,
      }))
    }
  }

  const handleInputBlur = async (cardId: string, value: string) => {
    // Handle onBlur logic for the specific input using cardId and inputValues[cardId]
    try {
      if (value !== '' && value.length < 12) {
        const res = await updateJobNotes(cardId, value)
        if (isSuccess(res)) {
          notify('Note updated', 'success')
        }
      }
      if (value.length > 12) {
        notify('notes value can not be more than 11', 'error')
      }
    } catch (error) {
      console.log(error)
    }
  }
  const currentDate = new Date()

  const stageLoading = { columns: new Array(5).fill({ cards: new Array(10).fill('1') }) }

  return (
    <>
      {loading
        ? stageLoading.columns.map((column, idx) => {
            return (
              <Styled.StagesBoard key={idx} className="loading">
                <h2>
                  <SLoader />
                </h2>
                <ul className="list-container">
                  {column.cards?.map((card: any, idx2: number) => (
                    <SLoader height={74} key={idx2} />
                  ))}
                </ul>
              </Styled.StagesBoard>
            )
          })
        : boardValue.columns.map((column: any, idx: number) => {
            const isDescending = column?.sortOrder === -1

            if (!columnsForSubcontractor?.includes(column?.title) && isRestrictedOpp) return null //  {/* hardcoded code */}

            return (
              <Styled.StagesBoard key={column.id}>
                {/* <h2 className="heading">{column.title}</h2> */}
                <SharedStyled.FlexBox margin="10px 0 0 10px" gap="5px" alignItems="center">
                  <SharedStyled.TooltipContainer
                    width="180px"
                    positionLeft="0px"
                    positionBottom="0px"
                    positionLeftDecs="90px"
                    positionBottomDecs="unset"
                    // positionRightDecs="20px"
                    positionTopDecs="20px"
                    fontSize="14px"
                  >
                    <span className="tooltip-content">{column?.description || ''}</span>
                    <SharedStyled.Text fontSize="14px" fontWeight="600">
                      {column.title}{' '}
                      <SharedStyled.Text
                        fontSize="14px"
                        fontWeight="400"
                      >{`(${column?.cards?.length})`}</SharedStyled.Text>
                    </SharedStyled.Text>
                  </SharedStyled.TooltipContainer>
                </SharedStyled.FlexBox>

                <ul className="list-container" style={{}}>
                  {column?.title === 'Current Job'
                    ? column?.cards
                        ?.sort((a: any, b: any) => {
                          const timeA = getSortTime(column, a)
                          const timeB = getSortTime(column, b)
                          return isDescending ? timeB - timeA : timeA - timeB
                        })
                        ?.map((card: any, idx2: number) => {
                          const givenDate = new Date(card?.jobCompletedDate)
                          const thirtyDaysLater = new Date(givenDate)
                          thirtyDaysLater.setDate(givenDate.getDate() + 30)
                          const isThirtyDaysForward = currentDate >= thirtyDaysLater
                          return (
                            !isThirtyDaysForward && (
                              <Styled.ListItem
                                key={idx2}
                                className="list-item"
                                onClick={(e: any) => {
                                  // Prevent parent ListItem click when input is clicked
                                  if (e.target.tagName.toLowerCase() !== 'input') {
                                    handleItemClick(card)
                                  }
                                }}
                                percent={getPercentCompleteFromId(card._id, percentComplete)}
                                projectTypeHexColor={card['order-type']?.colorCode}
                                borderColor={nextAction(card)}
                              >
                                <SharedStyled.FlexBox justifyContent="space-between">
                                  <h3>{card?.title}</h3> <h3>{card?.dayCalc}d</h3>
                                </SharedStyled.FlexBox>

                                <SharedStyled.FlexBox justifyContent="space-between">
                                  <p>
                                    P: {card?.jobInfo?.pitch} | SQ: {card?.jobInfo?.install}
                                  </p>
                                  <div>
                                    <input
                                      type="text"
                                      value={inputValues[card._id] || card?.jobNote || ''}
                                      onChange={(e) => handleInputChange(e, card._id)}
                                      onBlur={(e) => handleInputBlur(card._id, e.target.value)}
                                    />
                                  </div>

                                  {card?.agingVal ? (
                                    <SharedStyled.FlexCol
                                      width="max-content"
                                      justifyContent="flex-end"
                                      alignItems="flex-end"
                                    >
                                      <p style={{ marginBottom: '0px' }}>{card?.agingVal}d</p>
                                    </SharedStyled.FlexCol>
                                  ) : null}
                                </SharedStyled.FlexBox>
                              </Styled.ListItem>
                            )
                          )
                        })
                    : column?.cards
                        ?.sort((a: any, b: any) => {
                          const timeA = getSortTime(column, a)
                          const timeB = getSortTime(column, b)
                          return isDescending ? timeB - timeA : timeA - timeB
                        })
                        ?.map((card: any, idx2: number) => {
                          const givenDate = new Date(card?.jobCompletedDate)
                          const thirtyDaysLater = new Date(givenDate)
                          thirtyDaysLater.setDate(givenDate?.getDate() + 30)
                          const isThirtyDaysForward = currentDate >= thirtyDaysLater
                          return (
                            !isThirtyDaysForward && (
                              <Styled.ListItem
                                key={idx2}
                                className="list-item"
                                onClick={(e: any) => {
                                  // Prevent parent ListItem click when input is clicked
                                  if (e.target.tagName.toLowerCase() !== 'input') {
                                    handleItemClick(card)
                                  }
                                }}
                                percent={getPercentCompleteFromId(card._id, percentComplete)}
                                projectTypeHexColor={card['order-type']?.colorCode}
                                borderColor={nextAction(card)}
                              >
                                <SharedStyled.FlexBox justifyContent="space-between">
                                  <h3>{card?.title}</h3> <h3>{card?.dayCalc}d</h3>
                                </SharedStyled.FlexBox>

                                <SharedStyled.FlexBox justifyContent="space-between">
                                  <p>
                                    P: {card?.jobInfo?.pitch} | SQ: {card?.jobInfo?.install}
                                  </p>
                                  <div>
                                    <input
                                      type="text"
                                      value={inputValues[card._id]}
                                      defaultValue={card?.jobNote || ''}
                                      onChange={(e) => handleInputChange(e, card._id)}
                                      onBlur={(e) => handleInputBlur(card._id, e.target.value)}
                                    />
                                  </div>

                                  {card?.agingVal ? (
                                    <SharedStyled.FlexCol
                                      width="max-content"
                                      justifyContent="flex-end"
                                      alignItems="flex-end"
                                    >
                                      <p style={{ marginBottom: '0px' }}>{card?.agingVal}d</p>
                                    </SharedStyled.FlexCol>
                                  ) : null}
                                </SharedStyled.FlexBox>
                              </Styled.ListItem>
                            )
                          )
                        })}
                </ul>
              </Styled.StagesBoard>
            )
          })}
    </>
  )
}
