import { Formik } from 'formik'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { getProjectTypes } from '../../../logic/apis/projects'
import { getOldOpportunities } from '../../../logic/apis/sales'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import { dayjsFormat, getDataFromLocalStorage, getIdFromName, isSuccess, notify } from '../../../shared/helpers/util'
import { Table } from '../../../shared/table/Table'
import * as SharedStyled from '../../../styles/styled'
import { OldCompletedCont } from '../style'
import { AddressWrap } from '../../client/style'
import { StorageKey } from '../../../shared/helpers/constants'

const OldCompleted = () => {
  const navigate = useNavigate()

  const [oldOpp, setOldOpp] = useState<any>([])
  const [loading, setLoading] = useState(true)
  const [type, setType] = useState('')
  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, positionDetails } = globalSelector.company

  const [pageCount, setPageCount] = useState<number>(10)
  const loadMoreRef = useRef(null)

  const fetchIdRef = useRef(0)

  const [initDropDown, setInitTodoData] = useState<any>({
    type: '',
  })

  const completedColumns = [
    {
      Header: 'Client Name',
      accessor: (row: any) => `${row?.fullName}`,
    },
    {
      Header: 'PO#',
      accessor: (row: any) => `${row?.PO}-${row?.num}`,
    },
    {
      Header: 'Type',
      accessor: (row: any) => `${row?.oppType?.name}`,
    },
    {
      Header: 'Volume',
      accessor: (row: any) => `$${row?.soldValue?.toFixed(2)}`,
    },
    {
      Header: 'Material Color',
      accessor: (row: any) => {
        return row?.orderId?.projects?.[0]?.colors?.['44bb8a4e-7562-456f-8a05-40ab7da8e1e6@Shingle Color'] ?? '--'
      },
    },
    {
      Header: 'Address',
      accessor: (row: any) => {
        return (
          <AddressWrap>
            {row?.street ? <p>{row?.street},</p> : null}

            <p>
              {row?.city ? `${row?.city},` : null} {row?.state} {row?.zip}
            </p>
          </AddressWrap>
        )
      },
    },
    {
      Header: 'Completed',
      accessor: (row: any) => `${dayjsFormat(row?.jobCompletedDate, 'M-D-YY')}`,
    },
    {
      Header: 'Days on Job',
      accessor: (row: any) => {
        const jobCompletedDate: any = new Date(row?.jobCompletedDate)
        const jobStartedDate: any = new Date(row?.jobStartedDate)

        // Calculate the time difference in milliseconds
        const timeDifferenceInMillis = jobCompletedDate - jobStartedDate

        // Convert to days
        const daysDifference = timeDifferenceInMillis / (1000 * 60 * 60 * 24)

        return Math.ceil(daysDifference) // Return the difference in days with two decimal places
      },
    },
  ]

  useEffect(() => {
    initFetch()
  }, [])

  useEffect(() => {
    if (positionDetails?.symbol) {
      fetchOldOpportunities()
    }
  }, [type, positionDetails?.symbol])

  const fetchOldOpportunities = async () => {
    try {
      const res = await getOldOpportunities({
        type,
        salesPerson: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : undefined,
      })
      if (isSuccess(res)) {
        const { opps } = res?.data?.data
        setOldOpp(opps)
        setLoading(false)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      setLoading(false)
      console.log(error)
    }
  }

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypesDrop(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      if (positionDetails.symbol) {
        try {
          // This will get called when the table needs new data
          setLoading(true)
          let receivedData: any = []
          let currentCompanyData: any = getDataFromLocalStorage('currentCompany')
          const res = await getOldOpportunities({
            limit: pageSize,
            type,
            salesPerson: positionDetails.symbol === 'SalesPerson' ? positionDetails.memberId : undefined,
          })

          if (res?.data?.statusCode === 200) {
            const { opps } = res?.data?.data

            opps.forEach((res: any, index: number) => {
              receivedData.push({
                ...res,
              })
            })
          } else {
            notify(res?.data?.message, 'error')
          }

          // Give this fetch an ID
          const fetchId = ++fetchIdRef.current

          // Set the loading state
          // setLoading(true)

          // We'll even set a delay to simulate a server here
          // setTimeout(() => {
          // Only update the data if this is the latest fetch
          if (fetchId === fetchIdRef.current) {
            const startRow = pageSize * pageIndex
            const endRow = startRow + pageSize

            setOldOpp(receivedData.slice(startRow, endRow))

            // Your server could send back total page count.
            // For now we'll just fake it, too
            // setPageCount(Math.ceil(receivedData.length / pageSize))
            setLoading(false)
          }
          // }, 1000)
          // setLoading(false)
        } catch (error) {
          console.error('TeamTable fetchData error', error)
          setLoading(false)
        }
      }
    },
    [type, positionDetails?.symbol]
  )

  return (
    <OldCompletedCont>
      <SharedStyled.FlexRow justifyContent="space-between" flexWrap="wrap">
        <SharedStyled.SectionTitle>Completed Operations</SharedStyled.SectionTitle>

        <Formik
          initialValues={initDropDown}
          onSubmit={() => {}}
          validateOnChange={true}
          validateOnBlur={false}
          enableReinitialize={true}
        >
          {({ touched, errors, resetForm, values, setFieldValue }) => {
            useEffect(() => {
              setType(getIdFromName(values.type, projectTypesDrop))
            }, [values.type])
            return (
              <SharedStyled.FlexRow
                margin="0 0 20px 0"
                flexWrap="wrap"
                justifyContent="space-between"
                width="max-content"
              >
                <div className="filter">
                  <CustomSelect
                    labelName="Filter by type"
                    stateName="type"
                    error={touched.type && errors.type ? true : false}
                    value={values.type}
                    dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name), 'All']}
                    setValue={() => {}}
                    setFieldValue={setFieldValue}
                    innerHeight="52px"
                    margin="10px 0 0 0"
                    maxWidth="100%"
                  />
                </div>
              </SharedStyled.FlexRow>
            )
          }}
        </Formik>
      </SharedStyled.FlexRow>

      <Table
        columns={completedColumns}
        data={oldOpp}
        loading={loading}
        fetchData={fetchData}
        onRowClick={(vals) => navigate(`/operations/opportunity/${vals?._id}`)}
        noSearch
        minWidth=""
        noBorder
        ref={loadMoreRef}
        isLoadMoreLoading={loading}
      />
    </OldCompletedCont>
  )
}

export default OldCompleted
