import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'

import { DeleteIcon } from '../../../assets/icons/DeleteIcon'
import { EditIcon } from '../../../assets/icons/EditIcon'
import { PermissionIcon } from '../../../assets/icons/PermissionIcon'
import { RemoveIcon } from '../../../assets/icons/RemoveIcon'
import { getLeadsPosition, getPosition } from '../../../logic/apis/position'
import { CustomModal } from '../../../shared/customModal/CustomModal'
import { getDataFromLocalStorage, notify } from '../../../shared/helpers/util'
import { Table } from '../../../shared/table/Table'
import * as SharedStyled from '../../../styles/styled'
import { colors } from '../../../styles/theme'
// import { ConfirmationPopUp } from './components/confirmationPopup/ConfirmationPopUp'
// import { CreatePositionPopUp } from './components/createPositionPopUp/CreatePositionPopUp'
// import { EditPositionPopUp } from './components/editPositionPopUp/EditPositionPopUp'
import * as Styled from './style'
import { ButtonCont, SettingsCont } from '../../units/style'
import Button from '../../../shared/components/button/Button'
import TabBar from '../../../shared/components/tabBar/TabBar'
// import DeletedPositionSettings from './components/deletedPositionSettings/DeletedPositionSettings'
import { StorageKey } from '../../../shared/helpers/constants'

const LeadsPositionSettings = () => {
  interface I_Data {
    positionName: string
    description: string
  }

  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const loadmoreRef = useRef(null)
  const [loading, setLoading] = useState<boolean>(false)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [showCreatePositionPopUp, setShowCreatePositionPopUp] = useState<boolean>(false)
  const [showEditPositionPopUp, setShowEditPositionPopUp] = useState<boolean>(false)
  const [positionData, setPositionData] = useState<any>({})
  const [initialPermissionData, setInitialPermissionData] = useState<any>([])
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      setLoading(true)
      try {
        // This will get called when the table needs new data

        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const positionResponse = await getLeadsPosition({ deleted: false, skip: pageIndex, limit: '200' }, false)

        if (positionResponse?.data?.statusCode === 200) {
          let statusRes = positionResponse?.data?.data?.position
          let permArray: any = []
          statusRes[0].permissions.forEach((perm: any) => {
            permArray.push({
              resource: perm.resource,
              permissions: [4],
            })
          })

          setInitialPermissionData(permArray)
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              positionName: res?.position,
              description: res?.description || '-',
              action: (
                <>
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    <Styled.IconContainer
                      className="edit"
                      onClick={() => {
                        setPositionData({
                          positionName: res?.position,
                          description: res?.description,
                          id: res?._id,
                          permissions: res.permissions,
                          editable: res.editable,
                        })
                        setShowEditPositionPopUp(true)
                      }}
                    >
                      <EditIcon />
                    </Styled.IconContainer>
                    {res?.deletable && (
                      <Styled.IconContainer
                        className="delete"
                        onClick={() => {
                          setPositionData({
                            positionName: res?.position,
                            description: res?.description,
                            id: res?._id,
                            permissions: res.permissions,
                          })
                          setShowConfirmationPopUp(true)
                        }}
                      >
                        <DeleteIcon />
                      </Styled.IconContainer>
                    )}
                    {/* <Styled.IconContainer
                      className="permission"
                      onClick={() => navigate(`/settings/position/permission/1`)}
                      // onClick={() => {
                      //   setPositionData({ positionName: 'Crew Member', description: 'test' })
                      //   setShowEditPositionPopUp(true)
                      // }}
                    >
                      <PermissionIcon />
                    </Styled.IconContainer> */}
                  </SharedStyled.FlexBox>
                </>
              ),
            })
          })
        } else {
          notify(positionResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))

          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Position Name',
        accessor: 'positionName',
      },
      {
        Header: 'Description',
        accessor: 'description',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  const getAllPositions = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let response = await getPosition({ deleted: false }, false)
      // }
    } catch (error) {
      console.error('getAllPositions error', error)
    }
  }

  useEffect(() => {
    getAllPositions()
  }, [])

  return (
    <SettingsCont gap="24px">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Leads Position</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setShowCreatePositionPopUp(true)
            }}
          >
            Add Position
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <Table
                    columns={columns}
                    data={data}
                    loading={loading}
                    // pageCount={pageCount}
                    fetchData={fetchData}
                    noLink={true}
                    ref={loadmoreRef}
                    isLoadMoreLoading={loading}
                  />
                ),
              },
              {
                title: 'Inactive',
                render: () => <></>,
                // render: () => <DeletedPositionSettings />,
              },
            ]}
            filterComponent={<></>}
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={showConfirmationPopUp}>
        {/* <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Delete Position"
          positionData={positionData}
        /> */}
      </CustomModal>
      <CustomModal show={showCreatePositionPopUp}>
        {/* <CreatePositionPopUp
          setShowCreatePositionPopUp={setShowCreatePositionPopUp}
          setDetailsUpdate={setDetailsUpdate}
          initialPermissionData={initialPermissionData}
        /> */}
      </CustomModal>
      <CustomModal show={showEditPositionPopUp}>
        {/* <EditPositionPopUp
          setDetailsUpdate={setDetailsUpdate}
          setShowEditPositionPopUp={setShowEditPositionPopUp}
          positionData={positionData}
        /> */}
      </CustomModal>
    </SettingsCont>
  )
}

export default LeadsPositionSettings
