import { ErrorMessage, Field, Form, Formik } from 'formik'
import { useCallback, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { getCompanyCrews } from '../../../../logic/apis/crew'
import {
  createDailyLog,
  getDailyLog,
  getDailyLogById,
  getDailyLogPO,
  getDailyLogProjectValue,
  updateDailyLog,
} from '../../../../logic/apis/dailyLog'
import { weatherApi } from '../../../../logic/apis/wheatherApi'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../shared/date/SharedDate'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { onlyMmDdYyyy } from '../../../../shared/helpers/regex'
import {
  endOfDate,
  getDataFromLocalStorage,
  getPoIdFromName,
  getSalesPersonNameFromId,
  isLessThanSevenDays,
  isSuccess,
  notify,
  startOfDate,
} from '../../../../shared/helpers/util'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import * as Styled from './style'
import { ModalHeaderContainer } from '../editTimeCardPopUp/style'
import { CrossContainer, ModalHeader } from '../addTimeCardPopUp/style'
import { ModalHeaderInfo } from '../../../newProject/style'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { ModalContainer } from '../../../taxJurisdiction/taxJurisdictionModal/style'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  date: string
  crew: string
  worked: string
  // weather: string
  highTemp: number
  lowTemp: number
  maxwind_mph: number
  totalprecip_in: number
  // when: string
  isTypeValue?: boolean
  projects: Array<any>
}

interface IDailylog {
  isEdit?: boolean
  onClose?: () => void
  onSubmit?: () => void
  selectedCrewId?: string
  logId?: string
  reportD?: string
  isReportDailyLog?: boolean
  selectedCrewName?: string
  isDisableEdit?: boolean
  pickPo?: any[]
}

const DailyLog = ({
  isEdit,
  onClose,
  onSubmit,
  selectedCrewId,
  logId,
  selectedCrewName,
  reportD,
  isReportDailyLog,
  isDisableEdit,
  pickPo,
}: IDailylog) => {
  // const location = useLocation()
  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */

  // const [showWhen, setShowWhen] = useState<boolean>(false)
  const [formValues, setFormValues] = useState<any>({})
  const [giveFormValue, setGiveFormValue] = useState<any>()
  const [crewData, setCrewData] = useState<any>([])
  const [crewsDataRes, setCrewsDataRes] = useState<any>([])
  const [crewIdData, setCrewIdData] = useState<any>([])
  const [IdCrewData, setIdCrewData] = useState<any>([])
  const [dailyLogId, setDailyLogId] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [dailyLogByIdFlag, setDailyLogByIdFlag] = useState<boolean>(true)
  // const [pickPo, setPickPo] = useState<any>([])
  const [getValueData, setGetValueData] = useState<any>([])
  const [getProjectValueData, setGetProjectValueData] = useState<any>([])
  const [isProjectValueChanged, setIsProjectValueChanged] = useState<any>([])
  const [indexValue, setIndexValue] = useState<number>(0)
  const [projectObjectByName, setProjectObjectByName] = useState<any>()
  const [submitLoading, setSubmitLoading] = useState(false)
  const [isShowmore, setIsShowmore] = useState(false)
  const [allPoList, setAllPoList] = useState([])
  const [allPoData, setAllPoData] = useState<any>([])

  const [projectValueData, setProjectValueData] = useState([{}])

  const crewId = selectedCrewId
  const dailyLog = logId
  const dateFromLocalStorage: any = getDataFromLocalStorage('currentDate')
  const [initialValues, setInitialValues] = useState<InitialValues>({
    date: reportD !== '' && reportD !== undefined ? reportD : dateFromLocalStorage?.replace(/^"(.*)"$/, '$1') ?? '',
    crew: '',
    worked: '',
    // weather: '',
    // when: '',
    highTemp: 0,
    lowTemp: 0,
    maxwind_mph: 0,
    totalprecip_in: 0,
    projects: [
      {
        oppPO: '',
        tearOffDone: 0,
        roofingDone: 0,
        plywoodReplaced: 0,
        nonBillHours: 0,
        manHours: 0,
        nonBillPly: 0,
        materialCosts: 0,
        notes: '',
        oppId: '',
        percentDone: 0,
        isTypeValue: false,
        // projectValues: [
        //   {
        //     doneTearSQValue: '',
        //     doneRoofSQValue: '',
        //     totalTearSQValue: '',
        //     totalRoofSQValue: '',
        //     percentDoneValue: '',
        //     plywoodReplacedValue: '',
        //     nonBillPlyValue: '',
        //     manHoursValue: '',
        //     nonBillHoursValue: '',
        //     materialCostsValue: '',
        //     isTypeValue: false,
        //   },
        // ],
      },
    ],
  })

  /**
   * DailyLogSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const DailyLogSchema = Yup.object().shape({
    // weather: Yup.string().when('weather', {
    //   is: (value: string) => value !== 'None',
    //   then: () => {
    //     setShowWhen(true)
    //     return Yup.string()
    //   },
    //   otherwise: () => {
    //     setShowWhen(false)
    //     return Yup.string()
    //   },
    // }),
    projects: Yup.array().of(
      Yup.object().shape({
        oppPO: Yup.string().required('PO is required'),
        // ... other project field validations ...
      })
    ),
    date: Yup.string().matches(onlyMmDdYyyy, 'Enter the date in MM/DD/YYYY format'),
    worked: Yup.string().required('Worked is required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues) => {
    try {
      setSubmitLoading(true)
      if (submittedValues.projects[0].isTypeValue) {
        submittedValues.projects[0].percentDone = 0
      } else {
        submittedValues.projects[0].roofingDone = 0
        submittedValues.projects[0].tearOffDone = 0
      }
      submittedValues.date = startOfDate(submittedValues.date)

      // const currentTimeDate = new Date().toISOString()
      // submittedValues.date = submittedValues.date + 'T' + currentTimeDate.split('T')[1]
      if (Object.keys(currentCompany)?.length > 0 && Object.keys(currentMember)?.length > 0) {
        setLoading(true)
        let cId: any = crewIdData[submittedValues.crew]
        let dataObj = {
          ...submittedValues,
          crewId: cId,
          // rainTime: submittedValues.when.replace(' ', ''),
          createdBy: currentMember._id,
          worked: submittedValues.worked?.replace(' ', ''),
          projects: submittedValues.projects.map((project: any) => ({
            oppPO: project?.oppPO,
            // oppId: pickPo
            //   ?.filter((value: any) => value?.po === project?.oppPO)
            //   .map((value: any) => String(value?.oppId))
            //   .join(','),
            oppId: getPoIdFromName(project?.oppPO, pickPo),
            tearOffDone: project?.tearOffDone || 0,
            roofingDone: project?.roofingDone || 0,
            plywoodReplaced: project?.plywoodReplaced || 0,
            nonBillHours: project?.nonBillHours || 0,
            manHours: project?.manHours || 0,
            nonBillPly: project?.nonBillPly || 0,
            materialCosts: project?.materialCosts || 0,
            notes: project?.notes || '',
            percentDone: project?.percentDone || 0,
            isTypeValue: project?.isTypeValue || false,
          })),
          auditLog: {
            editedBy: currentMember._id,
            editedAt: new Date(),
          },
        }
        if (!isEdit) {
          const response = await createDailyLog(dataObj)
          if (response?.data?.statusCode === 201) {
            setLoading(false)
            notify('Daily Log Created Successfully', 'success')
            // navigate(`/time-cards/approve`)
            onSubmit?.()
            onClose?.()
          } else {
            notify(response?.data?.message, 'error')
            setLoading(false)
          }
        } else {
          let dataObj = {
            ...submittedValues,
            crewId: crewsDataRes?.find((item: any) => item?.name === submittedValues?.crew)?._id || selectedCrewId,
            // rainTime: submittedValues.when.replace(' ', ''),
            createdBy: currentMember._id,
            worked: submittedValues.worked?.replace(' ', ''),
            dailyLogId: dailyLogId,
            projects: submittedValues.projects.map((project: any) => ({
              oppPO: project?.oppPO,
              oppId: getPoIdFromName(project?.oppPO, pickPo),
              tearOffDone: project?.tearOffDone || 0,
              roofingDone: project?.roofingDone || 0,
              plywoodReplaced: project?.plywoodReplaced || 0,
              nonBillHours: project?.nonBillHours || 0,
              manHours: project?.manHours || 0,
              nonBillPly: project?.nonBillPly || 0,
              materialCosts: project?.materialCosts || 0,
              notes: project?.notes || '',
              percentDone: project?.percentDone || 0,
              isTypeValue: project?.isTypeValue || false,
            })),
            auditLog: {
              editedBy: currentMember._id,
              editedAt: new Date(),
            },
          }

          const response = await updateDailyLog(dataObj)
          if (response?.data?.statusCode === 200) {
            setLoading(false)
            notify('Daily Log Edited Successfully', 'success')
            // navigate(`/time-cards/approve`)
            onSubmit?.()
            onClose?.()
          } else {
            notify(response?.data?.message, 'error')
            setLoading(false)
          }
        }
      }
    } catch (error) {
      console.error('handleSubmit error', error)
    } finally {
      setSubmitLoading(false)
    }
  }

  // const checksForFields = (setFieldValue?: any) => {
  //   try {
  //     if (formValues?.weather !== 'None' && formValues?.weather !== '') {
  //       setShowWhen(true)
  //     } else {
  //       setShowWhen(false)
  //     }
  //   } catch (error) {
  //     console.error('checksForFields error', error)
  //   }
  // }

  const getData = async () => {
    try {
      setLoading(true)
      if (Object.keys(currentCompany)?.length > 0) {
        let cId: any = selectedCrewId
        // let response = await getDailyLog(currentCompany._id, cId)
        // const statusResponse = await getCompanyCrews(
        //   { retired: false, deleted: false, companyId: currentCompany._id },
        //   id
        // )

        const crewResponse = !isReportDailyLog && (await getCompanyCrews({ retired: false, deleted: false }))

        // const dataResult = await getDailyLog(currentCompany._id, cId)
        const getDailyLogByIdValue = dailyLog && (await getDailyLogById(dailyLog))

        if (isSuccess(getDailyLogByIdValue)) {
          setDailyLogByIdFlag(false)
        }

        if (
          isReportDailyLog
            ? true
            : crewResponse?.data?.statusCode === 200 && isEdit
            ? getDailyLogByIdValue?.data?.statusCode === 200
            : true
        ) {
          const dailyData = getDailyLogByIdValue?.data?.data?.dailyLog

          let crewObj: any = []
          let crewIdObj: any = {}
          let idCrewObj: any = {}
          let crewDataArray = crewResponse?.data?.data?.crew
          // let dailyData = dailyResponse?.data?.data?.dailyLog[0]

          if (isReportDailyLog) {
            crewIdObj = { [selectedCrewName]: selectedCrewId }
            idCrewObj = { [selectedCrewId]: selectedCrewName }
          } else {
            crewDataArray.forEach((crew: any) => {
              crewObj.push(crew.name)
              crewIdObj = { ...crewIdObj, [`${crew.name}`]: crew._id }
              idCrewObj = { ...idCrewObj, [`${crew._id}`]: crew.name }
            })
          }
          setCrewsDataRes(crewDataArray)
          setCrewData(crewObj)
          setCrewIdData(crewIdObj)
          setIdCrewData(idCrewObj)
          setDailyLogId(dailyData._id)
          if (isEdit) {
            setInitialValues({
              date: reportD !== '' && reportD !== undefined ? reportD : dailyData?.date,
              crew: idCrewObj[dailyData?.crewId],
              worked: dailyData?.worked,
              highTemp: dailyData?.highTemp,
              lowTemp: dailyData?.lowTemp,
              maxwind_mph: dailyData?.maxwind_mph,
              totalprecip_in: dailyData?.totalprecip_in,
              // weather: dailyData?.weather,
              // when: dailyData?.rainTime,
              projects: dailyData?.projects,
            })
          }
          setLoading(false)
        } else {
          notify(crewResponse?.data?.message, 'error')
          setDailyLogByIdFlag(false)
        }
      }
    } catch (error) {
      setLoading(false)
      setDailyLogByIdFlag(false)
      console.error('getData error', error)
    }
  }

  // useEffect(() => {
  //   if (Object.keys(formValues).length > 0) {
  //     checksForFields()
  //   }
  // }, [formValues])

  const getPickPo = async () => {
    try {
      let clockObj: any = []
      let allClockObj: any = []

      // const result = await getDailyLogPO(currentCompany._id, true)

      // if (result?.data?.projects?.length) {
      //   result?.data?.projects.forEach((clockIn: any) => {
      //     clockObj.push(clockIn)
      //   })
      //   setPickPo(clockObj)
      // }

      // if (!isShowmore) {
      //   const result = await getDailyLogPO(currentCompany._id, true)
      //   if (result?.data?.projects !== 0) {
      //     result?.data?.projects.forEach((clockIn: any) => {
      //       allClockObj.push(clockIn)
      //     })
      //     setAllPoList(allClockObj)
      //   }
      // }
    } catch (error) {
      console.error('getClockIn error', error)
    }
  }

  // const getProjectDetails =useCallback( async (pickPo: any) => {
  //   const selectedOption = pickPo.find((option: any) => option.po === poName)
  //   return selectedOption ? await getDailyPRojectvalues(selectedOption) : null
  // },[pickPo])

  const getDailyPRojectvalues = useCallback(async (projectObjectByName: any) => {
    try {
      if (projectObjectByName && Object.keys(projectObjectByName)?.length && projectObjectByName?.oppId) {
        const res = await getDailyLogProjectValue(projectObjectByName?.oppId, projectObjectByName?.type)
        return res?.data?.SQ
      }

      // setGetProjectValueData((prevApiResponses: any) => [...prevApiResponses, [`projectObjectByName.po`]:res?.data?.SQ])
    } catch (error) {
      console.error(error)
    }
  }, [])

  // const getProjectDetails1 = useCallback(async () => {
  //   const promiseArray: any = pickPo?.map((item: any, index: number) => {
  //     return getDailyPRojectvalues(item)
  //   })
  //   const response = await Promise.all(promiseArray)
  //   let data: any = {}
  //   response.forEach((_, index: number) => {
  //     data[pickPo?.[index].po] = response[index]
  //     setGetProjectValueData(data)
  //   })
  // }, [pickPo, getDailyPRojectvalues])

  // useEffect(() => {
  //   if (pickPo) {
  //     getProjectDetails1()
  //   }
  // }, [pickPo])

  const fetchWheaterApi = async (date: string, zipcode: string, setFieldValue: any, index?: number) => {
    try {
      const res = await weatherApi(zipcode, date)
      if (isSuccess(res)) {
        setFieldValue(`highTemp`, res?.data?.forecast?.forecastday?.[0]?.day?.maxtemp_f)
        setFieldValue(`lowTemp`, res?.data?.forecast?.forecastday?.[0]?.day?.mintemp_f)
        setFieldValue(`maxwind_mph`, res?.data?.forecast?.forecastday?.[0]?.day?.maxwind_mph)
        setFieldValue(`totalprecip_in`, res?.data?.forecast?.forecastday?.[0]?.day?.totalprecip_in)
      }
    } catch (error) {
      console.error(error)
    }
  }

  const mapWorkBackendField = (response: string) => {
    switch (response) {
      case 'FullDay':
        return 'Full Day'
      case 'PartialDay':
        return 'Partial Day'
      case 'DayOff':
        return 'Day Off'
      default:
        return response
    }
  }

  useEffect(() => {
    const logDate =
      reportD !== '' && reportD !== undefined ? reportD : dateFromLocalStorage?.replace(/^"(.*)"$/, '$1') ?? ''
    if (!isEdit && !isLessThanSevenDays(logDate)) {
      notify('Weather data unavailable,please enter manually', 'info')
    }
  }, [isEdit])

  useEffect(() => {
    if (pickPo?.length) {
      ;(async () => {
        const promiseArray: any = pickPo?.map((selected: any) => {
          return getDailyPRojectvalues(selected)
        })
        const response = await Promise.all(promiseArray)

        const customResponse = response?.map((itm: any, idx: number) => ({ ...itm, oppId: pickPo?.[idx]?.oppId }))

        setAllPoData(customResponse)
      })()
    }
  }, [pickPo?.length])
  return (
    <Styled.DailyLogContainer>
      <ModalContainer>
        <ModalHeaderContainer>
          <SharedStyled.FlexRow>
            <img src={UnitSvg} alt="modal icon" />
            <SharedStyled.FlexCol>
              <ModalHeader>{isEdit ? 'Edit Daily Log' : 'Add Daily Log'}</ModalHeader>
            </SharedStyled.FlexCol>
          </SharedStyled.FlexRow>
          <CrossContainer
            onClick={() => {
              onClose?.()
            }}
          >
            <CrossIcon />
          </CrossContainer>
        </ModalHeaderContainer>
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={DailyLogSchema}
          enableReinitialize={true}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ errors, touched, values, handleChange, setFieldValue }) => {
            // setFormValues(values)

            const scrollToFirstError = () => {
              const errorFields = Object.keys(errors) // Get fields with errors

              const firstErrorOrEmptyField = errorFields.find((field: any) => errors[field])
              if (firstErrorOrEmptyField) {
                const element = document.querySelector(`[name="${firstErrorOrEmptyField}"]`)
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                }
              }
            }

            const getDailyProjectValue = async () => {
              try {
                const dataValue = pickPo?.filter((val: any) => val?.po === getValueData)
                // const result = await getDailyLogProjectValue(currentCompany._id, dataValue[0].oppId, dataValue[0].type)

                setFieldValue(`projects.${indexValue}.isTypeValue`, dataValue[0]?.type)
                setFieldValue(`projects.${indexValue}.oppPO`, dataValue[0]?.po)
                setFieldValue(`projects.${indexValue}.oppId`, dataValue[0]?.oppId)

                // setFieldValue(`projects.${indexValue}.projectValues`, {
                //   doneTearSQValue: result?.data?.SQ?.doneTearSQ,
                //   doneRoofSQValue: result?.data?.SQ?.doneRoofSQ,
                //   totalTearSQValue: result?.data?.SQ?.totalTearSQ,
                //   totalRoofSQValue: result?.data?.SQ?.totalRoofSQ,
                //   percentDoneValue: result?.data?.SQ?.percentDone,
                //   plywoodReplacedValue: result?.data?.SQ?.plywoodReplaced,
                //   nonBillPlyValue: result?.data?.SQ?.nonBillPly,
                //   manHoursValue: result?.data?.SQ?.manHours,
                //   nonBillHoursValue: result?.data?.SQ?.nonBillHours,
                //   materialCostsValue: result?.data?.SQ?.materialCosts,
                //   isTypeValue: dataValue[0].type,
                // })
              } catch (e) {
                console.warn(e)
              }
            }
            useEffect(() => {
              getData()
              getDailyProjectValue()
            }, [selectedCrewId])
            useEffect(() => {
              getPickPo()
            }, [selectedCrewId, isShowmore])

            useEffect(() => {
              getDailyProjectValue()
            }, [getValueData, indexValue])
            // setGiveFormValue(setFieldValue)
            useEffect(() => {
              setFieldValue('crew', getSalesPersonNameFromId(crewId!, crewsDataRes))
            }, [getSalesPersonNameFromId(crewId!, crewsDataRes)])
            // useEffect(() => {
            //   if (projectObjectByName) {
            //     getDailyPRojectvalues(projectObjectByName)
            //   }
            // }, [projectObjectByName])

            useEffect(() => {
              // values.projects.forEach((project: any, index: number) => {
              //   if (project.oppPO !== '') {
              //     const poToFind = project.oppPO
              //     const zipCode = pickPo.find((item: any) => item.po === poToFind)?.zipCode
              //     if (zipCode) {
              //       fetchWheaterApi(values.date, zipCode, setFieldValue, index)
              //     }
              //   }
              // })
              if (values?.projects?.[0]?.oppPO !== '') {
                const poToFind = values?.projects?.[0]?.oppPO
                const zipCode = pickPo?.find((item: any) => item?.po === poToFind)?.zipCode
                if (zipCode) {
                  isLessThanSevenDays(values.date) && fetchWheaterApi(values.date, zipCode, setFieldValue)
                }
              }
            }, [values?.projects?.[0]?.oppPO])

            let selectedPo: string[] = []
            const poArray = values.projects?.filter((a: { oppPO: string; oppId: string }) => a?.oppPO && a?.oppId)

            useEffect(() => {
              if (poArray?.length) {
                ;(async () => {
                  const responseData = poArray?.map((selected: any) => {
                    const poData = allPoData?.find((po: any) => po?.oppId === selected?.oppId)
                    return poData
                  })

                  setProjectValueData(responseData)
                })()
              }
            }, [poArray?.length, JSON.stringify(poArray), pickPo?.length, allPoList?.length, isShowmore, getValueData])

            useEffect(() => {
              // const getProjectData = async () => {
              //   const poObj = pickPo?.find((item: any) => item?.po === values?.projects?.[0]?.oppPO)
              //   const response = await getDailyPRojectvalues(poObj)
              //   setProjectValueData(response)
              // }
              if (isReportDailyLog && values?.projects?.[0]?.oppPO) {
                setFieldValue('crew', selectedCrewName)
                // getProjectData()
              }
            }, [isReportDailyLog, values?.projects?.[0]?.oppPO, pickPo?.length, allPoList?.length])

            return (
              <>
                {dailyLogByIdFlag ? (
                  <SharedStyled.FlexBox
                    padding="0px 20px 20px 20px"
                    width="100%"
                    justifyContent="center"
                    alignItems="center"
                    flexDirection="column"
                  >
                    <SharedStyled.Skeleton custWidth="100%" custHeight="50px" />
                    <SharedStyled.Skeleton custWidth="100%" custHeight="100px" custMarginTop="10px" />
                    <SharedStyled.Skeleton custWidth="100%" custHeight="100px" custMarginTop="10px" />
                    <SharedStyled.Skeleton custWidth="100%" custHeight="100px" custMarginTop="10px" />
                  </SharedStyled.FlexBox>
                ) : (
                  <Form className="form">
                    <SharedDate
                      value={values.date}
                      labelName="Date"
                      stateName="date"
                      error={touched.date && errors.date ? true : false}
                      setFieldValue={setFieldValue}
                      disabled={isDisableEdit}
                    />
                    <CustomSelect
                      value={values.crew}
                      labelName="Crew"
                      stateName="crew"
                      dropDownData={crewData}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.crew && errors.crew ? true : false}
                      showInitialValue={isReportDailyLog}
                      disabled={isReportDailyLog || isDisableEdit}
                    />
                    <CustomSelect
                      value={mapWorkBackendField(values.worked)}
                      labelName="Worked"
                      stateName="worked"
                      dropDownData={['Full Day', 'Partial Day', 'Day Off']}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      margin="10px 0 0 0"
                      error={touched.worked && errors.worked ? true : false}
                      disabled={isDisableEdit}
                    />
                    {/* <Styled.SectionHeader marginTop="12px">Weather</Styled.SectionHeader>
              <CustomSelect
                value={values.weather}
                labelName="Weather"
                stateName="weather"
                dropDownData={['None', 'Rain', 'Snow']}
                setValue={() => {}}
                margin="10px 0 0 0"
                setFieldValue={setFieldValue}
                error={touched.weather && errors.weather ? true : false}
              /> */}
                    {/* {showWhen && (
                <CustomSelect
                  value={values.when}
                  labelName="When"
                  stateName="when"
                  dropDownData={['Early', 'Late', 'All Day']}
                  setValue={() => {}}
                  margin="10px 0 0 0"
                  setFieldValue={setFieldValue}
                  error={touched.when && errors.when ? true : false}
                />
              )} */}

                    <Styled.SectionHeader marginTop="12px">Projects</Styled.SectionHeader>
                    <Styled.WorkDoneContainer name="projects">
                      {(fieldArrayProps: any) => {
                        const { push, remove } = fieldArrayProps
                        const handleProjectValue = (index: number, name: string, value: string) => {
                          setIndexValue(index)
                        }

                        // useEffect(() => {
                        //   if (values?.projects?.[index]?.oppPO !== '') {
                        //     const poToFind = values?.projects?.[index]?.oppPO
                        //     const zipCode = pickPo.find((item: any) => item.po === poToFind)?.zipCode
                        //     if (zipCode) {
                        //       fetchWheaterApi(values.date, zipCode, setFieldValue, index)
                        //     }
                        //   }
                        // }, [values?.projects?.[index]?.oppPO])

                        return (
                          <>
                            {values.projects.map((work: any, index: number) => {
                              {
                                if (values.projects[index].highTemp < values.projects[index].lowTemp) {
                                  notify('High temp must be greater than low temp', 'error')
                                  setFieldValue('lowTemp', values.projects[index].highTemp - 1)
                                }
                                const data = projectValueData[index]
                                // const data = projectValueData
                                selectedPo.push(values?.projects[index].oppPO)

                                // getProjectValueData?.[index]?.map((obj: any, index: number) => {
                                return loading ? (
                                  <>
                                    {/* <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} /> */}
                                    <SharedStyled.Skeleton custWidth="40%" custHeight="50%" custMarginTop="10px" />
                                    <SharedStyled.Skeleton custWidth="40%" custHeight="50%" custMarginTop="10px" />
                                  </>
                                ) : (
                                  <>
                                    <Styled.ProjectContainer marginTop="6px">
                                      <Styled.ProjectHeader>
                                        <Styled.ProjectSmallHeader name="projects">
                                          #{index + 1} PO:
                                        </Styled.ProjectSmallHeader>
                                        <CustomSelect
                                          labelName="Select"
                                          error={values?.projects[index].oppPO ? false : true}
                                          value={values?.projects[index].oppPO}
                                          setValue={setGetValueData}
                                          dropDownData={
                                            values?.projects[index].oppPO !== ''
                                              ? [values?.projects[index].oppPO].concat(
                                                  pickPo
                                                    ?.filter((item: any) => !selectedPo?.includes(item?.po))
                                                    ?.map((values: any) => values?.po)
                                                    ?.sort((a: any, b: any) => a?.localeCompare(b))
                                                )
                                              : pickPo
                                                  ?.filter((item: any) => !selectedPo?.includes(item?.po))
                                                  ?.map((values: any) => values?.po)
                                                  ?.sort((a: any, b: any) => a?.localeCompare(b))
                                          }
                                          setFieldValue={setFieldValue}
                                          innerHeight="45px"
                                          margin="10px 0 0 0"
                                          stateName={`projects.${index}.oppPO`}
                                          index={index}
                                          // onAddClick={() => {
                                          //   setIsShowmore(true)
                                          // }}
                                          handleProjectValue={handleProjectValue}
                                          // getProjectDetails={getProjectDetails}
                                          object={pickPo}
                                          // isShowmore={isShowmore}
                                          // showSeeMoreOption
                                          disabled={isDisableEdit}
                                        />
                                      </Styled.ProjectHeader>

                                      {values?.projects[index].oppPO && (
                                        <>
                                          <Styled.WorkHeader marginTop="10px">
                                            <b>Work Completed (entire crew):</b>
                                          </Styled.WorkHeader>

                                          {values?.projects[index]?.isTypeValue ? (
                                            <>
                                              <Styled.SingleFieldNameContainer marginTop="10px">
                                                <Styled.NameValueUnitContainer width="100%">
                                                  <Styled.NameDiv paddingRight="10px">
                                                    Tear Off:{' '}
                                                    {`${data?.doneTearSQ?.toFixed(2) ?? ''} / 
                                                ${data?.totalTearSQ?.toFixed(2) ?? ''} done`}
                                                    {/* ({values?.projects[index]?.projectValues?.doneTearSQValue}/
                                            {values?.projects[index]?.projectValues?.totalTearSQValue} done): */}
                                                  </Styled.NameDiv>

                                                  <Styled.InputWrap>
                                                    <Styled.ValueInput
                                                      name={`projects.${index}.tearOffDone`}
                                                      type="number"
                                                      onChange={(e: any) => {
                                                        const re = /^[0-9]*\.?[0-9]+$/
                                                        if (e.target.value === '' || re.test(e.target.value)) {
                                                          handleChange(e)
                                                        }
                                                      }}
                                                      disabled={isDisableEdit}
                                                      borderRadius="4px 0px 0px 4px"
                                                      onWheel={(e: any) => {
                                                        e.target.blur()
                                                      }}
                                                      defaultValue={initialValues?.projects[index]?.tearOffDone}
                                                      validate={(value: any) => {
                                                        const oldData = initialValues?.projects[index]?.tearOffDone

                                                        if (!isEdit && value + data?.doneTearSQ > data?.totalTearSQ)
                                                          return 'You have entered too many SQ done in Tear Off'

                                                        if (
                                                          (isEdit && value > (data?.totalTearSQ ?? 0)) ||
                                                          (isEdit &&
                                                            value < (data?.totalTearSQ ?? 0) &&
                                                            (data?.doneTearSQ ?? 0) - oldData + value >
                                                              (data?.totalTearSQ ?? 0))
                                                        ) {
                                                          return 'You have entered too many SQ done in Tear Off'
                                                        }
                                                      }}
                                                    />
                                                    <Styled.UnitDiv>SQ</Styled.UnitDiv>
                                                  </Styled.InputWrap>
                                                </Styled.NameValueUnitContainer>
                                              </Styled.SingleFieldNameContainer>

                                              <Styled.ErrorContainer>
                                                <Styled.ErrorMsg>
                                                  <ErrorMessage
                                                    name={`projects.${index}.tearOffDone`}
                                                    component="div"
                                                    className="error"
                                                  />
                                                </Styled.ErrorMsg>
                                              </Styled.ErrorContainer>

                                              <Styled.SingleFieldNameContainer marginTop="10px">
                                                <Styled.NameValueUnitContainer width="100%">
                                                  <Styled.NameDiv paddingRight="10px">
                                                    Roofing:{' '}
                                                    {`${data?.doneRoofSQ?.toFixed(2) ?? ''} / ${
                                                      data?.totalRoofSQ?.toFixed(2) ?? ''
                                                    } done`}
                                                    {/* ({values?.projects[index]?.projectValues?.doneRoofSQValue}/
                                            {values?.projects[index]?.projectValues?.totalRoofSQValue} done): */}
                                                  </Styled.NameDiv>
                                                  <Styled.InputWrap>
                                                    <Styled.ValueInput
                                                      name={`projects.${index}.roofingDone`}
                                                      type="number"
                                                      onChange={(e: any) => {
                                                        const re = /^[0-9]*\.?[0-9]+$/
                                                        if (e.target.value === '' || re.test(e.target.value)) {
                                                          handleChange(e)
                                                        }
                                                      }}
                                                      borderRadius="4px 0px 0px 4px"
                                                      onWheel={(e: any) => {
                                                        e.target.blur()
                                                      }}
                                                      defaultValue={initialValues?.projects[index]?.roofingDone}
                                                      validate={(value: any) => {
                                                        const oldData = initialValues?.projects[index]?.roofingDone

                                                        if (!isEdit && value + data?.doneRoofSQ > data?.totalRoofSQ)
                                                          return 'You have entered too many SQ done in Roofing'

                                                        if (
                                                          (isEdit && value > (data?.totalRoofSQ ?? 0)) ||
                                                          (isEdit &&
                                                            value < (data?.totalRoofSQ ?? 0) &&
                                                            (data?.doneRoofSQ ?? 0) - oldData + value >
                                                              (data?.totalRoofSQ ?? 0))
                                                        ) {
                                                          return 'You have entered too many SQ done in Roofing'
                                                        }
                                                      }}
                                                      disabled={isDisableEdit}
                                                    />
                                                    <Styled.UnitDiv>SQ</Styled.UnitDiv>
                                                  </Styled.InputWrap>
                                                </Styled.NameValueUnitContainer>
                                              </Styled.SingleFieldNameContainer>

                                              <Styled.ErrorContainer>
                                                <Styled.ErrorMsg>
                                                  <ErrorMessage
                                                    name={`projects.${index}.roofingDone`}
                                                    component="div"
                                                    className="error"
                                                  />
                                                </Styled.ErrorMsg>
                                              </Styled.ErrorContainer>
                                            </>
                                          ) : (
                                            <>
                                              <Styled.SingleFieldNameContainer marginTop="10px">
                                                <Styled.NameValueUnitContainer width="100%">
                                                  <Styled.NameDiv paddingRight="10px">
                                                    % Completed Today:&nbsp; ({data?.percentDone?.toFixed(2)}% so far)
                                                    {/* ({values?.projects[index]?.projectValues?.percentDoneValue}%
                                          so far) */}
                                                  </Styled.NameDiv>
                                                  <Styled.InputWrap>
                                                    <Styled.ValueInput
                                                      name={`projects.${index}.percentDone`}
                                                      type="number"
                                                      onChange={(e: any) => {
                                                        const re = /^[0-9]*\.?[0-9]+$/
                                                        if (e.target.value === '' || re.test(e.target.value)) {
                                                          handleChange(e)
                                                        }
                                                      }}
                                                      borderRadius="4px 0px 0px 4px"
                                                      onWheel={(e: any) => {
                                                        e.target.blur()
                                                      }}
                                                      disabled={isDisableEdit}
                                                      defaultValue={initialValues?.projects[index]?.percentDone}
                                                      validate={(value: any) => {
                                                        const oldData = initialValues?.projects[index]?.percentDone
                                                        if (!isEdit && value + data?.percentDone > 100) {
                                                          return 'You have entered More than 100%'
                                                        }

                                                        if (
                                                          (isEdit && value > 100) ||
                                                          (isEdit &&
                                                            value < 100 &&
                                                            data?.percentDone - oldData + value > 100)
                                                        ) {
                                                          return 'You have entered More than 100%'
                                                        }
                                                      }}
                                                    />
                                                    <Styled.UnitDiv>%</Styled.UnitDiv>
                                                  </Styled.InputWrap>
                                                </Styled.NameValueUnitContainer>
                                              </Styled.SingleFieldNameContainer>
                                              <Styled.ErrorContainer>
                                                <Styled.ErrorMsg>
                                                  <ErrorMessage
                                                    name={`projects.${index}.percentDone`}
                                                    component="div"
                                                    className="error"
                                                  />
                                                </Styled.ErrorMsg>
                                              </Styled.ErrorContainer>
                                            </>
                                          )}

                                          <Styled.WorkHeader marginTop="6px">
                                            <b>Extra Work:</b>
                                          </Styled.WorkHeader>
                                          <Styled.SingleFieldNameContainer marginTop="10px">
                                            <Styled.NameValueUnitContainer width="100%">
                                              <Styled.NameDiv paddingRight="10px">
                                                R&R Plywood: ({data?.plywoodReplaced} sheets so far)
                                                {/* ({values?.projects[index]?.projectValues?.plywoodReplacedValue}{' '}
                                        sheets so far): */}
                                              </Styled.NameDiv>

                                              <Styled.InputWrap>
                                                <Styled.ValueInput
                                                  name={`projects.${index}.plywoodReplaced`}
                                                  type="number"
                                                  onChange={(e: any) => {
                                                    const re = /^[0-9]*\.?[0-9]+$/
                                                    if (e.target.value === '' || re.test(e.target.value)) {
                                                      handleChange(e)
                                                    }
                                                  }}
                                                  borderRadius="4px 0px 0px 4px"
                                                  onWheel={(e: any) => {
                                                    e.target.blur()
                                                  }}
                                                  disabled={isDisableEdit}
                                                />
                                                <Styled.UnitDiv>EA</Styled.UnitDiv>
                                              </Styled.InputWrap>
                                            </Styled.NameValueUnitContainer>
                                          </Styled.SingleFieldNameContainer>
                                          <Styled.SingleFieldNameContainer marginTop="10px">
                                            <Styled.NameValueUnitContainer width="100%">
                                              <Styled.NameDiv paddingRight="10px">
                                                Non-billable Plywood: ({data?.nonBillPly} sheets so far)
                                                {/* ({values?.projects[index]?.projectValues?.nonBillPlyValue}{' '}
                                        sheets so far): */}
                                              </Styled.NameDiv>
                                              <Styled.InputWrap>
                                                <Styled.ValueInput
                                                  name={`projects.${index}.nonBillPly`}
                                                  type="number"
                                                  onChange={(e: any) => {
                                                    const re = /^[0-9]*\.?[0-9]+$/
                                                    if (e.target.value === '' || re.test(e.target.value)) {
                                                      handleChange(e)
                                                    }
                                                  }}
                                                  borderRadius="4px 0px 0px 4px"
                                                  onWheel={(e: any) => {
                                                    e.target.blur()
                                                  }}
                                                  disabled={isDisableEdit}
                                                />
                                                <Styled.UnitDiv>EA</Styled.UnitDiv>
                                              </Styled.InputWrap>
                                            </Styled.NameValueUnitContainer>
                                          </Styled.SingleFieldNameContainer>
                                          <Styled.SingleFieldNameContainer marginTop="10px">
                                            <Styled.NameValueUnitContainer width="100%">
                                              <Styled.NameDiv paddingRight="10px">
                                                Billable Hours: ({data?.manHours} hours so far)
                                              </Styled.NameDiv>
                                              <Styled.InputWrap>
                                                <Styled.ValueInput
                                                  name={`projects.${index}.manHours`}
                                                  type="number"
                                                  onChange={(e: any) => {
                                                    const re = /^[0-9]*\.?[0-9]+$/
                                                    if (e.target.value === '' || re.test(e.target.value)) {
                                                      handleChange(e)
                                                    }
                                                  }}
                                                  borderRadius="4px 0px 0px 4px"
                                                  onWheel={(e: any) => {
                                                    e.target.blur()
                                                  }}
                                                  disabled={isDisableEdit}
                                                />
                                                <Styled.UnitDiv>HRS</Styled.UnitDiv>
                                              </Styled.InputWrap>
                                            </Styled.NameValueUnitContainer>
                                          </Styled.SingleFieldNameContainer>
                                          <Styled.SingleFieldNameContainer marginTop="10px">
                                            <Styled.NameValueUnitContainer width="100%">
                                              <Styled.NameDiv paddingRight="10px">
                                                Non-Billable Hours: ({data?.nonBillHours} hours so far)
                                                {/* ({values?.projects[index]?.projectValues?.nonBillHoursValue}{' '}
                                        hours so far): */}
                                              </Styled.NameDiv>
                                              <Styled.InputWrap>
                                                <Styled.ValueInput
                                                  name={`projects.${index}.nonBillHours`}
                                                  type="number"
                                                  onChange={(e: any) => {
                                                    const re = /^[0-9]*\.?[0-9]+$/
                                                    if (e.target.value === '' || re.test(e.target.value)) {
                                                      handleChange(e)
                                                    }
                                                  }}
                                                  borderRadius="4px 0px 0px 4px"
                                                  onWheel={(e: any) => {
                                                    e.target.blur()
                                                  }}
                                                  disabled={isDisableEdit}
                                                />
                                                <Styled.UnitDiv>HRS</Styled.UnitDiv>
                                              </Styled.InputWrap>
                                            </Styled.NameValueUnitContainer>
                                          </Styled.SingleFieldNameContainer>
                                          <Styled.SingleFieldNameContainer marginTop="10px">
                                            <Styled.NameValueUnitContainer width="100%">
                                              <Styled.NameDiv paddingRight="10px">
                                                Add'l Materials: ${`(${data?.materialCosts?.toFixed(2)} so far)`}
                                                {/* {`${values?.projects[index]?.projectValues?.materialCostsValue} so far):`} */}
                                              </Styled.NameDiv>
                                              <Styled.InputWrap className="material">
                                                <Styled.UnitDiv borderRadius="4px 0px 0px 4px">$</Styled.UnitDiv>
                                                <Styled.ValueInput
                                                  name={`projects.${index}.materialCosts`}
                                                  type="number"
                                                  onChange={(e: any) => {
                                                    const re = /^[0-9]*\.?[0-9]+$/
                                                    if (e.target.value === '' || re.test(e.target.value)) {
                                                      handleChange(e)
                                                    }
                                                  }}
                                                  borderRadius="0px 4px 4px 0px"
                                                  onWheel={(e: any) => {
                                                    e.target.blur()
                                                  }}
                                                  disabled={isDisableEdit}
                                                />
                                              </Styled.InputWrap>
                                            </Styled.NameValueUnitContainer>
                                          </Styled.SingleFieldNameContainer>

                                          <Styled.WorkMiniHeader marginTop="11px">
                                            Notes (on extra work, problems, or other issues):
                                          </Styled.WorkMiniHeader>
                                          <Styled.TextArea
                                            component="textarea"
                                            as={Field}
                                            name={`projects.${index}.notes`}
                                            marginTop="8px"
                                            height="50px"
                                            disabled={isDisableEdit}
                                          />
                                        </>
                                      )}

                                      <SharedStyled.FlexBox gap="5px" alignItems="center" margin="12px 0 0 0">
                                        {index > 0 && (
                                          <Button
                                            type="button"
                                            className="fit delete"
                                            onClick={() => remove(index)}
                                            disabled={isDisableEdit}
                                          >
                                            Remove Project
                                          </Button>
                                        )}
                                      </SharedStyled.FlexBox>
                                    </Styled.ProjectContainer>
                                  </>
                                )
                                // })
                              }
                            })}
                            <SharedStyled.FlexRow margin="12px 0 0 0">
                              <Button
                                type="button"
                                width="max-content"
                                disabled={isDisableEdit}
                                onClick={() =>
                                  push({
                                    oppPO: '',
                                    tearOffDone: 0,
                                    roofingDone: 0,
                                    plywoodReplaced: 0,
                                    nonBillHours: 0,
                                    manHours: 0,
                                    nonBillPly: 0,
                                    materialCosts: 0,
                                    notes: '',
                                    percentDone: 0,
                                    // highTemp: 0,
                                    // lowTemp: 0,
                                    // maxwind_mph: 0,
                                    // totalprecip_in: 0,
                                    // projectValues: [
                                    //   {
                                    //     doneTearSQValue: '',
                                    //     doneRoofSQValue: '',
                                    //     totalTearSQValue: '',
                                    //     totalRoofSQValue: '',
                                    //     percentDoneValue: '',
                                    //     plywoodReplacedValue: '',
                                    //     nonBillPlyValue: '',
                                    //     manHoursValue: '',
                                    //     nonBillHoursValue: '',
                                    //     materialCostsValue: '',
                                    //   },
                                    // ],
                                  })
                                }
                              >
                                Add Project
                              </Button>
                            </SharedStyled.FlexRow>
                            <Styled.WeatherCont>
                              <Styled.SingleFieldNameContainer marginTop="10px">
                                <Styled.NameValueUnitContainer>
                                  <Styled.NameDiv paddingRight="10px">High Temp:</Styled.NameDiv>
                                  <Styled.ValueInput
                                    name={`highTemp`}
                                    type="number"
                                    borderRadius="4px 0px 0px 4px"
                                    onWheel={(e: any) => {
                                      e.target.blur()
                                    }}
                                    style={{ width: '60px' }}
                                  />
                                  <Styled.UnitDiv>°F</Styled.UnitDiv>
                                </Styled.NameValueUnitContainer>
                              </Styled.SingleFieldNameContainer>
                              <Styled.SingleFieldNameContainer marginTop="10px">
                                <Styled.NameValueUnitContainer>
                                  <Styled.NameDiv paddingRight="10px">Low Temp:</Styled.NameDiv>
                                  <Styled.ValueInput
                                    name={`lowTemp`}
                                    type="number"
                                    borderRadius="4px 0px 0px 4px"
                                    onWheel={(e: any) => {
                                      e.target.blur()
                                    }}
                                    style={{ width: '60px' }}
                                  />
                                  <Styled.UnitDiv>°F</Styled.UnitDiv>
                                </Styled.NameValueUnitContainer>
                              </Styled.SingleFieldNameContainer>

                              <Styled.SingleFieldNameContainer marginTop="10px">
                                <Styled.NameValueUnitContainer>
                                  <Styled.NameDiv paddingRight="10px">Max Wind:</Styled.NameDiv>
                                  <Styled.ValueInput
                                    name={`maxwind_mph`}
                                    type="number"
                                    onChange={(e: any) => {
                                      const re = /^[0-9]*\.?[0-9]+$/
                                      if (e.target.value === '' || re.test(e.target.value)) {
                                        handleChange(e)
                                      }
                                    }}
                                    borderRadius="4px 0px 0px 4px"
                                    onWheel={(e: any) => {
                                      e.target.blur()
                                    }}
                                    style={{ width: '60px' }}
                                  />
                                  <Styled.UnitDiv>mph</Styled.UnitDiv>
                                </Styled.NameValueUnitContainer>
                              </Styled.SingleFieldNameContainer>
                              <Styled.SingleFieldNameContainer marginTop="10px">
                                <Styled.NameValueUnitContainer>
                                  <Styled.NameDiv paddingRight="10px">Rain/Snow:</Styled.NameDiv>
                                  <Styled.ValueInput
                                    name={`totalprecip_in`}
                                    type="number"
                                    onChange={(e: any) => {
                                      const re = /^[0-9]*\.?[0-9]+$/
                                      if (e.target.value === '' || re.test(e.target.value)) {
                                        handleChange(e)
                                      }
                                    }}
                                    borderRadius="4px 0px 0px 4px"
                                    onWheel={(e: any) => {
                                      e.target.blur()
                                    }}
                                    style={{ width: '60px' }}
                                  />
                                  <Styled.UnitDiv>IN</Styled.UnitDiv>
                                </Styled.NameValueUnitContainer>
                              </Styled.SingleFieldNameContainer>
                            </Styled.WeatherCont>
                          </>
                        )
                      }}
                    </Styled.WorkDoneContainer>
                    <SharedStyled.FlexRow margin="40px 0 0 0">
                      <Button
                        type="submit"
                        maxWidth="200px"
                        isLoading={submitLoading}
                        onClick={() => {
                          scrollToFirstError()
                        }}
                        disabled={isDisableEdit}
                      >
                        Submit Daily Log
                      </Button>
                    </SharedStyled.FlexRow>
                  </Form>
                )}
              </>
            )
          }}
        </Formik>
      </ModalContainer>
    </Styled.DailyLogContainer>
  )
}

export default DailyLog
