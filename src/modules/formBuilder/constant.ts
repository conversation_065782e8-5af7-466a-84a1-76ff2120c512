export var options = {
  inputSets: [
    {
      label: 'User Details',
      name: 'user-details', // optional - one will be generated from the label if name not supplied
      showHeader: true, // optional - Use the label as the header for this set of inputs
      fields: [
        {
          type: 'text',
          label: 'First Name',
          className: 'form-control',
        },
        {
          type: 'select',
          label: 'Profession',
          className: 'form-control',
          values: [
            {
              label: 'Street Sweeper',
              value: 'option-2',
              selected: false,
            },
            {
              label: 'Brain Surgeon',
              value: 'option-3',
              selected: false,
            },
          ],
        },
        {
          type: 'textarea',
          label: 'Short Bio:',
          className: 'form-control',
        },
      ],
    },
    {
      label: 'User Agreement',
      fields: [
        {
          type: 'header',
          subtype: 'h2',
          label: 'Terms &amp; Conditions',
          className: 'header',
        },
        {
          type: 'paragraph',
          label:
            'Leverage agile frameworks to provide a robust synopsis for high level overviews. Iterative approaches to corporate strategy foster collaborative thinking to further the overall value proposition. Organically grow the holistic world view of disruptive innovation via workplace diversity and empowerment.',
        },
        {
          type: 'paragraph',
          label:
            'Bring to the table win-win survival strategies to ensure proactive domination. At the end of the day, going forward, a new normal that has evolved from generation X is on the runway heading towards a streamlined cloud solution. User generated content in real-time will have multiple touchpoints for offshoring.',
        },
        {
          type: 'checkbox',
          label: 'Do you agree to the terms and conditions?',
        },
      ],
    },
  ],
}

export const disabledAttributes = [
  'className',
  'placeholder',
  'multiple',
  'access',
  'description',
  'name',
  'min',
  'max',
  'step',
  'accept',
]

export const controlOrder = [
  'header',
  'textarea',
  'text',
  'number',
  'date',
  'paragraph',
  'button',
  'file',
  'autocomplete',
  'select',
  'radio-group',
  'checkbox-group',
  'hidden',
  'starRating',
]

export const fields = [
  {
    label: 'Dropdown',
    attrs: {
      type: 'select',
      options: [{ label: 'Option 1' }, { label: 'Option 2' }],
    },
    icon: '▼',
    field: 'select',
  },
  {
    label: 'Paragraph',
    attrs: {
      type: 'textarea',
    },
    icon: 'P',
    field: 'select',
  },
  {
    label: 'Audio',
    attrs: {
      type: 'file',
    },
    icon: '🎧',
    accept: 'audio/*', // audio only
    field: 'audio',
    tag: '',
  },
  {
    label: 'Video',
    attrs: {
      type: 'file',
    },
    accept: 'video/*', // video only
    icon: '🎥',
    field: 'video',
    tag: '',
  },
  {
    label: 'Multiple Photos',
    attrs: {
      type: 'file',
    },
    accept: 'image/*',
    icon: '🎞️',
    field: 'image',
    tag: '',
  },
  {
    label: 'Single Photo',
    attrs: {
      type: 'file',
    },
    accept: 'image/*',
    multiple: false,
    icon: '🖼️',
    field: 'image',
    tag: '',
  },
  {
    label: 'Weather',
    attrs: {
      type: 'weather',
    },
    icon: '🌤️',
    value: '',
  },

  {
    label: 'Location',
    attrs: {
      type: 'location',
    },
    icon: '📍',
    value: 'No Location information available',
  },
]

export let templates = {
  weather: function () {
    return {
      // field: `<input type="text" id="weather" class="form-control" placeholder="Enter weather">`,
    }
  },
  location: function () {
    return {
      // field: ``,
    }
  },
}

export const userDefinedAttrs = {
  weather: {
    value: {
      label: 'Default value',
      value: 'No weather information available',
    },
  },
  location: {
    value: {
      label: 'Default value',
      value: 'No location information available',
    },
  },
  date: {
    today: {
      label: 'Today',
      value: false,
    },
    value: {
      label: false, // hides the label
      type: 'hidden', // hides the input
    },
  },
  file: {
    accept: {
      label: 'Accepted types',
      value: '', // default value (optional)
      disabled: true,
    },
    multiple: {
      label: 'Allow multiple',
      value: true,
      disabled: true, // 👈 disables editing in field editor
    },
    tag: {
      label: 'Tags',
      value: '',
    },
  },
}

export const disabledFields = ['hidden', 'button', 'select', 'paragraph', 'textarea']

export const disabledFieldButtons = {
  // location: ['edit'],
  // weather: ['edit'],
}

export const replaceFields = [
  {
    type: 'date',
    label: 'Date Field',
    // CheckBox: false,
    // value: new Date().toISOString().split('T')[0],
    attributes: {
      min: '1900-01-01',
      max: '2100-12-31',
    },
  },
]
