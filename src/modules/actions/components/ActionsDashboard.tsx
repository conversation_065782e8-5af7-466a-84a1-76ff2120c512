import { useCallback, useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { SLoader } from '../../../shared/components/loader/Loader'
import * as SharedStyled from '../../../styles/styled'
import { useSelector } from 'react-redux'
import * as Styled from '../../dashboard/style'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import Button from '../../../shared/components/button/Button'
import {
  dayjsFormat,
  extractPermissionByName,
  formatCurrency,
  formatDate,
  generateUUID,
  getDueText,
  getEnumValue,
  hasValues,
  isSuccess,
  nextAction,
  notify,
  startOfDate,
} from '../../../shared/helpers/util'
import {
  assignOppsToSalesPerson,
  getDashboardActions,
  getDashboardSalesPersonActionList,
  getDashboardSalesPersonNoActionList,
  getSalesPersonReport,
} from '../../../logic/apis/dashboard'
import { renderLoadingTable } from '../../dashboard/components/dashboardLoaders'
import { Form, Formik } from 'formik'
import { ActionsContainer } from '../style'
import { getActionMembers, getSalesOpportunity } from '../../../logic/apis/sales'
import { Types } from '../../contact/constant'

const TaskColor: Record<string, string> = {
  Task: '#02952B',
  Call: '#0112C1',
  Text: '#faa010',
  Email: '#d90202',
}

const getTagColor = (type: string) => {
  return TaskColor[type]
}

const tempId = generateUUID()
const ActionsDashboard = () => {
  const [salesData, setSalesData] = useState<{ [key: string]: any }>({})
  const [showMoreClicked, setShowMoreClicked] = useState(false)
  const [actionLimit, setActionLimit] = useState(10)
  const [contactLimit, setContactLimit] = useState(10)
  const [actionData, setActionData] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, positionDetails, positionPermissions } = globalSelector.company
  const position = positionDetails?.symbol

  const hasOppManagedFullPermission =
    hasValues(positionDetails) && extractPermissionByName(positionDetails, 'actions')?.permissions < 3

  const [salesPersonDrop, setSalesPersonDrop] = useState<any[]>([])
  const [allOppsMembers, setAllOppsMembers] = useState<any[]>([])
  const [salesPersonName, setSalesPersonName] = useState<string>('')
  const [contactPersonName, setContactPersonName] = useState('')
  const [assignSalesPerson, setAssignSalesPerson] = useState('')
  const [assignLoading, setAssignLoading] = useState(false)
  const [actionResultData, setActionResultData] = useState<any>([])
  const [noActionResultData, setNoActionResultData] = useState<any>([])
  const [contactActionData, setContactActionData] = useState<any>([])
  const [isShowNoAction, setIsShowNoAction] = useState<boolean>(false)
  const [salesDataLoading, setSalesDataLoading] = useState(false)
  const [selectedOppsIds, setSelectedOppsIds] = useState<any[]>([])
  const [salesLoading, setSalesLoading] = useState(false)
  const [contactLoading, setContactLoading] = useState(false)
  const [showMoreLoading, setShowMoreLoading] = useState(false)
  const [salesPersonOpps, setSalesPersonOpps] = useState([])

  useEffect(() => {
    if (salesPersonName || positionDetails?.symbol === 'SalesPerson' || position == 'RRTech') {
      getSalesPersonOpps()
      getActionData()
      getSalesReport()
    }
  }, [currentMember, positionDetails, salesPersonName, salesPersonDrop?.length])
  useEffect(() => {
    if (contactPersonName && salesPersonDrop?.length) {
      getDashboardSalesPersonAction()
    }
  }, [contactPersonName, salesPersonDrop?.length])

  useEffect(() => {
    if ((currentMember?.name && hasOppManagedFullPermission) || position === 'SalesPerson' || position == 'RRTech') {
      // setSalesPersonName(currentMember?.name)
      setContactPersonName(currentMember?.name)
    }
    if (position) {
      getPositions()
    }
  }, [position, currentMember?.name])

  const getSalesReport = async () => {
    setSalesDataLoading(true)
    const salesPerson =
      position !== 'SalesPerson' || position !== 'RRTech'
        ? salesPersonDrop.filter((value: any) => value.name === salesPersonName)
        : []
    try {
      const payloadId = position === 'SalesPerson' || position == 'RRTech' ? currentMember?._id : salesPerson[0]?._id
      if (payloadId) {
        const response = await getSalesPersonReport({
          salesPersonId: payloadId,
          currentDate: startOfDate(new Date()),
        })

        setSalesData(response?.data?.data?.report)
      }
    } catch (error) {
      console.log('Error', error)
    } finally {
      setSalesDataLoading(false)
    }
  }

  const getPositionMembers = async () => {
    try {
      const response = await getActionMembers({}, false)
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }
  const getTerminatedMembers = async () => {
    try {
      const response = await getActionMembers({
        hasOpportunity: true,
      })
      if (isSuccess(response)) {
        setAllOppsMembers(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('getTerminatedMembers', err)
    }
  }

  const getActionData = async () => {
    const salesPerson =
      position !== 'SalesPerson' || position !== 'RRTech'
        ? salesPersonDrop.filter((value: any) => value.name === salesPersonName)
        : []

    const payloadId = position === 'SalesPerson' || position == 'RRTech' ? currentMember?._id : salesPerson?.[0]?._id
    try {
      if (payloadId) {
        const actionRes = await getDashboardActions({
          salesPersonId: payloadId,
          endDate: startOfDate(new Date()),
        })

        setActionData(actionRes?.data?.data)
      }
    } catch (error) {
      console.error('Error=====>', error)
    }
  }

  const getSalesPersonOpps = async () => {
    const salesPersonId = allOppsMembers?.find((member: any) => member.name === salesPersonName)?._id
    try {
      setSalesLoading(true)
      const res = await getSalesOpportunity({
        salesPerson: salesPersonId,
        deleted: false,
        status: 'active',
      })

      if (isSuccess(res)) {
        setSalesPersonOpps(res?.data?.data?.opportunity)
      }
    } catch (error: any) {
      console.error('getSalesPersonOpps error', error)
      return error?.response
    } finally {
      setSalesLoading(false)
    }
  }

  const handleAssignClick = async () => {
    const filterId = salesPersonDrop.filter((value: any) => value.name === assignSalesPerson)
    try {
      setAssignLoading(true)
      const res = await assignOppsToSalesPerson(filterId[0]?._id, selectedOppsIds)
      if (isSuccess(res)) {
        notify('Opps assigned successfully!', 'success')
        getActionData()
        getSalesPersonOpps()
        getSalesReport()
      }
    } catch (error) {
      console.log('Assign Opps Error', error)
    } finally {
      setAssignLoading(false)
      setSelectedOppsIds([])
    }
  }

  const getDashboardSalesPersonAction = async (limit?: number) => {
    const filterId =
      position !== 'SalesPerson' || position !== 'RRTech'
        ? salesPersonDrop.filter((value: any) => value.name === contactPersonName)
        : []

    try {
      if (!showMoreLoading && contactLimit === 10) {
        setContactLoading(true)
      } else {
        actionResultData?.length && setShowMoreLoading(true)
      }
      const payloadId = position === 'SalesPerson' || position == 'RRTech' ? currentMember?._id : filterId[0]?._id

      if (payloadId) {
        const actionResult = await getDashboardSalesPersonActionList(payloadId, limit)
        const noActionResult = await getDashboardSalesPersonNoActionList(payloadId)
        setActionResultData(actionResult?.data?.data?.currentOpps)
        setContactActionData(actionResult?.data?.data?.contacts)
        setNoActionResultData(noActionResult?.data?.data?.currentOpps)
      }
    } catch (error) {
      console.log('Error', error)
    } finally {
      setContactLoading(false)
      setShowMoreLoading(false)
    }
  }

  const getPositions = async () => {
    try {
      getPositionMembers()
      getTerminatedMembers()
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  useEffect(() => {
    if (contactLimit > 10) {
      getDashboardSalesPersonAction(contactLimit)
    }
  }, [contactLimit])

  const initialValue = {}

  const renderTable = ({ isContact, data }: { isContact: boolean; data?: any[] }) => {
    const handlecheckAll = () => {
      if (selectedOppsIds?.length === data?.length) {
        setSelectedOppsIds([])
      } else {
        setSelectedOppsIds([...data!?.map((value: any) => value?._id)])
      }
    }

    return (
      <Styled.TableCont key={`${isContact}-${data?.length}`}>
        {/* {(isContact ? contactPersonName : salesPersonName) && ( */}
        <>
          <Styled.TableContainer>
            <Styled.TableHeading className={`action-list ${isContact ? 'contact-list' : 'sales-list'}`}>
              {/* <Styled.TableTitle>&nbsp;</Styled.TableTitle> */}
              <Styled.TableTitle
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {!isContact && data?.length ? (
                  <>
                    <SharedStyled.CheckboxZoneLabel htmlFor={tempId} padding="4px" fontSize="0px" lineHeight="0px">
                      <input
                        type="checkbox"
                        id={tempId!}
                        checked={!isContact ? selectedOppsIds?.length === data?.length : false}
                        onChange={(e) => {
                          e.stopPropagation()
                          handlecheckAll()
                        }}
                      />
                    </SharedStyled.CheckboxZoneLabel>
                    &nbsp;&nbsp;
                  </>
                ) : (
                  <>
                    {isContact ? null : (
                      <div
                        style={{
                          width: '30px',
                        }}
                      />
                    )}
                  </>
                )}
                Contact
              </Styled.TableTitle>
              <Styled.TableTitle>{isContact ? 'Action' : 'PO#'}</Styled.TableTitle>
              <Styled.TableTitle>{isContact ? 'Type' : 'Stage'}</Styled.TableTitle>
              {isContact ? <Styled.TableTitle>Due Date</Styled.TableTitle> : null}
            </Styled.TableHeading>
            {(isContact ? contactLoading : salesLoading) && <>{renderLoadingTable()}</>}
            {!(isContact ? contactLoading : salesLoading) && (isContact ? data : salesPersonOpps)?.length ? (
              data
                ?.sort((a: any, b: any) => {
                  const dateA = new Date(a?.nextAction?.due)?.getTime()
                  const dateB = new Date(b?.nextAction?.due)?.getTime()
                  return dateA - dateB
                })
                ?.map((value: any, idx: number) => (
                  <Styled.TableContent
                    as={Link}
                    to={
                      value?.type
                        ? `/contact/profile/${value?._id}`
                        : isContact
                        ? `/${getEnumValue(value?.stage?.stageGroup)}/opportunity/${value?._id}`
                        : `/${getEnumValue(value?.stageData?.[0]?.stageGroup)}/opportunity/${value?._id}`
                    }
                    className={`checkbox`}
                    key={value?._id}
                    target="_blank"
                    $bgColor={nextAction(value)}
                  >
                    <Styled.CrewReportTableContentLabel className="checkbox">
                      {hasOppManagedFullPermission && !isContact ? (
                        <SharedStyled.CheckboxZoneLabel
                          fontSize="0px"
                          padding="4px"
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation()
                          }}
                          htmlFor={value?._id}
                        >
                          <input
                            type="checkbox"
                            checked={selectedOppsIds?.includes(value?._id)}
                            id={value?._id}
                            onChange={(e) => {
                              e.stopPropagation()
                              if (selectedOppsIds?.includes(value?._id)) {
                                setSelectedOppsIds(selectedOppsIds.filter((id) => id !== value?._id))
                              } else {
                                setSelectedOppsIds([...selectedOppsIds, value?._id])
                              }
                            }}
                          />
                        </SharedStyled.CheckboxZoneLabel>
                      ) : null}
                    </Styled.CrewReportTableContentLabel>
                    <SharedStyled.TooltipContainer
                      width="260px"
                      positionLeft="4px"
                      positionBottom="0px"
                      positionLeftDecs="50%"
                      positionBottomDecs="20px"
                    >
                      <Styled.CrewReportTableContentLabel className="bold" id={`name-${idx}`}>
                        {isContact ? value?.contactId?.fullName || value?.fullName : value?.contactName}
                      </Styled.CrewReportTableContentLabel>

                      <span className="tooltip-content">
                        {isContact ? value?.contactId?.fullName || value?.fullName : value?.contactName}
                      </span>
                    </SharedStyled.TooltipContainer>
                    {isContact ? (
                      <SharedStyled.TooltipContainer
                        width="330px"
                        positionLeft="4px"
                        positionBottom="0px"
                        positionLeftDecs="50%"
                        positionBottomDecs="20px"
                      >
                        <Styled.CrewReportTableContentLabel className="fit">
                          <Styled.ActionTag color={getTagColor(value?.nextAction?.type)}>
                            {value?.nextAction?.type}
                          </Styled.ActionTag>

                          <span id={`body-${idx}`} className="action-text">
                            {value?.nextAction?.body}
                          </span>

                          {(document.getElementById(`body-${idx}`)?.offsetWidth as number) > 340 ? (
                            <span className="tooltip-content">{value?.nextAction?.body}</span>
                          ) : null}
                        </Styled.CrewReportTableContentLabel>
                      </SharedStyled.TooltipContainer>
                    ) : (
                      <Styled.CrewReportTableContentLabel className="light">
                        {value?.PO}-{value?.num}
                      </Styled.CrewReportTableContentLabel>
                    )}
                    <Styled.CrewReportTableContentLabel className="light">
                      {isContact
                        ? value?.stage?.name ||
                          `Contact: ${Object.entries(Types)?.find(([_, v]) => v === value?.type)?.[0] || ''}`
                        : value?.stageData?.[0]?.name}
                    </Styled.CrewReportTableContentLabel>
                    {isContact ? (
                      <Styled.CrewReportTableContentLabel className="light">
                        <SharedStyled.TooltipContainer
                          width="200px"
                          positionLeft="4px"
                          positionBottom="0px"
                          positionLeftDecs="50%"
                          positionBottomDecs="20px"
                          style={{
                            textTransform: 'none',
                          }}
                        >
                          <span className="tooltip-content">{getDueText(value?.nextAction?.due)}</span>

                          {dayjsFormat(value?.nextAction?.due, 'M-D-YY @ h:mma')}
                        </SharedStyled.TooltipContainer>
                      </Styled.CrewReportTableContentLabel>
                    ) : null}
                  </Styled.TableContent>
                ))
            ) : (
              <>
                {!isShowNoAction && !(isContact ? contactLoading : salesLoading) && (
                  <Styled.CrewReportTableContentLabel className="empty">
                    No data found
                  </Styled.CrewReportTableContentLabel>
                )}
              </>
            )}
            {isShowNoAction &&
              isContact &&
              noActionResultData?.map((value: any) => (
                <Styled.TableContent
                  as={Link}
                  to={`/${getEnumValue(value?.stage?.stageGroup)}/opportunity/${value?._id}`}
                  className={`checkbox`}
                  key={value?._id}
                  $bgColor={nextAction(value)}
                >
                  <Styled.CrewReportTableContentLabel className="checkbox">
                    {hasOppManagedFullPermission && !isContact && (
                      <input
                        type="checkbox"
                        checked={selectedOppsIds?.includes(value?._id)}
                        onClick={(e) => {
                          e.stopPropagation()

                          if (selectedOppsIds?.includes(value?._id)) {
                            setSelectedOppsIds(selectedOppsIds.filter((id) => id !== value?._id))
                          } else {
                            setSelectedOppsIds([...selectedOppsIds, value?._id])
                          }
                        }}
                      />
                    )}
                  </Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel>{value?.contactId?.fullName}</Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel></Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel></Styled.CrewReportTableContentLabel>
                  <Styled.CrewReportTableContentLabel className="light">
                    {value?.stage?.name}
                  </Styled.CrewReportTableContentLabel>
                </Styled.TableContent>
              ))}
          </Styled.TableContainer>
          {!isContact ? null : (
            <Styled.TableButtonWrapper>
              <Button
                width="max-content"
                onClick={() => {
                  if (isContact) {
                    setContactLimit((prev) => {
                      return prev + 50
                    })
                  }
                }}
                isLoading={showMoreLoading}
                disabled={contactLimit > data!?.length}
              >
                Show More Actions
              </Button>

              <Button
                width="max-content"
                disabled={!noActionResultData?.length || isShowNoAction}
                bgColor="#E0A800"
                onClick={() => setIsShowNoAction(true)}
              >
                Show Opps w/ No Actions
              </Button>
            </Styled.TableButtonWrapper>
          )}
        </>
        {/* // )} */}
      </Styled.TableCont>
    )
  }

  return (
    <>
      {hasValues(positionPermissions) && !positionPermissions?.['to do list dashboard'] ? (
        <p>You do not have permission to view this dashboard.</p>
      ) : (
        <ActionsContainer>
          <Formik initialValues={initialValue} onSubmit={() => {}}>
            {({ setFieldValue }) => (
              <Form>
                <Styled.DashboardTextContentWrapper>
                  {hasOppManagedFullPermission ? (
                    <Styled.DashboardDropDownContainer
                      style={{
                        margin: '0px',
                      }}
                    >
                      <Styled.SelectCont>
                        <CustomSelect
                          labelName="Select team member"
                          error={false}
                          value={contactPersonName}
                          dropDownData={salesPersonDrop?.map((val) => val.name)}
                          setValue={setContactPersonName}
                          setFieldValue={setFieldValue}
                          innerHeight="45px"
                          margin="10px 0 0 0"
                          stateName="bVentCount"
                          disabled={!salesPersonDrop?.length}
                        />
                      </Styled.SelectCont>
                    </Styled.DashboardDropDownContainer>
                  ) : null}
                </Styled.DashboardTextContentWrapper>

                {renderTable({
                  isContact: true,
                  data: [...contactActionData, ...actionResultData],
                })}

                <Styled.DashboardSubHeading
                  style={{
                    fontSize: '20px',
                  }}
                >
                  Sales Person Opportunities
                </Styled.DashboardSubHeading>

                <Styled.SelectCont>
                  <CustomSelect
                    labelName="Select Sales Person"
                    error={false}
                    value={salesPersonName}
                    dropDownData={allOppsMembers?.map((val) => val.name)}
                    setValue={setSalesPersonName}
                    setFieldValue={setFieldValue}
                    innerHeight="45px"
                    margin="10px 0 0 0"
                    stateName="bVentCount"
                    disabled={!salesPersonDrop?.length}
                  />
                </Styled.SelectCont>

                {salesPersonName ? (
                  <>
                    <Styled.DashboardTextContentWrapper className="no-margin">
                      <SharedStyled.FlexCol gap="16px" margin="0 0 10px 0">
                        {/* <Styled.DashboardHeading>Sales Action List</Styled.DashboardHeading> */}
                        <SharedStyled.FlexCol gap="2px">
                          {salesDataLoading ? (
                            <SLoader width={200} height={20} />
                          ) : (
                            <Styled.DashboardDescription>
                              Opportunities w/ Overdue Actions: {actionData?.numOverdue}
                            </Styled.DashboardDescription>
                          )}
                          {salesDataLoading ? (
                            <SLoader width={200} height={20} />
                          ) : (
                            <Styled.DashboardDescription>
                              Opportunities w/ No Action: {actionData?.numNoAction}
                            </Styled.DashboardDescription>
                          )}

                          {salesPersonName && (
                            <Styled.DashboardTextContentWrapper className="no-margin">
                              {salesDataLoading ? (
                                <SLoader width={150} height={10} />
                              ) : (
                                <Styled.DashboardDescription style={{ margin: '0 0 2px 0' }}>
                                  Active Opportunities: {salesData?.activeOppsNum}
                                </Styled.DashboardDescription>
                              )}
                              {/*
                      {salesDataLoading ? null : (
                        <Styled.DashboardDescription
                          className={salesData?.firstHalf?.opps?.length ? 'pointer' : ''}
                          onClick={() => {
                            setShowNotCompleted((p) => !p)
                          }}
                          style={{
                            pointerEvents: salesData?.firstHalf?.opps?.length ? 'auto' : 'none',
                          }}
                        >
                          Sold But Not Completed: {salesData?.firstHalf?.num}
                        </Styled.DashboardDescription>
                      )}*/}
                            </Styled.DashboardTextContentWrapper>
                          )}

                          {/*    {showNotCompleted && salesPersonName && (
                    <Styled.TableWrap>
                      <thead>
                        {notCompletedColums?.map((item) => (
                          <th key={item}>{item}</th>
                        ))}
                      </thead>
                      <tbody>
                        {salesData?.firstHalf?.opps?.map((item: any) => (
                          <tr
                            onClick={() => {
                              navigate(`/${getEnumValue(item?.stage?.stageGroup)}/opportunity/${item?._id}`)
                            }}
                            key={item?._id}
                          >
                            <td>
                              {item?.clientId?.firstName} {item?.clientId?.lastName}
                            </td>
                            <td>{dayjsFormat(item?.saleDate, 'M/D/YY')}</td>
                            <td>${formatCurrency(item?.soldValue)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Styled.TableWrap>
                  )}*/}
                        </SharedStyled.FlexCol>
                      </SharedStyled.FlexCol>
                    </Styled.DashboardTextContentWrapper>

                    {renderTable({
                      isContact: false,
                      data: salesPersonOpps,
                    })}

                    {hasOppManagedFullPermission ? (
                      <>
                        <Styled.DashboardSubHeading>Assign selected opps to:</Styled.DashboardSubHeading>
                        <Styled.DashboardDropDownContainer>
                          <Styled.SelectCont>
                            <CustomSelect
                              labelName="Select Sales Person"
                              error={false}
                              value={assignSalesPerson}
                              dropDownData={salesPersonDrop
                                ?.filter((val) => val.name !== salesPersonName)

                                ?.map((val) => val.name)}
                              setValue={setAssignSalesPerson}
                              setFieldValue={setFieldValue}
                              innerHeight="45px"
                              disabled={!salesPersonDrop?.length}
                              margin="10px 0 0 0"
                              stateName="bVentCount"
                            />
                          </Styled.SelectCont>
                        </Styled.DashboardDropDownContainer>
                        <Button
                          width="max-content"
                          disabled={!selectedOppsIds?.length || !assignSalesPerson}
                          onClick={handleAssignClick}
                          isLoading={assignLoading}
                        >
                          Assign
                        </Button>
                      </>
                    ) : null}
                  </>
                ) : (
                  <>
                    <Styled.DashboardSubHeading>Please select a sales person.</Styled.DashboardSubHeading>
                  </>
                )}
              </Form>
            )}
          </Formik>
        </ActionsContainer>
      )}
    </>
  )
}

export default ActionsDashboard
