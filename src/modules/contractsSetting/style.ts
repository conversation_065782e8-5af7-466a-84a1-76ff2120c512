import styled from 'styled-components'
import { colors, fontSizes, screenSizes } from '../../styles/theme'
import { Nue } from '../../shared/helpers/constants'
import { FlexRow } from '../../styles/styled'

export const ContractBox = styled.div`
  /* color: ${colors.text}; */
  .link {
    color: ${colors.darkBlue};
    text-decoration: underline;
    font-weight: 600;
    cursor: pointer;
  }
`
export const IconContainer = styled.div<{ disabled: boolean }>`
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 8px;
  background-color: ${colors.white};
  border: 0.5px outset ${colors.darkGrey};
  transition: all 0.3s ease 0s;

  svg {
    width: 18px;
    height: 18px;
  }

  &.delete {
    border: 0.5px outset ${({ disabled }) => (disabled ? colors.grey : colors.errorRed)};
    svg * {
      stroke: ${({ disabled }) => (disabled ? colors.grey : colors.errorRed)};
    }
  }

  &.edit {
    border: 0.5px outset ${colors.blueLight};
    svg path {
      stroke: ${colors.blueLight};
    }
  }

  &.permission {
    border: 0.5px outset ${colors.green};
    svg path {
      stroke: ${colors.green};
    }
  }

  :hover {
    transform: translateY(-4px);
  }
`
