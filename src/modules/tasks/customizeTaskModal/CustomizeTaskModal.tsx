import { Field, FieldArray, Form, Formik } from 'formik'
import React, { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../assets/icons/CrossIcon'
import ReverseSvg from '../../../assets/newIcons/reverse.svg'
import { deleteTask, getInputsApi, getTaskById, getUnitsApi, updateTask } from '../../../logic/apis/projects'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import { onlyText } from '../../../shared/helpers/regex'
import {
  convertKeyToStr,
  getNameFromId,
  getUnitIdFromSymbol,
  isSuccess,
  notify,
  getIdFromName,
  getNameFrom_Id,
  getObjectKeyByValue,
  getDataFromLocalStorage,
  getValueByKeyAndMatch,
} from '../../../shared/helpers/util'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import RadioButtonGroup from '../../../shared/radioButtonGroup/RadioButtonGroup'
import * as SharedStyled from '../../../styles/styled'
import { IType } from '../../newProject/NewProject'
import { AnyKey } from '../../opportunity/components/assessmentForm/AssessmentForm'
import { PROJECT_TYPE } from '../../units/Units'
import { TaskGroups } from '../Tasks'
import * as Styled from './style'
import UnitSvg from '../../../assets/newIcons/unitModal.svg'
import Button from '../../../shared/components/button/Button'
import Toggle from '../../../shared/toggle/Toggle'
import { InputLabelWithValidation } from '../../../shared/inputLabelWithValidation/InputLabelWithValidation'
import { ProjectTypeGroups } from '../../projectTypes/components/projectTypeModal/constant'
import AutoComplete from '../../../shared/autoComplete/AutoComplete'
import { StorageKey } from '../../../shared/helpers/constants'
import { SLoader } from '../../../shared/components/loader/Loader'
import AutoCompleteIndentation from '../../../shared/autoCompleteIndentation/AutoCompleteIndentation'

export interface IFullUnit {
  _id: string
  name: string
  symbol: string
  createdBy: string
}

interface INewTaskModal {
  onClose: () => void
  isEditing?: boolean
  inputData?: any
  taskAutofill?: Record<string, string>
  handleUnitModal: () => void
  handleMaterialModal: (tempMaterialName: string) => void
  handleInputModal: (val: string) => void
  // units?: IFullUnit[]
  setProjectType: React.Dispatch<React.SetStateAction<string>>
  onComplete: () => void
  // inputTableValues?: any
  materialForUpdate?: any
  workerValues?: any
  types: IType[]
  handleAutoPick?: any
  // setCustomizeTaskModal: React.Dispatch<React.SetStateAction<boolean>>
}
interface I_InputObject {
  iNum: string
  input: string
  oper: string
  name: string
}
interface I_MaterialObject {
  tMat: string
  mat: string
  cov: string
  name: string
  customValue: string
  switch: boolean
}
interface I_LaborObject {
  tLabor: string
  worker: string
  time: number
  mod: string
  name: string
}
interface InitialValues {
  type: string
  name: string
  taskDescription: string
  unit: string
  materialWaste: number
  unitName: string
  group: string
  order: number
  // worker: string
  // time: string
  // modifiedBy: string
  input: I_InputObject[]
  material: I_MaterialObject[]
  labor: I_LaborObject[]
  createdBy: string | undefined
  taskId: string
  active?: boolean
}

const TYPES = ['roof-replacement', 'roof-repair']

export const ProfileSchema = Yup.object().shape({
  name: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').required('Required'),
  // .matches(onlyText, 'Enter Valid Name'),
  taskDescription: Yup.string().required('Required'),
  unit: Yup.string(),
  materialName: Yup.string(),
  coverageUnit: Yup.string(),
  materialWaste: Yup.number().max(100, 'Percentage cannot exceed 100%').required('Required'),
  worker: Yup.string(),
  time: Yup.string(),
  modifiedBy: Yup.string(),
  active: Yup.boolean(),
  type: Yup.string().required('Required'),
})

export const modifiedByValues: { [key: string]: string } = {
  none: 'Nothing',
  rmvMod: 'Remove',
  rmvAddMod: "Remove Add'l Layers",
  plyMod: 'Plywood',
  instMod: 'Install',
  pitchMod: 'Pitch',
}

const CustomizeTaskModal: React.FC<INewTaskModal> = (props) => {
  const {
    onClose,
    handleUnitModal,
    isEditing,
    inputData,
    // units,
    handleInputModal,
    onComplete,
    setProjectType,
    // inputTableValues,
    taskAutofill,
    handleMaterialModal,
    materialForUpdate,
    workerValues,
    types,
    handleAutoPick,
  } = props

  const [selectedType, setSelectedType] = useState(inputData?.type)
  // const [types, setTypes] = useState<IType[]>([])
  // const [measurement, setMeasurement] = useState(inputData ? inputData.unitName : '')

  const [selectedIndex, setSelectedIndex] = useState<null | number>(null)

  const [unitDropdownData, setUnitDropdownData] = useState<any>([])
  //   const [units, setUnits] = useState<IFullUnit[]>([])
  const [inputDropdownData, setInputDropdownData] = useState<any>([])
  const [calculated, setCalculated] = useState('')
  const [calculatedBy, setCalculatedBy] = useState('')
  const [materialsDropdownData, setMaterialsDropdownData] = useState<any>([])
  const [materialName, setMaterialName] = useState('')
  const [isRoofRepair, setIsRoofRepair] = useState(false)
  const [typeGroups, setTypeGroups] = useState<ProjectTypeGroups[]>([])
  // const [workerValues, setWorkerValues] = useState([])
  const [worker, setWorker] = useState('')
  const [modifiedByDropdown, setModifiedByDropdown] = useState<any>([])
  const [modifiedBy, setModifiedBy] = useState(modifiedByValues[inputData?.labor?.[0]?.mod] || '')
  const [unitsDrop, setUnitsDrop] = useState<AnyKey>({})
  const [inputsDrop, setinputsDrop] = useState([])
  const [materialDrop, setMaterialDrop] = useState([])
  const [workerDrop, setWorkerDrop] = useState([])
  const [unitTableValues, setUnitTableValues] = useState([])
  const [units, setUnits] = useState<IFullUnit[]>([])
  const [inputTableValues, setInputTableValues] = useState([])
  const [inputs, setInputs] = useState<IFullUnit[]>([])
  const [group, setGroup] = useState('')
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [submitLoading, setSubmitLoading] = useState(false)
  const [tempMaterialName, setTempMaterialName] = useState('')
  const [tempInputName, setTempInputName] = useState('')
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const [taskLoading, setTaskLoading] = useState(false)

  const EditTaskSchema = Yup.object().shape({
    name: Yup.string().min(2, 'Too Short!').max(100, 'Too Long!').required('Required'),
    // .matches(onlyText, 'Enter Valid Name'),
    taskDescription: Yup.string().required('Required'),
    unit: Yup.string(),
    materialName: Yup.string(),
    coverageUnit: Yup.string(),
    materialWaste: Yup.number().max(100, 'Percentage cannot exceed 100%').required('Required'),
    worker: Yup.string(),
    time: Yup.string(),
    modifiedBy: Yup.string(),
    active: Yup.boolean(),
    input: Yup.array().of(
      Yup.object().shape({
        name: Yup.string().required('Required'),
        oper: Yup.string().required('Required'),
      })
    ),
    material: Yup.array().of(
      Yup.object().shape({
        name: Yup.string(),
        cov: Yup.string().when('name', {
          is: (name: string) => !!name, // Check if name has a value
          then: Yup.string().required('Required'),
        }),
      })
    ),
    group: isRoofRepair ? Yup.string() : Yup.string().required('Required'),
  })
  const [initialValues, setInitialValues] = useState<InitialValues>({
    type: '',
    name: '',
    taskDescription: '',
    unit: '',
    materialWaste: 0,
    unitName: '',
    group: '',
    order: '',
    active: false,
    // worker: '',
    // time: '',
    // modifiedBy: '',
    labor: [
      {
        tLabor: '',
        worker: '',
        time: 0,
        mod: '',
        name: '',
      },
    ],
    input: [
      {
        iNum: '',
        input: '',
        oper: '+',
        name: '',
      },
    ],
    material: [
      // {
      //   tMat: '',
      //   mat: '',
      //   cov: '',
      //   name: '',
      //   isSwitched: false,
      // },
    ],
    createdBy: '',
    taskId: '',
  })

  //   useEffect(() => {
  //     const filterWithNameAndSymbol = units.map((item: any) => `${item.symbol} (${item.name})`)
  //     filterWithNameAndSymbol.push('--Add New--')
  //     setUnitDropdownData(filterWithNameAndSymbol)
  //   }, [units])

  // const initFetch = () => {
  //   TYPES.forEach((type) => {
  //     setTypes((prev) => {
  //       prev.push({
  //         id: type,
  //         label: convertKeyToStr(type),
  //         name: type,
  //         value: type,
  //       })
  //       return prev
  //     })
  //   })
  // }
  // useEffect(() => {
  //   initFetch()
  // }, [])

  useEffect(() => {
    if (inputData?._id !== '') {
      fetchTaskByIdApi()
    }
  }, [inputData?._id])

  const fetchTaskByIdApi = async () => {
    try {
      setTaskLoading(true)
      const res = await getTaskById({
        taskId: inputData?._id,
        deleted: false,
      })
      if (isSuccess(res)) {
        const { task } = res?.data?.data
        setProjectType(getNameFromId(task?.type, types))
        setInitialValues({
          type: task?.type,
          name: task?.name,
          taskDescription: task?.description,
          unit: task?.unit,
          materialWaste: Math.round((task?.waste - 1) * 100),
          unitName: task?.unitName,
          order: task?.order,
          active: !!task?.active,
          group: '',
          labor: task?.labor,
          input: task?.input,
          material: task?.material?.map((itm) => itm?.cov)?.filter(Boolean)?.length
            ? task?.material?.map((itm: any, index: number) => ({
                ...itm,
                name: `${getValueByKeyAndMatch('name', itm?.mat, '_id', materialForUpdate)} ($${
                  getValueByKeyAndMatch('cost', itm?.mat, '_id', materialForUpdate) || '--'
                }/${getValueByKeyAndMatch('unitName', itm?.mat, '_id', materialForUpdate)})`,
                customValue: itm?.switch ? Number(Number(1 / itm?.cov)?.toFixed(6)) : '',
                switch: itm?.switch ?? false,
              }))
            : [],
          createdBy: task?.createdBy,
          taskId: '',
        })
        handleAutoPick?.setMeasurement(task?.unitName)
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('init fetch failed!', error)
    } finally {
      setTaskLoading(false)
    }
  }

  useEffect(() => {
    fetchUnit()
  }, [])

  const fetchUnit = async () => {
    fetchUnitsData()
  }

  useEffect(() => {
    if (units?.length) {
      fetchInputsData()
    }
  }, [units])

  useEffect(() => {
    const filterWithNameAndSymbol = units.map((item: any) => `${item.symbol} (${item.name})`)
    filterWithNameAndSymbol.push('--Add New--')
    setUnitDropdownData(filterWithNameAndSymbol)
  }, [units])

  const fetchUnitsData = async () => {
    try {
      const res = await getUnitsApi({ deleted: false, limit: 1000 })
      if (isSuccess(res)) {
        const { unit } = res.data.data
        const tableData = unit.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur.name,
              symbol: cur.symbol,
            },
          ]
        }, [])
        setUnits(unit)
        setUnitTableValues(tableData)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.error('Failed init fetch', err)
    }
  }

  useEffect(() => {
    if (units && units.length) {
      let localUnits: AnyKey = {}
      units?.forEach((unit) => {
        localUnits[`${unit.symbol} (${unit.name})`] = unit
      })
      if (Object.keys(localUnits)) setUnitsDrop(localUnits)
    }
  }, [units])

  useEffect(() => {
    fetchInputsData()
  }, [handleAutoPick?.measurement, selectedType])

  const fetchInputsData = async () => {
    try {
      const res = await getInputsApi({
        deleted: false,
        unit: getUnitIdFromSymbol(handleAutoPick?.measurement.split(' ')[0], units),
        type: selectedType,
      })
      if (isSuccess(res)) {
        const { input } = res.data.data
        const tableData = input.reduce((prev: any, cur: any) => {
          return [
            ...prev,
            {
              ...cur,
              name: cur?.name,
              unit: cur?.unit,
              order: cur?.orderNumber,
              type: convertKeyToStr(PROJECT_TYPE[cur?.projectType]),
            },
          ]
        }, [])
        setInputs(input)
        setInputTableValues(tableData)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.error('Failed init fetch', err)
    }
  }

  // useEffect(() => {
  //   if (calculated === '--Add New--') {
  //     setNewInputModal(true)
  //   }
  // }, [calculated])

  useEffect(() => {
    const filterWithName = inputTableValues.map((item: any) => `${item.name} :-${item._id}`)
    // filterWithName.push('--Add New--')
    setInputDropdownData(filterWithName)
  }, [inputTableValues])

  useEffect(() => {
    // const filterWithName = materialForUpdate.map(
    //   (item: any) => `${item.name} ($${item?.cost}/${item?.unitName}) :-${item._id}`
    // )

    const filterWithName: any = {}
    materialForUpdate.forEach((item) => {
      if (!filterWithName[item.categoryName]) {
        filterWithName[item.categoryName] = {}
      }

      if (!filterWithName[item.categoryName][item.subCategoryName]) {
        filterWithName[item.categoryName][item.subCategoryName] = []
      }

      filterWithName[item.categoryName][item.subCategoryName].push(`${item.name} ($${item?.cost}/${item?.unitName})`)
    })
    setMaterialsDropdownData(filterWithName)
  }, [materialForUpdate])
  useEffect(() => {
    setWorkerDrop(workerValues)
  }, [workerValues])
  useEffect(() => {
    const isRoofReplacement = types?.find((item) => item?.id === selectedType)?.typeReplacement
    const isRoofRepairLocal = !types?.find((item) => item?.id === selectedType)?.typeReplacement
    const isUsesPitch = types?.find((item) => item?.id === selectedType)?.usesPitch

    if (isRoofRepairLocal) {
      setIsRoofRepair(true)
    } else {
      setIsRoofRepair(false)
    }

    if (isRoofReplacement) {
      setModifiedByDropdown(['Nothing', 'Remove', "Remove Add'l Layers", 'Plywood', 'Install'])
    } else {
      setModifiedByDropdown(isUsesPitch ? ['Nothing', 'Pitch'] : ['Nothing'])
      // setModifiedBy('Nothing')
    }
  }, [selectedType])

  useEffect(() => {
    if (materialName === '--Add New--') {
      handleMaterialModal(tempMaterialName)
    }
  }, [materialName])

  useEffect(() => {
    if (calculated === '--Add New--') {
      handleInputModal(tempInputName)
    }
  }, [calculated])

  const handleSubmit = async (submittedValues: InitialValues) => {
    try {
      setSubmitLoading(true)
      // const groupById = getIdFromName(submittedValues?.group, typeGroups)
      // const typeId = getIdFromName(submittedValues?.type, types)
      submittedValues.labor[0].tLabor = 'LaborId1'
      // submittedValues.labor[0].worker = workerDrop
      //   ?.find((item: string) => item.split(' :-')[0]?.trim() === worker?.trim())
      //   ?.split(' :-')?.[1]

      // submittedValues.type = typeId
      submittedValues.labor[0].mod = getObjectKeyByValue(modifiedByValues, modifiedBy || 'instMod')!

      submittedValues.unit = getUnitIdFromSymbol(submittedValues.unitName.split(' ')[0], units)
      for (let i = 0; i < submittedValues.input.length; i++) {
        submittedValues.input[i].iNum = `InputId${i + 1}`
      }
      for (let m = 0; m < submittedValues.material.length; m++) {
        submittedValues.material[m].tMat = `MatId${m + 1}`
        // submittedValues.material[m].cov = submittedValues?.material?.[m]?.isSwitched
        //   ? submittedValues?.material?.[m]?.customValue
        //   : submittedValues?.material?.[m]?.cov

        submittedValues.material[m].cov = submittedValues?.material?.[m]?.switch
          ? Number(Number(1 / submittedValues?.material?.[m]?.customValue)?.toFixed(6))
          : submittedValues?.material?.[m]?.cov
      }

      if (submittedValues.input[0].iNum === 'InputId1') submittedValues.input[0].oper = '+'
      // submittedValues.group = groupById ? groupById : undefined
      submittedValues.createdBy = currentMember._id
      submittedValues.taskId = inputData._id

      submittedValues.material = submittedValues?.material?.map((itm) => {
        const { customValue, ...rest } = itm
        return rest
      })

      const { materialWaste, taskDescription, ...payload } = submittedValues

      const res = await updateTask({
        ...payload,
        waste: materialWaste,
        description: taskDescription,

        group: getIdFromName(submittedValues?.group, typeGroups) ?? undefined,
      })
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        onComplete()
        onClose()
      }
    } catch (err) {
      notify('Failed to update task!', 'error')
      console.error('Submit error', err)
    } finally {
      setSubmitLoading(false)
      setSelectedIndex(null)
    }
  }
  const handleDelete = async () => {
    try {
      setDeleteLoading(true)
      const res = await deleteTask({ id: inputData._id })
      if (isSuccess(res)) {
        notify('Task deleted successfully!', 'success')
        onComplete()
        onClose()
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.error('Submit error', err)
    } finally {
      setDeleteLoading(false)
    }
  }

  return (
    <Styled.ModalContainer>
      <Styled.ModalHeaderContainer>
        <SharedStyled.FlexRow>
          <img src={UnitSvg} alt="modal icon" />
          <SharedStyled.FlexCol>
            <Styled.ModalHeader>{isEditing ? `Edit task` : 'Add Task'}</Styled.ModalHeader>
          </SharedStyled.FlexCol>
        </SharedStyled.FlexRow>
        <Styled.CrossContainer
          onClick={() => {
            onClose()
          }}
        >
          <CrossIcon />
        </Styled.CrossContainer>
      </Styled.ModalHeaderContainer>
      {taskLoading ? (
        <Styled.ModalHeaderContainer>
          <SharedStyled.FlexCol gap="25px">
            <SLoader height={45} width={100} isPercent />
            <SLoader height={15} width={100} isPercent />
            <SLoader height={45} width={100} isPercent />
            <SLoader height={45} width={100} isPercent />
            <SLoader height={45} width={100} isPercent />
            <SLoader height={45} width={100} isPercent />
            <SLoader height={15} width={100} isPercent />
            <SLoader height={85} width={100} isPercent />
          </SharedStyled.FlexCol>
        </Styled.ModalHeaderContainer>
      ) : (
        <Formik
          initialValues={{ ...initialValues }}
          onSubmit={handleSubmit}
          validationSchema={EditTaskSchema}
          enableReinitialize={true}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ values, errors, touched, setFieldValue }) => {
            const testId = (id: string, name: string, index: number | undefined) => {}

            const scrollToFirstError = () => {
              const errorFields = Object.keys(errors) // Get fields with errors

              const firstErrorOrEmptyField = errorFields.find((field: any) => errors[field])
              if (firstErrorOrEmptyField) {
                const element = document.querySelector(`[name="${firstErrorOrEmptyField}"]`)
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth', block: 'center' })
                }
              }
            }

            useEffect(() => {
              if (Object.entries(taskAutofill).length) {
                setFieldValue(`material[${selectedIndex}].name`, taskAutofill?.name)
                setFieldValue(`material[${selectedIndex}].mat`, taskAutofill?.mat)
              }
            }, [taskAutofill])

            useEffect(() => {
              // Reset the inputs when the measurement changes

              if (inputData.unitName !== handleAutoPick?.measurement)
                setFieldValue('input', [
                  {
                    iNum: '',
                    input: '',
                    oper: '+',
                    name: '',
                  },
                ])
            }, [handleAutoPick?.measurement])

            useEffect(() => {
              if (values?.type !== '') {
                const typeObject: any = types?.find((v) => v.name === getNameFromId(values.type, types))
                if (typeObject?.groups?.length) {
                  setTypeGroups([...(typeObject?.groups || [])])
                  setInitialValues((pre) => ({ ...pre, group: getNameFrom_Id(inputData.group, typeObject?.groups) }))
                } else {
                  setTypeGroups([])
                }
              }
            }, [values?.type])

            useEffect(() => {
              if (Object?.entries?.(handleAutoPick?.addInputResponse || {})?.length) {
                setFieldValue(`input.${handleAutoPick?.addInputIndex}.input`, handleAutoPick?.addInputResponse._id)
                setFieldValue(`input.${handleAutoPick?.addInputIndex}.name`, handleAutoPick?.addInputResponse.name)
              }
            }, [handleAutoPick?.addInputResponse])

            useEffect(() => {
              if (Object?.entries?.(handleAutoPick?.addMaterialResponse || {})?.length) {
                setFieldValue(
                  `material.${handleAutoPick?.addMaterialIndex}.mat`,
                  handleAutoPick?.addMaterialResponse._id
                )
                setFieldValue(
                  `material.${handleAutoPick?.addMaterialIndex}.name`,
                  `${handleAutoPick?.addMaterialResponse.name} ($${
                    handleAutoPick?.addMaterialResponse?.cost
                  }/${getValueByKeyAndMatch('name', handleAutoPick?.addMaterialResponse?.unitId, '_id', units)})`
                )
              }
            }, [handleAutoPick?.addMaterialResponse])

            return (
              <Form className="form taskForm">
                {/* <SharedStyled.FlexBox flexDirection="column">
                <SharedStyled.Text fontSize="14px" fontWeight="medium">
                  Project Type:
                </SharedStyled.Text>
                <RadioButtonGroup
                  onChange={(e) => onTypeChange(e.target.value)}
                  radioButtons={types}
                  selectedType={selectedType}
                />
              </SharedStyled.FlexBox> */}

                <CustomSelect
                  labelName="Project Type"
                  error={!!errors?.type}
                  value={getNameFromId(values.type, types)}
                  dropDownData={types?.map((v) => v.name)}
                  setValue={() => onTypeChange(values.type)}
                  setFieldValue={setFieldValue}
                  innerHeight="45px"
                  margin="10px 0 0 0"
                  stateName="type"
                  disabled={true}
                />
                <Styled.InfoContainer>
                  {/* <SharedStyled.Text fontSize="14px" fontWeight="medium">
                  Name the Task: <SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                </SharedStyled.Text> */}

                  <InputWithValidation
                    labelName="Task Name"
                    stateName="name"
                    error={touched.name && errors.name ? true : false}
                    twoInput={true}
                  />
                  <div className="margin">
                    <SharedStyled.Text fontSize="14px" fontWeight="medium">
                      Description (seen on the contract page):
                    </SharedStyled.Text>
                  </div>
                  <Styled.TextArea
                    component="textarea"
                    as={Field}
                    name="taskDescription"
                    marginTop="8px"
                    height="auto"
                    // placeHolder="ie. Remove existing shingle,flashing, and underlayment; then install new ice shield, flashing, and shingle to match"
                    error={errors.taskDescription ? true : false}
                  />
                  {errors?.taskDescription && <SharedStyled.ErrorMsg>{errors?.taskDescription}</SharedStyled.ErrorMsg>}

                  <InputWithValidation
                    labelName="Work Order"
                    stateName="order"
                    forceType="number"
                    error={touched.order && errors.order ? true : false}
                  />

                  <SharedStyled.FlexRow margin="16px 0 16px 0">
                    <SharedStyled.Text fontSize="14px" fontWeight="500" className="bold-text">
                      How is it measured?
                    </SharedStyled.Text>
                  </SharedStyled.FlexRow>

                  {/* <SharedStyled.Text fontSize="16px" fontWeight="medium">
                  Unit of measurement: <SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                </SharedStyled.Text> */}
                  <CustomSelect
                    labelName="Unit of measurement"
                    error={false}
                    value={values.unitName}
                    dropDownData={Object.keys(unitsDrop)}
                    setValue={handleAutoPick?.setMeasurement}
                    setFieldValue={setFieldValue}
                    innerHeight="45px"
                    margin="10px 0 0 0"
                    showAddOption
                    stateName="unitName"
                    onAddClick={handleUnitModal}
                  />

                  <SharedStyled.FlexCol alignItems="stretch" margin="20px 0 0 0">
                    <SharedStyled.FlexRow justifyContent="space-between">
                      <SharedStyled.Text fontSize="14px" fontWeight="medium">
                        How is it calculated? (only inputs with the same unit are shown){' '}
                      </SharedStyled.Text>
                      <SharedStyled.Text fontSize="14px" fontWeight="medium">
                        calculated by
                      </SharedStyled.Text>
                    </SharedStyled.FlexRow>

                    <FieldArray name="input">
                      {({ insert, remove, push }) => (
                        <>
                          {values.input.length > 0 &&
                            values.input.map((input: any, index: number) => (
                              <Styled.SaperatorDiv key={index}>
                                <div>
                                  {/* <CustomSelect
                                  labelName="Pick an input"
                                  error={false}
                                  value={
                                    inputDropdownData
                                      ?.find(
                                        (item: any) =>
                                          item.split(' :-')[1]?.trim() === values.input[index].input?.trim()
                                      )
                                      ?.split(' :-')[0]
                                      ?.trim() ?? ''
                                  }
                                  dropDownData={inputDropdownData.map((item: any) => item.split(' :-')[0]?.trim())}
                                  setValue={setCalculated}
                                  setFieldValue={setFieldValue}
                                  testId={testId}
                                  dropDownDataForId={inputDropdownData}
                                  innerHeight="45px"
                                  margin="10px 0 0 0"
                                  stateName={`input.${index}.name`}
                                  selectedIdName={`input.${index}.input`}
                                  onAddClick={handleInputModal}
                                  showInitialValue
                                /> */}

                                  <AutoComplete
                                    labelName="Pick an input"
                                    stateName={`input.${index}.name`}
                                    error={touched.input?.[index]?.name && errors.input?.[index]?.name ? true : false}
                                    setFieldValue={setFieldValue}
                                    options={inputDropdownData.map((item: any) => item.split(' :-')[0]?.trim())}
                                    value={values?.input?.[index]?.name}
                                    showAddOption
                                    autoFillData={''}
                                    onAddClick={(val) => {
                                      setFieldValue(`input.${index}.name`, val)
                                      setCalculated('--Add New--')
                                      handleAutoPick && handleAutoPick.setAutoInputName(val)
                                      handleAutoPick?.setAddInputIndex(index)
                                      setTempInputName(val)
                                    }}
                                    // borderRadius="0px"
                                    className="clientHeight"
                                    setValueOnClick={(val: string) => {
                                      setCalculated(val)
                                      setFieldValue(
                                        `input.${index}.input`,
                                        inputDropdownData
                                          ?.find((item: any) => item.split(' :-')[0]?.trim() === val?.trim())
                                          ?.split(' :-')[1]
                                      )
                                      setFieldValue(`input.${index}.name`, val)
                                    }}
                                    // className="material-autocomplete"
                                  />
                                </div>
                                <Styled.Gap className="margin-left">
                                  <CustomSelect
                                    labelName=""
                                    error={false}
                                    value={values.input[index].oper}
                                    dropDownData={['+', '-', 'x', '/']}
                                    setValue={setCalculatedBy}
                                    setFieldValue={setFieldValue}
                                    innerHeight="45px"
                                    // margin="10px 0 0 0"
                                    stateName={`input.${index}.oper`}
                                  />
                                </Styled.Gap>

                                {values.input.length > 1 && (
                                  <div style={{ margin: '8px 0 0 10px' }}>
                                    <Button
                                      padding="8px 15px"
                                      className="fit delete"
                                      type="button"
                                      onClick={() => remove(index)}
                                    >
                                      X
                                    </Button>
                                  </div>
                                )}
                              </Styled.SaperatorDiv>
                            ))}
                          <SharedStyled.ButtonContainer justifyContent="start" marginTop="10px">
                            <Button
                              className="fit"
                              type="button"
                              onClick={() => push({ iNum: '', input: '', oper: '+', name: '' })}
                            >
                              Add Input
                            </Button>
                            &emsp;
                            {/* <Button
                              className="fit delete"
                              type="button"
                              onClick={() => {
                                values.input.length > 1 ? remove(values.input.length - 1) : null
                              }}
                            >
                              Remove Input
                            </Button> */}
                          </SharedStyled.ButtonContainer>
                        </>
                      )}
                    </FieldArray>
                  </SharedStyled.FlexCol>
                  <div className="margin">
                    <SharedStyled.Text margin="12px 0 0 0" fontSize="14px" fontWeight="500" className="bold-text">
                      Materials Needed
                    </SharedStyled.Text>
                  </div>

                  <FieldArray name="material">
                    {({ insert, remove, push }) => (
                      <>
                        {values.material.length > 0 &&
                          values.material.map((input: any, index: number) => (
                            <Styled.SaperatorDiv key={index}>
                              <Styled.SapceWidth>
                                {/* <CustomSelect
                                labelName="Material Name"
                                error={false}
                                value={
                                  materialDrop
                                    .find(
                                      (item: any) => item.split(' :-')[1]?.trim() === values.material[index].mat?.trim()
                                    )
                                    ?.split(' :-')[0]
                                    ?.trim() ?? ''
                                }
                                dropDownData={materialDrop.map((item: any) => item.split(' :-')[0]?.trim())}
                                setValue={setMaterialName}
                                setFieldValue={setFieldValue}
                                dropDownDataForId={materialDrop}
                                testId={testId}
                                innerHeight="45px"
                                margin="10px 0 0 0"
                                stateName={`material.${index}.name`}
                                selectedIdName={`material.${index}.mat`}
                                showInitialValue
                              /> */}

                                <AutoCompleteIndentation
                                  labelName="Material Name"
                                  stateName={`material.${index}.name`}
                                  dropdownHeight="300px"
                                  error={
                                    touched.material?.[index]?.name && errors.material?.[index]?.name ? true : false
                                  }
                                  setFieldValue={setFieldValue}
                                  options={
                                    Object.values(materialsDropdownData)
                                      .flatMap((category) => Object.values(category))
                                      .flat()
                                      ?.filter(
                                        (name: string) => !values?.material?.some((obj) => obj.name === name)
                                      ) as string[]
                                  }
                                  formatedOptions={materialsDropdownData}
                                  value={values?.material?.[index]?.name}
                                  showAddOption
                                  onAddClick={(val) => {
                                    setSelectedIndex(index)
                                    handleAutoPick && handleAutoPick.setAutoMaterialName(val)
                                    setFieldValue(`material.${index}.name`, val)
                                    handleAutoPick?.setAddMaterialIndex(index)
                                    setMaterialName('--Add New--')
                                    setTempMaterialName(val)
                                  }}
                                  borderRadius="0px"
                                  setValueOnClick={(val: string) => {
                                    setMaterialName(val)
                                    setFieldValue(
                                      `material.${index}.mat`,
                                      getValueByKeyAndMatch(
                                        '_id',
                                        val.split('($')[0]?.trim(),
                                        `name`,
                                        materialForUpdate
                                      )
                                    )
                                  }}
                                  className="material-autocomplete"
                                  isIndentation={true}
                                />
                              </Styled.SapceWidth>
                              &emsp;
                              <Styled.SapceWidth>
                                <InputWithValidation
                                  value={
                                    values?.material?.[index]?.switch
                                      ? values.material[index].customValue
                                      : values.material[index].cov
                                  }
                                  labelName={
                                    values.material[index].mat
                                      ? `1 ${
                                          values?.material?.[index]?.switch
                                            ? unitsDrop[values?.unitName]?.name
                                            : materialForUpdate?.find(
                                                (itm: any) => itm?._id === values.material[index].mat
                                              )?.unitName
                                        } ${values?.material?.[index]?.switch ? 'uses' : 'covers'} how many ${
                                          values?.material?.[index]?.switch
                                            ? materialForUpdate?.find(
                                                (itm: any) => itm?._id === values.material[index].mat
                                              )?.unitName
                                            : unitsDrop[values?.unitName]?.name
                                        }?`
                                      : 'Please select material'
                                  }
                                  stateName={
                                    values?.material?.[index]?.switch
                                      ? `material.${index}.customValue`
                                      : `material.${index}.cov`
                                  }
                                  error={touched.material?.[index]?.cov && errors.material?.[index]?.cov ? true : false}
                                  forceType="number"
                                  twoInput={true}
                                />
                              </Styled.SapceWidth>
                              &emsp;
                              <SharedStyled.FlexRow
                                width="max-content"
                                margin="8px 0 0 0"
                                alignItems="center"
                                style={{ flex: 0 }}
                              >
                                <Button
                                  style={{ display: 'flex' }}
                                  type="button"
                                  padding="6px 8px"
                                  width="max-content"
                                  bgColor={values?.material?.[index]?.switch ? '#1aa41a' : '#E9ECEF'}
                                  onClick={() => {
                                    setFieldValue(`material.${index}.switch`, !values.material[index].switch)
                                    // if (!Number.isInteger(values?.material?.[index]?.cov)) {

                                    if (values?.material?.[index]?.customValue) {
                                      setFieldValue(
                                        `material.${index}.cov`,
                                        values?.material?.[index]?.cov
                                          ? values?.material?.[index]?.cov
                                          : Number(Number(1 / values?.material?.[index]?.customValue)?.toFixed(6))
                                      )
                                    } else {
                                      setFieldValue(
                                        `material.${index}.customValue`,

                                        values?.material?.[index]?.customValue
                                          ? values?.material?.[index]?.customValue
                                          : Number(Number(1 / values?.material?.[index]?.cov)?.toFixed(6))
                                      )
                                    }

                                    // setFieldValue(
                                    //   `material.${index}.customValue`,

                                    //   !Number.isInteger(values?.material?.[index]?.customValue) &&
                                    //     values?.material?.[index]?.customValue
                                    //     ? Math.round(values?.material?.[index]?.customValue)
                                    //     : Number(Number(1 / values?.material?.[index]?.cov)?.toFixed(6))
                                    // )
                                    // }
                                  }}
                                >
                                  <img src={ReverseSvg} alt="switch-icon" />
                                </Button>

                                {values?.material?.length > 1 && (
                                  <div style={{ margin: '0 0 0 10px' }}>
                                    <Button
                                      padding="8px 15px"
                                      className="fit delete"
                                      type="button"
                                      onClick={() => remove(index)}
                                    >
                                      X
                                    </Button>
                                  </div>
                                )}
                              </SharedStyled.FlexRow>
                            </Styled.SaperatorDiv>
                          ))}
                        <SharedStyled.ButtonContainer justifyContent="start" marginTop="10px">
                          <Button
                            className="fit"
                            type="button"
                            marginTop="10px"
                            onClick={() => push({ tMat: '', mat: '', cov: '', name: '' })}
                          >
                            Add Material
                          </Button>
                          &emsp;
                          {/* {values?.material?.map((itm) => itm?.cov)?.filter(Boolean)?.length ? (
                            <Button
                              className="fit delete"
                              type="button"
                              onClick={() => (values.material.length > 1 ? remove(values.material.length - 1) : null)}
                            >
                              Remove Material
                            </Button>
                          ) : null} */}
                        </SharedStyled.ButtonContainer>
                      </>
                    )}
                  </FieldArray>

                  {/* <div className="margin">
                  <SharedStyled.Text fontSize="14px" fontWeight="medium">
                    Add Group: <SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                  </SharedStyled.Text>
                </div> */}

                  {/* <div className="margin">
                  <SharedStyled.Text fontSize="14px" fontWeight="500">
                    Materials Waste
                  </SharedStyled.Text>
                </div> */}

                  {/* <div className="margin"> */}
                  {/* <SharedStyled.Text fontSize="14px" fontWeight="medium">
                    Material Waste (%): <SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                  </SharedStyled.Text> */}
                  <Styled.NameValueUnitContainer width={'100%'}>
                    {/* <Styled.PercentValueInput
                      width={'100%'}
                      name="waste"
                      type="number"
                      placeholder="Percent waste"
                      borderRadius="4px 0px 0px 4px"
                    />
                    <Styled.UnitDiv>%</Styled.UnitDiv> */}
                    <InputLabelWithValidation
                      disabled={
                        values?.material?.length === 1 &&
                        (values.material[0].name === '' || values.material[0].cov === '')
                      }
                      stateName={`materialWaste`}
                      labelName="Material Waste (%)"
                      error={!!errors.materialWaste}
                      labelUnit={'%'}
                      inputType="number"
                      value={String(values.materialWaste)}
                    />
                  </Styled.NameValueUnitContainer>
                  {/* </div> */}

                  <div className="margin">
                    <SharedStyled.Text fontSize="14px" fontWeight="500" className="bold-text">
                      Labor Required
                    </SharedStyled.Text>
                  </div>

                  <FieldArray name="labor">
                    {({ insert, remove, push }) => (
                      <>
                        {values.labor.length > 0 &&
                          values.labor.map((data: any, index: number) => (
                            <Styled.SaperatorDiv key={index}>
                              <Styled.SapceWidth>
                                <CustomSelect
                                  labelName="Worker:"
                                  error={false}
                                  value={(
                                    workerDrop.find(
                                      (item: any) => item.split(':-')[1]?.trim() === values?.labor?.[index]?.worker
                                    ) as unknown as string
                                  )
                                    ?.split(':-')[0]
                                    ?.trim()}
                                  dropDownData={workerDrop.map((item: any) => item.split(':-')[0]?.trim())}
                                  setValue={() => {}}
                                  setFieldValue={setFieldValue}
                                  testId={testId}
                                  dropDownDataForId={workerDrop}
                                  innerHeight="45px"
                                  margin="10px 0 0 0"
                                  stateName={`labor.${index}.name`}
                                  selectedIdName={`labor.${index}.worker`}
                                />
                              </Styled.SapceWidth>
                              &emsp;
                              <Styled.SapceWidth>
                                {/* <SharedStyled.Text fontSize="14px" fontWeight="medium">
                                Time<SharedStyled.AstricColor>*</SharedStyled.AstricColor>
                              </SharedStyled.Text> */}
                                {/* <Styled.NameValueUnitContainer width={'100%'}>
                                <Styled.PercentValueInput
                                  value={values.labor[index].time}
                                  width={'100%'}
                                  name={`labor.${[index]}.time`}
                                  type="number"
                                  placeholder="How long does it take?"
                                  borderRadius="4px 0px 0px 4px"
                                />
                                <Styled.UnitDiv>Min</Styled.UnitDiv>
                              </Styled.NameValueUnitContainer> */}

                                <SharedStyled.FlexRow margin="10px 0 0 0">
                                  <InputLabelWithValidation
                                    stateName={'labor[0].time'}
                                    labelName="Time"
                                    labelUnit={'Min'}
                                    inputType="number"
                                    value={String(values.labor[index].time)}
                                  />
                                </SharedStyled.FlexRow>
                              </Styled.SapceWidth>
                              &emsp;
                              <Styled.SapceWidth>
                                <CustomSelect
                                  labelName="Modified By..."
                                  error={false}
                                  value={modifiedByValues[values.labor[index].mod] ?? modifiedBy}
                                  dropDownData={modifiedByDropdown}
                                  setValue={setModifiedBy}
                                  setFieldValue={setFieldValue}
                                  innerHeight="45px"
                                  margin="10px 0 0 0"
                                  stateName={`labor.${[index]}.mod`}
                                />
                              </Styled.SapceWidth>
                            </Styled.SaperatorDiv>
                          ))}
                      </>
                    )}
                  </FieldArray>

                  {/* {isRoofRepair ? null : (
                  <CustomSelect
                    labelName={`Group`}
                    error={!!errors?.group}
                    value={values.group ?? ''}
                    dropDownData={Object.values(TaskGroups).map((v) => v)}
                    setValue={setGroup}
                    testId={testId}
                    setFieldValue={setFieldValue}
                    innerHeight="45px"
                    margin="10px 0 0 0"
                    stateName={`group`}
                  />
                )} */}

                  <SharedStyled.FlexBox marginTop="10px" flexDirection="column">
                    <CustomSelect
                      labelName={`Group`}
                      error={!!errors.group}
                      value={values.group}
                      dropDownData={typeGroups?.map((v) => v.name)}
                      setValue={setGroup}
                      testId={testId}
                      setFieldValue={setFieldValue}
                      innerHeight="45px"
                      margin="10px 0 0 0"
                      stateName={`group`}
                    />
                  </SharedStyled.FlexBox>

                  <Toggle
                    title="Active"
                    customStyles={{ marginBottom: '16px', marginTop: '26px', fontSize: '14px' }}
                    isToggled={!!values?.active}
                    onToggle={() => {
                      setFieldValue('active', !values?.active)
                    }}
                    className="text"
                  />

                  {inputData ? (
                    <>
                      <SharedStyled.FlexCol margin="20px 0 0 0">
                        <SharedStyled.FlexRow>
                          <SharedStyled.Text fontSize="16px" fontWeight="600">
                            Used in Packages: {inputData?.packages?.length || 'None'}
                          </SharedStyled.Text>

                          {/* <RenderData loader={<SLoader width={20} height={20} />} loading={projectsLoading}> */}

                          {/* </RenderData> */}
                        </SharedStyled.FlexRow>
                        {inputData?.packages?.length ? (
                          <ol>
                            <SharedStyled.FlexCol gap="6px">
                              {inputData?.packages.map((v: any) => (
                                <li
                                  key={v._id}
                                  style={{
                                    fontStyle: v.active ? 'normal' : 'italic',
                                    color: v.active ? 'inherit' : 'grey',
                                    textDecoration: v.deleted ? `line-through` : 'none',
                                  }}
                                >
                                  {v.name}
                                </li>
                              ))}
                            </SharedStyled.FlexCol>
                          </ol>
                        ) : (
                          <></>
                        )}
                      </SharedStyled.FlexCol>
                    </>
                  ) : (
                    <></>
                  )}

                  {inputData ? (
                    <>
                      <SharedStyled.FlexCol margin="20px 0 0 0">
                        <SharedStyled.FlexRow>
                          <SharedStyled.Text fontSize="16px" fontWeight="600">
                            Used in Options: {inputData?.options?.length || 'None'}
                          </SharedStyled.Text>

                          {/* <RenderData loader={<SLoader width={20} height={20} />} loading={projectsLoading}> */}

                          {/* </RenderData> */}
                        </SharedStyled.FlexRow>
                        {inputData?.options?.length ? (
                          <ol>
                            <SharedStyled.FlexCol gap="6px">
                              {inputData?.options.map((v: any) => (
                                <li
                                  key={v._id}
                                  style={{
                                    fontStyle: v.active ? 'normal' : 'italic',
                                    color: v.active ? 'inherit' : 'grey',
                                    textDecoration: v.deleted ? `line-through` : 'none',
                                  }}
                                >
                                  {v.name}
                                </li>
                              ))}
                            </SharedStyled.FlexCol>
                          </ol>
                        ) : (
                          <></>
                        )}
                      </SharedStyled.FlexCol>
                    </>
                  ) : (
                    <></>
                  )}
                  <SharedStyled.ButtonContainer marginTop="30px">
                    <Button
                      width="max-content"
                      type="submit"
                      onClick={() => {
                        scrollToFirstError()
                      }}
                      isLoading={submitLoading}
                    >
                      Save Changes
                    </Button>
                    &emsp;
                    <Button
                      className="fit delete"
                      type="button"
                      onClick={() => handleDelete()}
                      isLoading={deleteLoading}
                    >
                      Delete Task
                    </Button>
                  </SharedStyled.ButtonContainer>

                  {/* <TaskTable /> */}
                </Styled.InfoContainer>
              </Form>
            )
          }}
        </Formik>
      )}
    </Styled.ModalContainer>
  )
}

export default CustomizeTaskModal
