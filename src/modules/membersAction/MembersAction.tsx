import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import { CrewReportTableContentLabel, TableContent, TableHeading, TableTitle } from '../reports/customSalesReport/style'
import { deleteCompanySalesAction, getSalesActionDefault, getSalesActions } from '../../logic/apis/sales'
import { isSuccess, notify } from '../../shared/helpers/util'
import { EditIcon } from '../../assets/icons/EditIcon'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import { ButtonCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import ActionModal from '../opportunity/components/actionModal/ActionModal'
import { CustomModal } from '../../shared/customModal/CustomModal'

const MembersAction = () => {
  const [toggleConvCount, setToggleConvCount] = useState<{ [key: string]: boolean }>({})
  const [salesActions, setSalesActions] = useState<any[]>([])
  const [salesActionsDefault, setSalesActionsDefault] = useState<any[]>([])
  const [actionModal, setActionModal] = useState(false)
  const [actionData, setActionData] = useState<any>()

  useEffect(() => {
    fetchActions()
    fetchActionsDefault()
  }, [])

  const fetchActions = async () => {
    try {
      const res = await getSalesActions(false)

      if (isSuccess(res)) {
        const { salesActions } = res?.data?.data
        setSalesActions(salesActions)
      }
    } catch (error) {
    } finally {
    }
  }

  const handleDelete = async (actionId: string) => {
    try {
      const res = await deleteCompanySalesAction({ actionId: actionId! })
      if (isSuccess(res)) {
        fetchActionsDefault()
        notify('Action deleted successfully', 'success')
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const fetchActionsDefault = async () => {
    try {
      const res = await getSalesActionDefault()
      let receivedData: any = []
      if (isSuccess(res)) {
        const { salesAction } = res?.data?.data
        salesAction?.actions?.forEach((res: any, _index: number) => {
          receivedData.push({
            name: res?.name || '-',
            type: res?.type || '-',
            action: (
              <>
                <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                  <SharedStyled.IconContainer
                    className="edit"
                    onClick={() => {
                      setActionData({
                        name: res?.name,
                        type: res?.type,
                        _id: res?._id,
                      })
                      setActionModal(true)
                    }}
                  >
                    <EditIcon />
                  </SharedStyled.IconContainer>

                  <SharedStyled.IconContainer
                    className="delete"
                    onClick={() => {
                      handleDelete(res?._id)
                    }}
                  >
                    <DeleteIcon />
                  </SharedStyled.IconContainer>
                </SharedStyled.FlexBox>
              </>
            ),
          })
        })
        setSalesActionsDefault(receivedData)
      }
    } catch (error) {
    } finally {
    }
  }

  console.log({ salesActions })

  const toggleCount = (type: string) => {
    setToggleConvCount((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }
  return (
    <div>
      <SharedStyled.SectionTitle textAlign="center">Company Actions</SharedStyled.SectionTitle>
      <SharedStyled.FlexCol gap="10px">
        <Styled.SectionCont>
          <SharedStyled.FlexBox justifyContent="space-between">
            <SharedStyled.Text fontSize="24px" fontWeight="600">
              Company Default Actions
            </SharedStyled.Text>
            <ButtonCont>
              <Button
                onClick={() => {
                  setActionModal(true)
                  setActionData({})
                }}
              >
                Add Action
              </Button>
            </ButtonCont>
          </SharedStyled.FlexBox>
          <div>
            <Styled.TagWrapper
              bgColor={toggleConvCount['default'] ? '#9b9b9b' : '#eaeaea'}
              onClick={() => toggleCount('default')}
            >
              <SharedStyled.Text textAlign="left" fontSize="16px">
                Default
              </SharedStyled.Text>
            </Styled.TagWrapper>
          </div>
          <SharedStyled.FlexCol gap="10px">
            {toggleConvCount['default'] ? (
              <>
                <SharedStyled.Text margin="0 auto" fontSize="20px" fontWeight="600">
                  Default
                </SharedStyled.Text>
                <Styled.TableContainer>
                  <>
                    <TableHeading column="repeat(3, 1fr)">
                      <TableTitle>Action</TableTitle>
                      <TableTitle>Type</TableTitle>
                      <TableTitle></TableTitle>
                    </TableHeading>
                  </>

                  {salesActionsDefault?.map((action: any) => {
                    return (
                      <>
                        <tr>
                          <TableContent
                            // key={}
                            column="repeat(3, 1fr)"
                          >
                            <CrewReportTableContentLabel>{action?.name}</CrewReportTableContentLabel>
                            <CrewReportTableContentLabel>{action?.type}</CrewReportTableContentLabel>
                            <CrewReportTableContentLabel>{action?.action}</CrewReportTableContentLabel>
                          </TableContent>
                        </tr>
                      </>
                    )
                  })}
                </Styled.TableContainer>
              </>
            ) : null}
          </SharedStyled.FlexCol>
        </Styled.SectionCont>

        <Styled.SectionCont>
          <SharedStyled.Text fontSize="24px" fontWeight="700">
            Member's Actions
          </SharedStyled.Text>

          <SharedStyled.FlexRow gap="10px" flexWrap="wrap" margin="16px 0 0 0">
            {salesActions?.map((salesAction: any, index: number) => (
              <Styled.TagWrapper
                bgColor={toggleConvCount[index + salesAction?.memberData?.name] ? '#9b9b9b' : '#eaeaea'}
                onClick={() => toggleCount(index + salesAction?.memberData?.name)}
              >
                <SharedStyled.Text textAlign="left" fontSize="16px">
                  {salesAction?.memberData?.name}
                </SharedStyled.Text>
              </Styled.TagWrapper>
            ))}
          </SharedStyled.FlexRow>

          <SharedStyled.FlexCol gap="10px">
            {salesActions?.map((salesAction: any, index: number) =>
              toggleConvCount[index + salesAction?.memberData?.name] ? (
                <>
                  <SharedStyled.Text margin="0 auto" fontSize="20px" fontWeight="600">
                    {salesAction?.memberData?.name}
                  </SharedStyled.Text>
                  <Styled.TableContainer>
                    <>
                      <TableHeading column="repeat(2, 1fr)">
                        <TableTitle>Action</TableTitle>
                        <TableTitle>Type</TableTitle>
                      </TableHeading>
                    </>

                    {salesAction?.actions?.map((action: any) => {
                      return (
                        <>
                          <tr>
                            <TableContent
                              // key={}
                              column="repeat(2, 1fr)"
                            >
                              <CrewReportTableContentLabel>{action?.name || '--'}</CrewReportTableContentLabel>
                              <CrewReportTableContentLabel>{action?.type || '--'}</CrewReportTableContentLabel>
                            </TableContent>
                          </tr>
                        </>
                      )
                    })}
                  </Styled.TableContainer>
                </>
              ) : null
            )}
          </SharedStyled.FlexCol>
        </Styled.SectionCont>
      </SharedStyled.FlexCol>
      <CustomModal show={actionModal}>
        <ActionModal
          onClose={() => {
            setActionModal(false)
            setActionData({})
          }}
          onComplete={() => {
            fetchActionsDefault()
            setActionData({})
          }}
          actionData={actionData}
          isDefault={true}
        />
      </CustomModal>
    </div>
  )
}

export default MembersAction
