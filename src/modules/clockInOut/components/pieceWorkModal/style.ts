import { Field, FieldArray } from 'formik'
import styled from 'styled-components'
import { colors, screenSizes } from '../../../../styles/theme'
import { SettingModalHeaderContainer } from '../../../units/components/newUnitModal/style'
import { Nue } from '../../../../shared/helpers/constants'

export const PieceWorkModalContainer = styled.div`
  background: ${colors.white};
  width: 600px;
  height: 100%;
  border-radius: 10px;
  @media (max-width: 768px) {
    width: 90vw;
  }

  label {
    margin-top: 0;
    color: ${colors.gray1};
    font-family: ${Nue.regular};
  }
  .extra-container {
    * {
      height: 52px;
    }
    input {
      padding: 12px;
      max-width: 40px;
    }
  }

  button[type='submit'] {
    @media (max-width: ${screenSizes.XS}px) {
      padding: 16px;
      font-size: 14px;
    }
  }
`

export const ModalHeaderContainer = styled(SettingModalHeaderContainer)``

export const ModalHeader = styled.p`
  margin: 0;
  line-height: 1.5;
  font-size: 20px;
  font-family: ${Nue.medium};
`

export const CrossContainer = styled.div`
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
    svg path {
      stroke: ${colors.darkGrey};
    }
    :hover {
      svg path {
        stroke: ${colors.grey};
      }
      transform: scale(1.03);
    }
    transition: all 0.01s linear;
  }
`

export const LabelDiv = styled.label<any>`
  font-size: 14px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

export const CheckBox = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.marginTop};
  cursor: pointer;
`

export const TextArea = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.marginTop};
  cursor: pointer;
  resize: vertical;
  outline: none;
  border: 1px solid ${colors.lightGray};
  border-radius: 8px;
  padding: 12px 18px;
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
  }
  margin-top: ${(props) => props.marginTop};
`

export const CheckBoxDescription = styled.p<any>`
  margin: 0;
  font-size: 10px;
  color: ${colors.gray1};
  font-family: ${Nue.regular};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

export const WorkDoneContainer = styled(FieldArray)<any>`
  /* width: 100%;
  border-radius: 8px;
  padding: 6px;
  margin-top: ${(props) => props.marginTop};
  border: 1px solid ${colors.darkGrey}; */
`

export const MultiInputField = styled(Field)<any>``

export const WorkDoneInnerSingleContainer = styled.div<any>`
  width: 100%;
  border-top: 1px solid ${colors.lightGrey7};
  border-bottom: 1px solid ${colors.lightGrey7};
  padding-bottom: 10px;
  margin-top: ${(props) => props.marginTop};
`

export const AddWorkButton = styled.button``

export const UnitDiv = styled.div`
  padding: 4px 12px;
  background-color: ${colors.lightGrey3};
  border-radius: 0px 4px 4px 0px;
  border: 1px solid ${colors.lightGrey6};
  font-size: 12px;
  color: ${colors.lightGrey4};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`

export const ExtrasContainer = styled.div<any>`
  /* width: 100%;
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap; */

  width: 100%;
  display: grid;
  grid-template-columns: 1fr;
  gap: 5px;
  margin-top: ${(props) => props.marginTop};
  justify-content: space-between;

  ${UnitDiv} {
    padding: 4px;
  }

  input {
    max-width: 40px;
    padding: 0;
  }
  @media (min-width: ${screenSizes.XS}px) {
    grid-template-columns: max-content max-content;
  }
`

export const NameValueUnitContainer = styled.div<any>`
  width: ${(props) => props.width};
  display: flex;
  align-items: center;
  justify-content: ${(props) => props.justifyContent};
  margin-top: ${(props) => props.marginTop};
  flex-wrap: ${(props) => props.flexWrap};
  gap: ${(props) => (props.gap ? props.gap : '8px')};

  justify-content: flex-start;
  p {
    width: 156px;
  }
  &:nth-child(4) {
    p {
      text-align: left;
      width: 156px;
    }
  }
  @media (min-width: ${screenSizes.XS}px) {
    justify-content: flex-end;
    p {
      width: max-content;
    }
    &:nth-child(4) {
      p {
        text-align: right;
        width: 156px;
      }
    }
  }
  ${UnitDiv} {
    padding: 4px;
  }
  input {
    padding: 3.7px 4px;
    max-width: 35px;
  }
`

export const NameDiv = styled.p<any>`
  word-break: break-all;
  margin: 0;
  font-size: 12px;
  color: ${colors.lightGrey4};
  width: 80px;
  font-family: ${Nue.regular};
`

export const ValueInput = styled(Field)<any>`
  padding: 2.7px 10px;
  outline: none;
  max-width: 50px;
  color: ${colors.lightGrey4};
  border: 1px solid ${colors.lightGrey};
  width: ${(props) => props.width};
  ::-webkit-outer-spin-button,
  ::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
    /* background-color: ${colors.white}; */
  }
  border-radius: 4px 0px 0px 4px;
  margin-left: ${(props) => props.marginLeft};
`
