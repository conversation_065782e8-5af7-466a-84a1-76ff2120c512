import { Field, FieldArray, Form, Formik } from 'formik'
import { Fragment, useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

export const mergeSourceAndCampaignNames = (data: any) => {
  return data.flatMap((channel: any) =>
    channel.leadSources.flatMap((source: any) => [
      source.name,
      ...source.campaigns.map((campaign: any) => campaign.name),
    ])
  )
}

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { createClient, getClients } from '../../../../logic/apis/client'
import { onlyNumber, onlyText } from '../../../../shared/helpers/regex'
import {
  getDigitsFromPhone,
  getIdFromName,
  getKeysFromObjects,
  isSuccess,
  notify,
  splitFullName,
  getNameFrom_Id,
  getDataFromLocalStorage,
  formatPhoneNumber,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import Toggle from '../../../../shared/toggle/Toggle'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import { getCampaigns, getLeadSources } from '../../../../logic/apis/leadSource'
import { I_LeadSource } from '../../../newLead/NewLead'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { Nue, StorageKey, usStatesShortNames } from '../../../../shared/helpers/constants'
import { colors } from '../../../../styles/theme'
import Button from '../../../../shared/components/button/Button'
import '../../../../shared/helpers/yupExtension'
import Contacts from './components/Contacts'
import { CheckBox } from '../../../timeCard/components/editTimeCardPopUp/style'
import { SLoader } from '../../../../shared/components/loader/Loader'
import Checkbox from '../../../../shared/checkbox/Checkbox'
import { getFormattedLeadSrcData, getLeadSrcDropData, getLeadSrcDropdownId } from '../../../leadSource/LeadSource'
import useFetch from '../../../../logic/apis/useFetch'
import AutoCompleteIndentation from '../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'

interface I_AddNewClientModal {
  setShowAddCityModal: React.Dispatch<React.SetStateAction<boolean>>
  setShowAddNewClientModal: React.Dispatch<React.SetStateAction<boolean>>
  setDetailsUpdate: React.Dispatch<React.SetStateAction<boolean>>
  detailsUpdate: boolean
  setClientAutoFill?: any
  clientName?: string
  onClose?: () => void
  setCreatedClient?: any
  referrerDropdownData?: any
  refererres?: any
  setReferrerValue?: any
  isNewLead?: boolean
  noLoadScript?: boolean
  isLead?: boolean
  leadInfo?: any
  setClientData?: React.Dispatch<any>
}
export interface I_Contacts {
  email: ''
  firstName: ''
  lastName: ''
  fullName: ''
  phone: ''
  notes: ''
}
export const AddNewClientModal = (props: I_AddNewClientModal) => {
  const {
    setShowAddNewClientModal,
    setDetailsUpdate,
    setShowAddCityModal,
    detailsUpdate,
    setClientAutoFill,
    clientName,
    onClose,
    setCreatedClient,
    referrerDropdownData,
    refererres,
    setReferrerValue,
    isNewLead,
    noLoadScript,
    isLead,
    leadInfo,
    setClientData,
  } = props
  const [isBusiness, setIsBusiness] = useState(leadInfo?.isBusiness ? true : false)

  /**
   * InitialValues is an interface declared here so that it can be used as a type for the useState hook
   */

  interface InitialValues {
    firstName: string
    lastName: string
    city: string
    street: string
    state: string
    zip: string
    phone: string
    email: string
    contacts: I_Contacts[]
    leadSourceName: string
    referredBy: string
    notes: string

    // businessName?: string
  }

  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: isLead ? leadInfo?.firstName : '',
    lastName: isLead ? leadInfo?.lastName : '',
    city: isLead ? leadInfo?.city : '',
    street: isLead ? leadInfo?.street : '',
    state: isLead ? leadInfo?.state : '',
    zip: isLead ? leadInfo?.zip : '',
    phone: isLead ? leadInfo?.phone : '',
    contacts: [
      // {
      //   email: '',
      //   firstName: '',
      //   lastName: '',
      //   phone: '',
      //   notes: '',
      // },
    ],
    email: isLead ? leadInfo?.email : '',
    leadSourceName: isLead ? leadInfo?.leadSource : '',
    referredBy: '',
    notes: '',

    // businessName: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [toggleAddress, setToggleAddress] = useState<boolean>(false)
  // const [clientFirstName, setClientFirstName] = useState('')
  const [leadsrcDrop, setLeadsrcDrop] = useState<I_LeadSource[]>([])
  const [leadSrcData, setLeadSrcData] = useState([])
  const [clients, setClients] = useState<I_LeadSource[]>([])
  const [clientsLoader, setClientsLoader] = useState<boolean>(true)
  const [checkedIndex, setCheckedIndex] = useState(null)
  const [selectedValue, setSelectedValue] = useState<any>(null)
  const globalSelector = useSelector((state: any) => state)
  const { currentMember, companySettingForAll } = globalSelector.company

  useEffect(() => {
    if (leadSrcData?.length) {
      const data = getLeadSrcDropData(leadSrcData)
      setLeadsrcDrop(data)
    }
  }, [leadSrcData?.length])

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  useEffect(() => {
    if (isLead) {
      try {
        const fetchClient = async () => {
          const requests = []

          if (leadInfo?.phone) {
            requests.push(getClients({ deleted: false, limit: '20', search: leadInfo.phone }, false))
          }
          if (leadInfo?.email) {
            requests.push(getClients({ deleted: false, limit: '20', search: leadInfo.email }, false))
          }

          const responses = await Promise.all(requests)
          const successfulResponses = responses.filter(isSuccess)
          // const clientResponse = await getClients(
          //   {
          //     deleted: false,
          //     limit: '20',
          //     search: `${leadInfo?.phone || ''} ${leadInfo?.email || ''}`,
          //   },
          //   false
          // )

          const clients = successfulResponses.flatMap((response) => response?.data?.data?.client || [])

          // Merge both arrays and remove duplicates based on a unique identifier (e.g., client ID or phone)
          const uniqueClients = Array.from(new Map(clients.map((client) => [client._id, client])).values())

          setClients([...uniqueClients, { isNewClient: true }])

          setClientsLoader(false)
        }
        fetchClient()
      } catch (error) {
        setClientsLoader(false)
        console.log({ error })
      }
    }
  }, [])

  /**
   * AddCityModalSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */

  const AddCityModalSchema = Yup.object().shape({
    // firstName: Yup.string().when('businessName', (value, schema) => {
    //   if (value && isBusiness) return schema.optional()
    //   return schema.min(1, 'Too Short!').max(50, 'Too Long!').required('Required').matches(onlyText, 'Enter Valid Name')
    // }),
    firstName: Yup.string().required('Required'),
    lastName: isBusiness
      ? Yup.string().min(1, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name')
      : Yup.string().min(1, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    leadSourceName: Yup.string(),
    notes: Yup.string(),
    phone: Yup.string().min(10, 'Too Short!').required('Required'),
    email: Yup.string().trimEmail().email('Invalid email'),
    // businessName: Yup.string().min(2, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    // .required('Required'),
  })

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    const result = getLeadSrcDropdownId(submittedValues?.leadSourceName, leadSrcData)

    try {
      // const customLastName = submittedValues?.businessName?.split(clientFirstName)?.[1]?.trim()
      // const leadsourceId = getIdFromName(submittedValues?.leadSourceName, leadsrcDrop)
      const leadsourceId = result?.leadSourceId
      const campaignId = result?.campaignId || undefined
      const referredById =
        leadsourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
          ? getIdFromName(submittedValues?.referredBy, refererres)
          : undefined
      setLoading(true)
      let { email, ...restSubmitted }: any = submittedValues

      // remove cEmails key if empty
      if (email) restSubmitted.email = email

      let phone = getDigitsFromPhone(submittedValues.phone)
      let dataObj = {
        ...restSubmitted,
        firstName: submittedValues?.firstName?.trim(),
        lastName: isBusiness ? '' : submittedValues?.lastName?.trim() || '',
        // businessName: submittedValues?.businessName?.trim() || undefined,
        phone,
        leadSource: leadsourceId,
        createdBy: currentMember._id,
        referredBy: referredById ? referredById : undefined,
        isBusiness,
        campaignId,
      }

      let response = await createClient(dataObj)
      if (response?.data?.statusCode === 201) {
        setClientAutoFill?.({
          // clientName: `${isBusiness ? clientFirstName : submittedValues?.firstName}${
          //   submittedValues?.lastName ? ` ${isBusiness ? customLastName : submittedValues?.lastName}` : ''
          // }`.trim(),
          phone: isLead ? submittedValues?.phone : undefined,
          email: isLead ? submittedValues?.email : undefined,
          clientName: `${submittedValues?.firstName} ${submittedValues?.lastName || ''}`?.trim(),
          sStreet: submittedValues?.street,
          sCity: submittedValues?.city,
          sState: submittedValues?.state,
          sZip: submittedValues?.zip,
          sLeadSourceName: submittedValues?.leadSourceName,
          sLeadSource: leadsourceId,
          referredBy: referredById,
        })

        notify('Client Added Successfully', 'success')
        resetForm()
        setDetailsUpdate((prev) => !prev)
        setLoading(false)
        setShowAddNewClientModal(false)
        setCreatedClient(response?.data?.data?.data)
        setClientData(response?.data?.data?.data)
        onClose?.()
      } else {
        setLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('AddNewClientModal handleSubmit', error)
    }
  }

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  useEffect(() => {
    getLeadSrcData()
  }, [detailsUpdate])

  useEffect(() => {
    if (!clientsLoader) {
      if (clients?.length > 1) {
        handleCheckboxChange(0, clients[0])
      } else {
        handleCheckboxChange(-1)
      }
    }
  }, [clientsLoader])
  const handleCheckboxChange = (index: number, client?: any) => {
    if (checkedIndex === index) {
      // If the same checkbox is clicked again, uncheck it
      setCheckedIndex(null)
      setClientAutoFill?.({}) // Reset selected value
    } else {
      // Otherwise, select the new checkbox
      setCheckedIndex(index)
      setCreatedClient(client)
      setClientAutoFill?.({
        // clientName: `${isBusiness ? clientFirstName : submittedValues?.firstName}${
        //   submittedValues?.lastName ? ` ${isBusiness ? customLastName : submittedValues?.lastName}` : ''
        // }`.trim(),
        clientName: `${client?.firstName} ${client?.lastName || ''}`?.trim(),
        sStreet: client?.street,
        sCity: client?.city,
        sState: client?.state,
        sZip: client?.zip,
        sLeadSourceName: client?.leadSourceName,
        sLeadSource: client?.leadSource,
        referredBy: client?.referredBy,
        phone: client?.phone,
        email: client?.email,
      })
    }
  }
  return (
    <Styled.AddNewClientModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={AddCityModalSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange }) => {
          useEffect(() => {
            if (!isLead) {
              if (clientName && isNewLead) {
                const { firstName, lastName } = splitFullName(clientName)
                setFieldValue('firstName', firstName)
                setFieldValue('lastName', lastName)
                // isBusiness && setFieldValue('businessName', `${firstName} ${lastName}`)
                // isBusiness && setClientFirstName(firstName)
              }
            }
          }, [clientName, isBusiness])

          useEffect(() => {
            if (isBusiness) {
              if (values?.firstName !== '' || values?.lastName !== '')
                setFieldValue('firstName', `${values?.firstName?.trim()} ${values?.lastName?.trim() || ''}`)
              setFieldValue('lastName', '')
            } else {
              if (!isNewLead) {
                const { firstName, lastName } = values?.lastName
                  ? splitFullName(`${values?.firstName} ${values?.lastName}`)
                  : splitFullName(values?.firstName)
                setFieldValue('firstName', firstName)
                setFieldValue('lastName', lastName)
              }
            }
          }, [isBusiness])

          // useEffect(() => {
          //   if (!isNewLead) {
          //     const { firstName, lastName } = splitFullName(
          //       values?.businessName?.trim() || `${values?.firstName} ${values?.lastName}`
          //     )
          //     setFieldValue('firstName', firstName)
          //     setFieldValue('lastName', lastName)
          //     isBusiness && setFieldValue('businessName', `${firstName} ${lastName}`)
          //     isBusiness && setClientFirstName(firstName)
          //   }
          // }, [isBusiness])

          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Add New Client</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    setCreatedClient?.({})
                    setClientAutoFill?.({
                      clientName: '',
                      sStreet: '',
                      sCity: '',
                      sState: '',
                      sZip: '',
                      sLeadSourceName: '',
                      sLeadSource: '',
                      referredBy: '',
                      phone: '',
                      email: '',
                    })
                    setShowAddNewClientModal(false)
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>

              {isLead ? (
                <Styled.ClientSelectWrapper>
                  <>
                    {clients?.length ? (
                      <>
                        {clients?.map((client, index: number) => {
                          return (
                            <Fragment key={index}>
                              <SharedStyled.FlexBox justifyContent="space-between" margin="0 0 10px 0">
                                <SharedStyled.FlexBox alignItems="center" gap="10px">
                                  <div>
                                    {/* <CheckBox
                                      style={{ cursor: 'pointer' }}
                                      type="checkbox"
                                      checked={checkedIndex === index}
                                      onChange={() => handleCheckboxChange(index, client)}
                                      disabled={checkedIndex !== null && checkedIndex !== index}
                                    /> */}
                                    <Field
                                      type="radio"
                                      name=""
                                      style={{ height: '20px', width: '20px', cursor: 'pointer' }}
                                      // value={_id}
                                      // id={_id}
                                      checked={client?.isNewClient ? checkedIndex === -1 : checkedIndex === index}
                                      onChange={() => {
                                        client?.isNewClient
                                          ? handleCheckboxChange(-1)
                                          : handleCheckboxChange(index, client)
                                      }}
                                    />
                                  </div>
                                  &nbsp;
                                  {client?.isNewClient ? (
                                    <div>
                                      <SharedStyled.Text fontWeight="700">New Client</SharedStyled.Text>
                                    </div>
                                  ) : (
                                    <div>
                                      <SharedStyled.Text fontWeight="400">
                                        {client.firstName} {client.lastName}
                                      </SharedStyled.Text>
                                      <br />
                                      <SharedStyled.Text fontWeight="400">
                                        {client.street !== '' ? client.street : '--'}
                                      </SharedStyled.Text>
                                      <br />
                                      <SharedStyled.Text fontWeight="400">
                                        {/* <b>City : </b> */}
                                        <span style={{ fontFamily: Nue.regular }}>{client.city},&nbsp;</span>
                                        {/* <b>State : </b> */}
                                        <span style={{ fontFamily: Nue.regular }}>{client.state},&nbsp;</span>
                                        {/* <b>Zip : </b> */}
                                        <span style={{ fontFamily: Nue.regular }}>{client.zip}</span>
                                      </SharedStyled.Text>
                                    </div>
                                  )}
                                </SharedStyled.FlexBox>

                                <div style={{ width: '300px' }}>
                                  <SharedStyled.Text fontWeight="400">
                                    {/* <b>City : </b> */}
                                    <span style={{ fontFamily: Nue.regular }}>
                                      {formatPhoneNumber(client.phone, '')}
                                    </span>
                                    {/* <b>State : </b> */}
                                  </SharedStyled.Text>
                                  <br />
                                  <SharedStyled.Text fontWeight="400">
                                    <span style={{ fontFamily: Nue.regular }}>{client.email}</span>
                                  </SharedStyled.Text>
                                </div>
                              </SharedStyled.FlexBox>
                            </Fragment>
                          )
                        })}
                        {/* <SharedStyled.FlexBox alignItems="center" gap="10px">
                          <div>
                            <CheckBox
                              style={{ cursor: 'pointer' }}
                              type="checkbox"
                              checked={checkedIndex === -1}
                              onChange={() => handleCheckboxChange(-1)}
                              disabled={checkedIndex !== null && checkedIndex !== -1}
                            />
                          </div>
                          &nbsp;
                          <div>
                            <SharedStyled.Text fontWeight="700">New Client</SharedStyled.Text>
                          </div>
                        </SharedStyled.FlexBox> */}
                        {clients?.length > 1 && checkedIndex !== -1 ? (
                          <>
                            <br />
                            <Button onClick={() => setShowAddNewClientModal(false)} type="button" isLoading={loading}>
                              Add
                            </Button>
                          </>
                        ) : null}
                      </>
                    ) : clientsLoader ? (
                      <>
                        <SLoader height={35} width={100} isPercent />
                      </>
                    ) : (
                      <>
                        <SharedStyled.FlexBox alignItems="center" gap="10px">
                          <div>
                            <CheckBox
                              style={{ cursor: 'pointer' }}
                              type="checkbox"
                              checked={checkedIndex === -1}
                              onChange={() => handleCheckboxChange(-1)}
                              disabled={checkedIndex !== null && checkedIndex !== -1}
                            />
                          </div>
                          &nbsp;
                          <div>
                            <SharedStyled.Text fontWeight="700">New Client</SharedStyled.Text>
                          </div>
                        </SharedStyled.FlexBox>
                      </>
                    )}
                  </>
                </Styled.ClientSelectWrapper>
              ) : null}

              <SharedStyled.SettingModalContentContainer>
                {(isLead && checkedIndex === -1) || !isLead ? (
                  <Form className="form">
                    <SharedStyled.Content
                      maxWidth="706px"
                      width="100%"
                      gap="6px"
                      disableBoxShadow={true}
                      noPadding={true}
                    >
                      <Toggle
                        title="Business"
                        customStyles={{ margin: '16px' }}
                        isToggled={isBusiness}
                        onToggle={() => {
                          setIsBusiness((prev) => !prev)
                        }}
                      />

                      {isBusiness ? (
                        <InputWithValidation
                          labelName="Business Name*"
                          stateName="firstName"
                          error={touched.firstName && errors.firstName ? true : false}
                          twoInput={true}
                        />
                      ) : (
                        <SharedStyled.TwoInputDiv>
                          <InputWithValidation
                            labelName="Primary First Name*"
                            stateName="firstName"
                            error={touched.firstName && errors.firstName ? true : false}
                            twoInput={true}
                          />
                          <InputWithValidation
                            labelName="Primary Last Name"
                            stateName="lastName"
                            error={touched.lastName && errors.lastName ? true : false}
                            twoInput={true}
                          />
                        </SharedStyled.TwoInputDiv>
                      )}

                      <SharedStyled.TwoInputDiv>
                        <SharedPhone
                          labelName="Primary Phone*"
                          stateName="phone"
                          value={values.phone || ''}
                          onChange={handleChange('phone')}
                          error={touched.phone && errors.phone ? true : false}
                        />

                        <InputWithValidation
                          labelName="Primary Email"
                          stateName="email"
                          error={touched.email && errors.email ? true : false}
                        />
                      </SharedStyled.TwoInputDiv>

                      <FieldArray name="contacts">
                        {({ remove, push }) => (
                          <>
                            {values.contacts?.length > 0 && (
                              <SharedStyled.FlexCol margin="10px 0 0 0">
                                <SharedStyled.Text
                                  color={`${colors.darkGrey}`}
                                  textAlign="left"
                                  fontWeight="bold"
                                  fontSize="16px"
                                >
                                  Other Contacts
                                </SharedStyled.Text>
                                {values.contacts?.map((data: any, index: number) => (
                                  <>
                                    <Contacts
                                      values={values?.contacts}
                                      remove={remove}
                                      index={index}
                                      data={data}
                                      setFieldValue={setFieldValue}
                                      handleChange={handleChange}
                                    />
                                  </>
                                ))}
                              </SharedStyled.FlexCol>
                            )}
                            <SharedStyled.ButtonContainer justifyContent="start" margin="24px 0 16px 0">
                              <Button
                                type="button"
                                className="fit outline"
                                onClick={() =>
                                  push({
                                    email: '',
                                    firstName: '',
                                    lastName: '',
                                    phone: '',
                                    notes: '',
                                  })
                                }
                              >
                                Add Contact
                              </Button>
                            </SharedStyled.ButtonContainer>
                          </>
                        )}
                      </FieldArray>

                      {/* <CustomSelect
                        value={values.leadSourceName}
                        labelName="Lead Source*"
                        stateName="leadSourceName"
                        dropDownData={getKeysFromObjects(leadsrcDrop, 'name')?.sort()}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        margin="10px 0 0 0"
                        error={touched.leadSourceName && errors.leadSourceName ? true : false}
                      /> */}

                      {leadSrcData?.length ? (
                        <AutoCompleteIndentation
                          labelName="Lead Source*"
                          stateName={`leadSourceName`}
                          isLeadSource
                          dropdownHeight="300px"
                          error={touched.leadSourceName && errors.leadSourceName ? true : false}
                          borderRadius="0px"
                          setFieldValue={setFieldValue}
                          options={mergeSourceAndCampaignNames(leadSrcData)}
                          formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                          value={values.leadSourceName!}
                          setValueOnClick={(val: string) => {
                            setFieldValue('leadSourceName', val)
                          }}
                          className="material-autocomplete"
                          isIndentation={true}
                        />
                      ) : null}

                      {values.leadSourceName === 'Referral' && (
                        <SharedStyled.FlexBox width="100%" justifyContent="end">
                          <CustomSelect
                            labelName="Referrer"
                            stateName="referredBy"
                            error={touched.referredBy && errors.referredBy ? true : false}
                            setFieldValue={setFieldValue}
                            setValue={setReferrerValue}
                            value={values.referredBy}
                            dropDownData={referrerDropdownData}
                            innerHeight="52px"
                            className="top"
                            maxWidth="95%"
                          />
                        </SharedStyled.FlexBox>
                      )}

                      <Styled.GoogleSearchBox>
                        <AutoCompleteAddress
                          setFieldValue={setFieldValue}
                          street={'street'}
                          city={'city'}
                          state={'state'}
                          zip={'zip'}
                          sourceAddress={companySettingForAll?.address}
                          companyLatLong={companySettingForAll}
                          noLoadScript={noLoadScript ? noLoadScript : false}
                        />
                      </Styled.GoogleSearchBox>
                      {/* setToggleAddress */}
                      {!toggleAddress ? (
                        <div style={{ width: '100%' }}>
                          {values.street !== '' || values.city !== '' || values.state !== '' || values.zip !== '' ? (
                            <>
                              <SharedStyled.Text fontWeight="400">
                                <b>Address: </b> <br />
                                <span style={{ fontFamily: Nue.regular }}>
                                  {values.street !== '' ? values.street : '--'}
                                </span>
                              </SharedStyled.Text>
                              <br />
                              <SharedStyled.Text fontWeight="400">
                                {/* <b>City : </b> */}
                                <span style={{ fontFamily: Nue.regular }}>{values.city},&nbsp;</span>
                                {/* <b>State : </b> */}
                                <span style={{ fontFamily: Nue.regular }}>{values.state},&nbsp;</span>
                                {/* <b>Zip : </b> */}
                                <span style={{ fontFamily: Nue.regular }}>{values.zip}</span>
                              </SharedStyled.Text>
                              &emsp;
                              <span
                                style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                                className="link"
                                onClick={() => setToggleAddress(!toggleAddress)}
                              >
                                Edit Manually
                              </span>
                            </>
                          ) : (
                            <>
                              <span
                                style={{ cursor: 'pointer', color: colors.darkBlue, fontFamily: Nue.medium }}
                                className="link"
                                onClick={() => setToggleAddress(!toggleAddress)}
                              >
                                Edit Manually
                              </span>
                            </>
                          )}
                        </div>
                      ) : (
                        <>
                          <InputWithValidation
                            labelName="Street Address"
                            stateName="street"
                            error={touched.street && errors.street ? true : false}
                          />

                          <SharedStyled.TwoInputDiv>
                            <InputWithValidation
                              labelName="City"
                              stateName="city"
                              error={touched.city && errors.city ? true : false}
                              twoInput={true}
                            />
                            <CustomSelect
                              dropDownData={usStatesShortNames}
                              setValue={() => {}}
                              stateName="state"
                              value={values.state}
                              // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                              setFieldValue={setFieldValue}
                              labelName="State"
                              // innerHeight="52px"
                              margin="8px 0 0 0"
                            />
                            <InputWithValidation
                              labelName="Zip"
                              stateName="zip"
                              error={touched.zip && errors.zip ? true : false}
                            />
                          </SharedStyled.TwoInputDiv>
                        </>
                      )}
                      {/* <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Notes
                    </Styled.LabelDiv> */}
                      <Styled.TextArea
                        component="textarea"
                        placeholder="Notes"
                        as={Field}
                        name="notes"
                        marginTop="8px"
                        height="52px"
                      ></Styled.TextArea>
                      <SharedStyled.ButtonContainer marginTop="26px">
                        <Button type="submit" isLoading={loading}>
                          Add
                        </Button>
                        <Button
                          type="button"
                          className="delete"
                          onClick={() => {
                            setShowAddNewClientModal(false)
                            setClientAutoFill?.({})
                          }}
                        >
                          Close
                        </Button>
                      </SharedStyled.ButtonContainer>
                    </SharedStyled.Content>
                  </Form>
                ) : null}
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.AddNewClientModalContainer>
  )
}
