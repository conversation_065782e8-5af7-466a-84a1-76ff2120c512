import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { Table } from '../../../../shared/table/Table'
import { useCallback, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { formatPhoneNumber, getDataFromLocalStorage, notify } from '../../../../shared/helpers/util'
import { getClients } from '../../../../logic/apis/client'
import ProfileInfo from '../../../../shared/components/profileInfo/ProfileInfo'
import { StorageKey } from '../../../../shared/helpers/constants'

const DeletedClient = () => {
  interface I_Data {
    clientName: string
    status: string
    address: string
    phone: string
    email: string
  }

  const [loading, setLoading] = useState<boolean>(false)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [showCreateDepartmentPopUp, setShowCreateDepartmentPopUp] = useState<boolean>(false)
  const [showEditDepartmentPopUp, setShowEditDepartmentPopUp] = useState<boolean>(false)
  const [departmentData, setDepartmentData] = useState<any>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)
  const navigate = useNavigate()

  const loadmoreRef = useRef(null)

  const fetchData = useCallback(async ({ pageSize, pageIndex, search }: any) => {
    try {
      // This will get called when the table needs new data
      setLoading(true)
      let receivedData: any = []
      let currentCompanyData: any = localStorage.getItem('currentCompany')

      const clientResponse = await getClients({ deleted: true, search, limit: pageSize }, true)

      if (clientResponse?.data?.statusCode === 200) {
        let statusRes = clientResponse?.data?.data?.client
        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            clientName: res?.name?.trim(),
            address: res?.street,
            phone: formatPhoneNumber(res?.phone, ''),
            email: res?.email,
            clientId: res._id,
            isDeleted: true,
            name: res?.name?.trim(),
          })
        })
      } else {
        notify(clientResponse?.data?.message, 'error')
      }

      // Give this fetch an ID
      const fetchId = ++fetchIdRef.current

      // Set the loading state
      // setLoading(true)

      // We'll even set a delay to simulate a server here
      // setTimeout(() => {
      // Only update the data if this is the latest fetch
      if (fetchId === fetchIdRef.current) {
        const startRow = pageSize * pageIndex
        const endRow = startRow + pageSize
        setData(receivedData.slice(startRow, endRow))

        // Your server could send back total page count.
        // For now we'll just fake it, too
        // setPageCount(Math.ceil(receivedData.length / pageSize))
        // setLoading(false)
      }
      // }, 1000)
    } catch (error) {
      console.error('TeamTable fetchData error', error)
    } finally {
      setLoading(false)
    }
  }, [])

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => <ProfileInfo data={props?.row?.original} showImagePlaceholder />,
      },
      {
        Header: 'Address',
        accessor: 'address',
      },
      {
        Header: 'Phone',
        accessor: 'phone',
      },
    ],
    []
  )

  return (
    <>
      <Table
        columns={columns}
        data={data}
        loading={loading}
        // pageCount={pageCount}
        fetchData={fetchData}
        client={true}
        ref={loadmoreRef}
        isLoadMoreLoading={loading}
      />
    </>
  )
}

export default DeletedClient
