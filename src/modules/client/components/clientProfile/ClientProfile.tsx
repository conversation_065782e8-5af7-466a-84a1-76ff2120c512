import { Field, FieldArray, Form, Formik } from 'formik'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'

import {
  deleteClient,
  getClientById,
  getClients,
  getOpportunitiesForClient,
  migrateClient,
  permanentDeleteClient,
  restoreClient,
  updateClient,
} from '../../../../logic/apis/client'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import Button from '../../../../shared/components/button/Button'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { Dropdown } from '../../../../shared/dropDown/Dropdown'
import { StorageKey, usStatesShortNames } from '../../../../shared/helpers/constants'
import { onlyNumber, onlyText, onlyTextWithSpaces, onlyUsNumber } from '../../../../shared/helpers/regex'
import {
  formatPhoneNumber,
  getDataFromLocalStorage,
  getDigitsFromPhone,
  getEnumValue,
  getIdFromName,
  getKeysFromObjects,
  getNameFrom_Id,
  isSuccess,
  notify,
  splitFullName,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { AddressWrap } from '../../style'
import { AddCityModal } from '../addCityModal/AddCityModal'
import * as Styled from './style'
import { getCampaigns, getLeadSources } from '../../../../logic/apis/leadSource'
import Toggle from '../../../../shared/toggle/Toggle'
import { getReferres } from '../../../../logic/apis/company'
import '../../../../shared/helpers/yupExtension'
import { I_Contacts, mergeSourceAndCampaignNames } from '../addNewClientModal/AddNewClientModal'
import Contacts from '../addNewClientModal/components/Contacts'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { getFormattedLeadSrcData, getLeadSrcDropData, getLeadSrcDropdownId } from '../../../leadSource/LeadSource'
import useFetch from '../../../../logic/apis/useFetch'
import AutoCompleteIndentation from '../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  firstName: string
  lastName: string
  city: string
  street: string
  state: string
  zip: string
  phone: string
  email: string
  contacts: I_Contacts[]
  leadSourceName: string
  referredBy: string
  notes: string
  isBusiness: boolean
  // businessName?: string
  fullName?: string
}

interface I_Data {
  name: string
  project: string
  price: string
  workType: string
  property: string
  value: string
}

const ClientProfile = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    firstName: '',
    lastName: '',
    city: '',
    street: '',
    state: '',
    zip: '',
    phone: '',
    contacts: [
      // {
      //   email: '',
      //   firstName: '',
      //   lastName: '',
      //   phone: '',
      //   notes: '',
      // },
    ],
    email: '',
    leadSourceName: '',
    referredBy: '',
    notes: '',
    isBusiness: false,
    // businessName: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [permanentdeleteLoading, setPermanentDeleteLoading] = useState<boolean>(false)
  const [unDeleteLoading, setUnDeleteLoading] = useState<boolean>(false)
  // const [lat, setLat] = useState('')
  // const [long, setLong] = useState('')
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [refererres, setRefererres] = useState<any>([])

  const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)
  const [showOpp, setShowOpp] = useState<boolean>(false)
  const [showEditAddress, setShowEditAddress] = useState(false)
  const [leadsrcDrop, setLeadsrcDrop] = useState([])
  const [leadSrcData, setLeadSrcData] = useState([])

  const [opportunityData, setOpportunityData] = useState([])
  // const [clientFirstName, setClientFirstName] = useState('')
  // const [duration, setDuration] = useState(0)
  // const [distance, setDistance] = useState(0)
  const [mergeModal, setMergeModal] = useState(false)
  const [matchingClients, setMatchingClients] = useState<any[]>([])
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null)
  const [isBusiness, setIsBusiness] = useState(initialValues?.isBusiness)
  const [pendingClientData, setPendingClientData] = useState<InitialValues | null>(null)

  const { clientId, isDeleted } = useParams()
  const globalSelector = useSelector((state: any) => state)

  const { currentCompany, currentMember, companySettingForAll } = globalSelector.company

  useEffect(() => {
    setIsBusiness(initialValues?.isBusiness)
  }, [initialValues?.isBusiness])

  useEffect(() => {
    if (leadSrcData?.length) {
      const data = getLeadSrcDropData(leadSrcData)
      setLeadsrcDrop(data)
    }
  }, [leadSrcData?.length])
  /**
   * ClientProfileSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ClientProfileSchema = Yup.object().shape({
    // firstName: Yup.string().when('businessName', (value, schema) => {
    //   if (value && isBusiness) return schema.optional()
    //   return schema.min(1, 'Too Short!').max(50, 'Too Long!').required('Required').matches(onlyText, 'Enter Valid Name')
    // }),
    firstName: Yup.string().required('Required'),
    lastName: Yup.string().min(1, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    leadSourceName: Yup.string(),
    notes: Yup.string(),
    street: Yup.string(),
    city: Yup.string(),
    state: Yup.string(),
    zip: Yup.string().matches(onlyNumber, 'Must be a number'),
    phone: Yup.string().required(),
    email: Yup.string().trimEmail().email('Invalid email'),
    // businessName: Yup.string()
    //   .min(2, 'Too Short!')
    //   .max(50, 'Too Long!')
    //   .matches(onlyText, 'Enter Valid Name')
    //   .required('Required'),
  })

  const [tableLoading, setTableLoading] = useState(false)
  const navigate = useNavigate()

  const getOpportunities = async () => {
    setTableLoading(true)
    try {
      const res = await getOpportunitiesForClient(clientId!)
      setOpportunityData(res?.data?.data?.clientOpps)
    } catch (error) {
      console.error('Error=====>', error)
    } finally {
      setTableLoading(false)
    }
  }

  useEffect(() => {
    initFetchReferrers()
  }, [])

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, true)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: (row: any) => `${row?.clientId?.firstName} ${row?.clientId?.lastName || ''}`,
      },
      {
        Header: 'PO#',
        accessor: (row: any) => `${row?.PO}-${row?.num}`,
      },
      {
        Header: 'Price',
        accessor: (row: any) => `${row?.price ?? '----'}`,
      },
      {
        Header: 'Work Type',
        accessor: (row: any) => row?.oppType?.name,
      },
      {
        Header: 'Property',
        accessor: 'street',
        Cell: (props: any) => {
          return (
            <AddressWrap>
              {props?.row?.original?.street ? <p>{props?.row?.original?.street},</p> : null}
              {props?.row?.original?.city ? (
                <p>
                  {props?.row?.original?.city}, {props?.row?.original?.state} {props?.row?.original?.zip}
                </p>
              ) : null}
            </AddressWrap>
          )
        },
      },
      {
        Header: 'Value',
        accessor: (row: any) => `${row?.value ?? '----'}`,
      },
    ],
    []
  )

  useEffect(() => {
    window?.scrollTo(0, 0)
    getOpportunities()
  }, [])

  const deleteClientFunc = async () => {
    try {
      setDeleteLoading(true)
      let clId: any = clientId
      let dataObj = {
        id: clId,
      }
      let response = await deleteClient(dataObj)
      if (response?.data?.statusCode === 204) {
        notify('Client Profile Deleted Successfully', 'success')
        setDeleteLoading(false)
        navigate(`/client`)
      } else {
        setDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteClientFunc error', error)
    }
  }

  const permanentDeleteClientFunc = async () => {
    try {
      setPermanentDeleteLoading(true)
      let clId: any = clientId
      let dataObj = {
        id: clId,
      }
      let response = await permanentDeleteClient(dataObj)
      if (response?.data?.statusCode === 204) {
        notify('Client Profile Deleted Permanently', 'success')
        setPermanentDeleteLoading(false)
        navigate(`/client`)
      } else {
        setPermanentDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteClientFunc error', error)
    }
  }

  const restoreClientFunc = async () => {
    try {
      setUnDeleteLoading(true)
      let clId: any = clientId
      let dataObj = {
        id: clId,
      }
      let response = await restoreClient(dataObj)
      if (response?.data?.statusCode === 200) {
        notify('Client Profile Restored Successfully', 'success')
        setUnDeleteLoading(false)
        navigate(`/client`)
      } else {
        setUnDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteClientFunc error', error)
    }
  }

  const getClientDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let clId: any = clientId
      let isDeletedValue: any = isDeleted
      const clientResponse = await getClientById({ deleted: isDeletedValue, clientId: clId }, isDeletedValue)

      if (clientResponse?.data?.statusCode === 200) {
        let statusRes = clientResponse?.data?.data?.client
        let dataObj = {
          ...statusRes,
          // firstName: statusRes?.isBusiness ? statusRes?.fullName : statusRes?.firstName,
        }
        delete dataObj.updatedAt
        delete dataObj.createdBy
        delete dataObj.createdAt
        delete dataObj.__v
        delete dataObj.companyId
        delete dataObj.deleted
        delete dataObj._id

        setInitialValues({
          ...dataObj,
          referredBy: getNameFrom_Id(dataObj?.referredBy, refererres),
          // leadSourceName: getNameFrom_Id(dataObj?.leadSource, leadsrcDrop),
          leadSourceName: statusRes?.campaignName || statusRes?.leadSourceName,
        })
      } else {
        notify(clientResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
    } finally {
      setShimmerLoading(false)
    }
  }

  useEffect(() => {
    if (refererres.length && leadsrcDrop.length && leadSrcData.length) {
      getClientDetails()
    }
  }, [dataUpdate, refererres, leadsrcDrop, leadSrcData])

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  useEffect(() => {
    getLeadSrcData()
  }, [])

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      const result = getLeadSrcDropdownId(submittedValues?.leadSourceName, leadSrcData)

      // const leadsourceId = getIdFromName(submittedValues?.leadSourceName, leadsrcDrop)
      const leadsourceId = result?.leadSourceId
      const campaignId = result?.campaignId || null
      const campaignName = result?.campaignName || null
      const referredById =
        leadsourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
          ? getIdFromName(submittedValues?.referredBy, refererres)
          : null

      setPendingClientData(submittedValues)
      setLoading(true)
      let clId: any = clientId
      let phone = getDigitsFromPhone(submittedValues?.phone)
      let dataObj = {
        ...submittedValues,
        isBusiness,
        firstName: submittedValues?.firstName?.trim(),
        lastName: isBusiness ? '' : submittedValues?.lastName?.trim(),
        phone,
        leadSource: leadsourceId,
        createdBy: currentMember._id,
        clientId: clId,
        referredBy: referredById,
        campaignId,
        campaignName,
      }
      console.log({ dataObj })

      if (submittedValues?.firstName && submittedValues?.lastName) {
        const existingClients = await getClients(
          {
            deleted: false,
            limit: '20',
            search: `${submittedValues.firstName.trim()} ${submittedValues?.lastName?.trim()}`?.trim(),
          },
          false
        )

        if (isSuccess(existingClients)) {
          const duplicates = existingClients?.data?.data?.client?.filter(
            (client: any) =>
              client?.firstName?.toLowerCase()?.trim() === submittedValues?.firstName?.toLowerCase()?.trim() &&
              client?.lastName?.toLowerCase()?.trim() === submittedValues?.lastName?.toLowerCase()?.trim() &&
              client._id !== clId
          )
          if (duplicates.length > 0) {
            setMatchingClients(duplicates)
            setMergeModal(true) // Open the modal to show options
            setLoading(false)
            return
          }
        }
      } else if (submittedValues?.firstName && isBusiness) {
        const existingClients = await getClients(
          {
            deleted: false,
            limit: '20',
            search: `${submittedValues.firstName.trim()}`,
          },
          false
        )
        if (isSuccess(existingClients)) {
          const duplicates = existingClients?.data?.data?.client?.filter(
            (client: any) =>
              client?.firstName?.toLowerCase()?.trim() === submittedValues?.firstName?.toLowerCase()?.trim() &&
              client._id !== clId &&
              client.isBusiness
          )
          if (duplicates.length > 0) {
            setMatchingClients(duplicates)
            setMergeModal(true) // Open the modal to show options
            setLoading(false)
            return
          }
        }
      }

      let response = await updateClient(dataObj)
      if (response?.data?.statusCode === 200) {
        notify('Client Profile Edited Successfully', 'success')
        setLoading(false)
      } else {
        setLoading(false)
      }
    } catch (error) {
      console.error('Client Profile handleSubmit', error)
      setLoading(false)
    }
  }

  const handleContinueUpdate = async () => {
    setMergeModal(false) // Close the modal
    if (!pendingClientData) return // Ensure we have data to update

    setLoading(true)
    let phone = getDigitsFromPhone(pendingClientData?.phone)
    let leadsourceId = getIdFromName(pendingClientData?.leadSourceName, leadsrcDrop)
    let referredById =
      leadsourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
        ? getIdFromName(pendingClientData?.referredBy, refererres)
        : undefined

    let dataObj = {
      ...pendingClientData,
      isBusiness,
      firstName: pendingClientData?.firstName?.trim(),
      lastName: isBusiness ? '' : pendingClientData?.lastName?.trim(),
      phone,
      leadSource: leadsourceId,
      createdBy: currentMember._id,
      clientId: clientId,
      referredBy: referredById,
    }

    try {
      let response = await updateClient(dataObj)
      if (response?.data?.statusCode === 200) {
        notify('Client Profile Edited Successfully', 'success')
      }
    } catch (error) {
      console.error('Error updating client', error)
    } finally {
      setLoading(false)
    }
  }

  const handleMergeConfirm = async () => {
    try {
      const res = await migrateClient(clientId!, selectedClientId!)
      if (isSuccess(res)) {
        notify('Client Profile Merged Successfully', 'success')
        navigate(`/client`)
      }
    } catch (error) {
    } finally {
      setMergeModal(false)
      setLoading(false)
    }
  }

  return (
    <>
      {shimmerLoading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={ClientProfileSchema}
          enableReinitialize={true}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ errors, touched, values, setFieldValue, handleChange }) => {
            // useEffect(() => {
            //   if (values?.fullName) {
            //     const { firstName, lastName } = splitFullName(values?.businessName || values?.fullName)
            //     setFieldValue('firstName', firstName)
            //     setFieldValue('lastName', lastName)
            //     isBusiness && setFieldValue('businessName', `${firstName} ${lastName}`)
            //     // isBusiness && setClientFirstName(firstName)
            //   }
            // }, [values?.fullName, isBusiness])

            useEffect(() => {
              if (isBusiness) {
                if (values?.firstName !== '' || values?.lastName !== '')
                  setFieldValue('firstName', `${values?.firstName} ${values?.lastName || ''}`)
                setFieldValue('lastName', '')
              } else {
                const { firstName, lastName } = values?.lastName
                  ? splitFullName(`${values?.firstName} ${values?.lastName}`)
                  : splitFullName(values?.firstName)
                setFieldValue('firstName', firstName)
                setFieldValue('lastName', lastName)
              }
            }, [isBusiness])

            console.log({ errors, values })
            return (
              <Styled.ClientProfileContainer>
                <Form className="form">
                  <SharedStyled.Content width="100%" gap="10px" disableBoxShadow={true} noPadding={true}>
                    <SharedStyled.SectionTitle>Client Profile</SharedStyled.SectionTitle>

                    <Toggle
                      title="Business"
                      isToggled={isBusiness}
                      onToggle={() => {
                        setIsBusiness((prev) => !prev)
                      }}
                    />
                    {isBusiness ? (
                      <InputWithValidation
                        labelName="Business Name*"
                        stateName="firstName"
                        error={touched.firstName && errors.firstName ? true : false}
                      />
                    ) : (
                      <SharedStyled.TwoInputDiv>
                        <InputWithValidation
                          labelName="Primary First Name*"
                          stateName="firstName"
                          error={touched.firstName && errors.firstName ? true : false}
                        />
                        <InputWithValidation
                          labelName="Primary Last Name"
                          stateName="lastName"
                          error={touched.lastName && errors.lastName ? true : false}
                        />
                      </SharedStyled.TwoInputDiv>
                    )}

                    <SharedStyled.TwoInputDiv>
                      <SharedPhone
                        labelName="Primary Phone*"
                        stateName="phone"
                        onChange={handleChange('phone')}
                        value={values.phone}
                        error={touched.phone && errors.phone ? true : false}
                      />

                      <InputWithValidation
                        labelName="Primary Email"
                        stateName="email"
                        error={touched.email && errors.email ? true : false}
                      />
                    </SharedStyled.TwoInputDiv>

                    <FieldArray name="contacts">
                      {({ remove, push }) => (
                        <>
                          {values.contacts?.length > 0 && (
                            <>
                              <SharedStyled.FlexCol margin="10px 0 0 0">
                                <SharedStyled.Text
                                  color={`${colors.darkGrey}`}
                                  textAlign="left"
                                  fontWeight="bold"
                                  fontSize="16px"
                                >
                                  Other Contacts
                                </SharedStyled.Text>
                                {values.contacts?.map((data: any, index: number) => (
                                  <>
                                    <Contacts
                                      values={values?.contacts}
                                      remove={remove}
                                      index={index}
                                      data={data}
                                      setFieldValue={setFieldValue}
                                      handleChange={handleChange}
                                    />
                                  </>
                                ))}
                              </SharedStyled.FlexCol>
                            </>
                          )}
                          <SharedStyled.ButtonContainer justifyContent="start" margin="24px 0 16px 0">
                            <Button
                              type="button"
                              className="fit outline"
                              onClick={() =>
                                push({
                                  email: '',
                                  firstName: '',
                                  lastName: '',
                                  phone: '',
                                  notes: '',
                                })
                              }
                            >
                              Add Contact
                            </Button>
                          </SharedStyled.ButtonContainer>
                        </>
                      )}
                    </FieldArray>

                    {/* <CustomSelect
                      dropDownData={leadsrcDrop?.map((item: any) => item?.name)}
                      setValue={() => {}}
                      stateName="leadSourceName"
                      value={values.leadSourceName}
                      error={touched.leadSourceName && errors.leadSourceName ? true : false}
                      setFieldValue={setFieldValue}
                      labelName="Lead Source*"
                      innerHeight="52px"
                      margin="10px 0 0 0"
                      showInitialValue
                    /> */}

                    {leadSrcData?.length ? (
                      <AutoCompleteIndentation
                        labelName="Lead Source*"
                        stateName={`leadSourceName`}
                        isLeadSource
                        dropdownHeight="180px"
                        error={touched.leadSourceName && errors.leadSourceName ? true : false}
                        borderRadius="0px"
                        setFieldValue={setFieldValue}
                        options={mergeSourceAndCampaignNames(leadSrcData)}
                        formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                        value={values.leadSourceName!}
                        setValueOnClick={(val: string) => {
                          setFieldValue('leadSourceName', val)
                        }}
                        className="material-autocomplete"
                        isIndentation={true}
                      />
                    ) : null}

                    {values.leadSourceName === 'Referral' && (
                      <SharedStyled.FlexBox width="100%" justifyContent="end">
                        <CustomSelect
                          labelName="Referrer"
                          stateName="referredBy"
                          error={touched.referredBy && errors.referredBy ? true : false}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          value={values.referredBy}
                          dropDownData={getKeysFromObjects(refererres, 'name')}
                          innerHeight="52px"
                          className="top"
                          maxWidth="95%"
                        />
                      </SharedStyled.FlexBox>
                    )}

                    <SharedStyled.FlexRow justifyContent="flex-start" alignItems="flex-start">
                      <div style={{ width: '100%' }}>
                        <SharedStyled.FlexRow>
                          <b>Address - </b>
                          <p
                            className="link"
                            onClick={() => {
                              setShowEditAddress(!showEditAddress)
                            }}
                          >
                            {showEditAddress ? 'Confirm' : 'Edit'}
                          </p>
                        </SharedStyled.FlexRow>

                        {showEditAddress ? (
                          <AutoCompleteAddress
                            setFieldValue={setFieldValue}
                            street={'street'}
                            city={'city'}
                            state={'state'}
                            zip={'zip'}
                            sourceAddress={companySettingForAll?.address}
                            companyLatLong={companySettingForAll}
                            // setLat={setLat}
                            // setLong={setLong}
                            // setDistance={setDistance}
                            // setDuration={setDuration}
                          />
                        ) : (
                          <AddressWrap className="font">
                            <p>{values?.street}</p>
                            <p>
                              {values?.city}, {values.state} {values.zip}
                            </p>
                          </AddressWrap>
                        )}
                      </div>
                    </SharedStyled.FlexRow>

                    {/* <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Notes
                    </Styled.LabelDiv> */}
                    <Styled.TextArea
                      component="textarea"
                      placeholder="Notes"
                      as={Field}
                      name="notes"
                      margintop="8px"
                      height="52px"
                    ></Styled.TextArea>

                    <SharedStyled.ButtonContainer marginTop="20px" justifyContent="flex-start">
                      <Button type="submit" isLoading={loading} width="max-content">
                        Save Changes
                      </Button>
                      {isDeleted === 'false' && !opportunityData?.length && (
                        <Button
                          type="button"
                          className="delete"
                          width="max-content"
                          isLoading={deleteLoading}
                          onClick={() => deleteClientFunc()}
                        >
                          Make Inactive
                        </Button>
                      )}
                      {isDeleted === 'true' && (
                        <>
                          <Button
                            type="button"
                            width="max-content"
                            bgColor={colors.green}
                            onClick={() => restoreClientFunc()}
                            isLoading={unDeleteLoading}
                          >
                            Un-Delete Client
                          </Button>
                          <Button
                            type="button"
                            width="max-content"
                            className="delete"
                            onClick={() => permanentDeleteClientFunc()}
                            isLoading={permanentdeleteLoading}
                          >
                            Permanently Delete Client
                          </Button>
                        </>
                      )}
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>

                <Styled.OpportunityContainer marginTop="50px" width="100%">
                  <SharedStyled.FlexBox width="100%" alignItems="flex-start" gap="5px">
                    <SharedStyled.SectionTitle>Opportunities</SharedStyled.SectionTitle>
                  </SharedStyled.FlexBox>
                  <Table
                    columns={columns}
                    data={opportunityData}
                    loading={tableLoading}
                    // pageCount={pageCount}
                    fetchData={() => {}}
                    noSearch={true}
                    onRowClick={(val) => {
                      navigate(`/${getEnumValue(val?.stage?.stageGroup)}/opportunity/${val?._id}`)
                    }}
                  />
                </Styled.OpportunityContainer>
              </Styled.ClientProfileContainer>
            )
          }}
        </Formik>
      )}

      <CustomModal show={showAddCityModal}>
        <AddCityModal setShowAddCityModal={setShowAddCityModal} action="Add City" setDetailsUpdate={setDataUpdate} />
      </CustomModal>

      <CustomModal show={mergeModal} toggleModal={() => setMergeModal(false)}>
        <SharedStyled.ModalContainer style={{ width: '700px' }}>
          <SharedStyled.ModalHeaderContainer>
            <SharedStyled.FlexRow>
              <img src={UnitSvg} alt="modal icon" />
              <SharedStyled.FlexCol>
                <SharedStyled.ModalHeader>Merge Client</SharedStyled.ModalHeader>
              </SharedStyled.FlexCol>
            </SharedStyled.FlexRow>
          </SharedStyled.ModalHeaderContainer>

          <SharedStyled.SettingModalContentContainer>
            <SharedStyled.FlexBox width="100%" flexDirection="column" gap="10px">
              <SharedStyled.Text fontSize="16px">
                There are existing clients with the same name, would you like to merge this client into the client with
                that name?
                <br /> <br /> Select a client to merge into:
              </SharedStyled.Text>
              {matchingClients?.map((client) => (
                <SharedStyled.FlexRow>
                  <SharedStyled.CheckboxZoneLabel
                    margin={0}
                    key={client._id}
                    style={{ display: 'block', margin: '8px 0' }}
                  >
                    <input
                      type="radio"
                      name="mergeClient"
                      value={client._id}
                      onChange={(e) => setSelectedClientId(e.target.value)}
                    />
                  </SharedStyled.CheckboxZoneLabel>

                  <SharedStyled.FlexCol gap="5px" margin="10px 0 10px 10px">
                    <SharedStyled.Text fontWeight="700" margin="0 0 0 20px" fontSize="14px">
                      {client?.firstName} {client?.lastName || ''}
                    </SharedStyled.Text>
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {client?.street}
                    </SharedStyled.Text>
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {client?.city}, {client?.state} {client?.zip}
                    </SharedStyled.Text>
                  </SharedStyled.FlexCol>

                  <SharedStyled.FlexCol gap="5px" margin="10px 0 10px 10px">
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {formatPhoneNumber(client?.phone, '')}
                    </SharedStyled.Text>
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {client?.email}
                    </SharedStyled.Text>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
              ))}

              <SharedStyled.Warning>
                Note: opportunities from the current client will be migrated to the selected client.
              </SharedStyled.Warning>
              <SharedStyled.FlexBox gap="10px">
                <Button onClick={handleMergeConfirm} disabled={!selectedClientId}>
                  Confirm Merge
                </Button>
                <Button onClick={handleContinueUpdate}>Cancel</Button>
              </SharedStyled.FlexBox>
            </SharedStyled.FlexBox>
          </SharedStyled.SettingModalContentContainer>
        </SharedStyled.ModalContainer>
      </CustomModal>
    </>
  )
}

export default ClientProfile
