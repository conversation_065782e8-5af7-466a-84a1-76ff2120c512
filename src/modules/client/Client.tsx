import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

import { getClients } from '../../logic/apis/client'
import { getReferres } from '../../logic/apis/company'
import Button from '../../shared/components/button/Button'
import ProfileInfo from '../../shared/components/profileInfo/ProfileInfo'
import TabBar from '../../shared/components/tabBar/TabBar'
import { CustomModal } from '../../shared/customModal/CustomModal'
import FilterSvg from '../../assets/newIcons/filter.svg'

import { formatPhoneNumber, getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import ReferrerModal from '../Refferer/components/referrerModal/ReferrerModal'
import { ButtonCont, SettingsCont } from '../units/style'
import { AddCityModal } from './components/addCityModal/AddCityModal'
import { AddNewClientModal } from './components/addNewClientModal/AddNewClientModal'
import DeletedClient from './components/deletedClient/DeletedClient'
import * as Styled from './style'
import { StorageKey } from '../../shared/helpers/constants'
import { DropdownContainer, DropdownContent } from '../../shared/dropdownWithCheckboxes/style'
import { FilterContainer } from '../operations/style'
import CheckboxList from '../track/components/CheckboxList'
import { getSelectedFilters, handleFilterChange } from '../reports/productionReport/ProductionReport'

interface I_Data {
  clientName: string
  status: string
  address: string
  phone: string
  email: string
}

const typeDropdown = [
  {
    name: 'Client',
    _id: 'Client',
  },
  {
    name: 'Prospect',
    _id: 'Prospect',
  },
]

const Client = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [refererres, setRefererres] = useState<any>([])
  const [referrerModal, setShowReferrerModal] = useState(false)
  const [referrerValue, setReferrerValue] = useState<any>([])
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [showFilter, setShowFilter] = useState(false)
  const [statusSelected, setStatusSelected] = useState({})
  const statusFilters = getSelectedFilters(statusSelected)
  const [tabIndex, setTabIndex] = useState(0)

  // const [pageCount, setPageCount] = useState<number>(10)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)
  const navigate = useNavigate()

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  const loadMoreRef = useRef(null)
  const [referrerDropdownData, setReferrerDropdownData] = useState<any>([])

  useEffect(() => {
    initFetchReferrers()
  }, [])

  useEffect(() => {
    const filterWithNameAndSymbol = refererres?.map((item: any) => item.name)
    filterWithNameAndSymbol.push('--Add New--')
    setReferrerDropdownData(filterWithNameAndSymbol)
  }, [refererres])

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, true)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }
  useEffect(() => {
    if (referrerValue === '--Add New--') {
      setShowReferrerModal(true)
    }
  }, [referrerValue])

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const clientResponse = await getClients(
          {
            deleted: false,
            limit: pageSize,
            search,
            status: statusFilters?.length ? statusFilters?.join(',') : undefined,
          },
          false
        )

        if (clientResponse?.data?.statusCode === 200) {
          let statusRes = clientResponse?.data?.data?.client
          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              clientName: res?.name,
              businessName: res?.businessName,
              street: res?.street,
              city: res?.city,
              state: res?.state,
              zip: res?.zip,
              phone: formatPhoneNumber(res?.phone, ''),
              email: res?.email,
              clientId: res._id,
              leadSource: res.leadSourceName,
              isBusiness: res?.isBusiness,
              isDeleted: false,
              name: res?.name,
              status: res?.status,
            })
          })
        } else {
          notify(clientResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state
        // setLoading(true)

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
          // setLoading(false)
        }
        // }, 1000)
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate, statusFilters?.length]
  )

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => <ProfileInfo data={props?.row?.original} showImagePlaceholder />,
      },

      {
        Header: 'Address',
        accessor: 'address',
        Cell: (props: any) => {
          return (
            <Styled.AddressWrap>
              {props?.row?.original?.street ? <p>{props?.row?.original?.street},</p> : null}

              <p>
                {props?.row?.original?.city ? `${props?.row?.original?.city},` : null} {props?.row?.original?.state}{' '}
                {props?.row?.original?.zip}
              </p>
            </Styled.AddressWrap>
          )
        },
      },
      {
        Header: 'Phone',
        accessor: 'phone',
      },
      {
        Header: 'Lead Source',
        accessor: 'leadSource',
      },
      {
        Header: 'Status',
        accessor: 'status',
      },
    ],
    []
  )

  return (
    <SettingsCont gap="24px">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Clients</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setShowAddNewClientModal(true)
            }}
          >
            Add Client
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            onTabChange={setTabIndex}
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <Table
                    columns={columns}
                    data={data}
                    loading={loading}
                    // pageCount={pageCount}
                    fetchData={fetchData}
                    client={true}
                    ref={loadMoreRef}
                    isLoadMoreLoading={loading}
                  />
                ),
              },
              {
                title: 'Inactive',
                render: () => <DeletedClient />,
              },
            ]}
            filterComponent={
              <>
                {tabIndex ? null : (
                  <DropdownContainer ref={dropdownRef} className="filter client-filter">
                    <img
                      src={FilterSvg}
                      className="filter-icon"
                      alt="filter icon"
                      style={{ width: '20px', cursor: 'pointer' }}
                      onClick={() => setShowFilter((p) => !p)}
                    />

                    {showFilter ? (
                      <DropdownContent
                        style={{ width: '230px', maxHeight: '500px', overflowY: 'scroll', right: '0px' }}
                      >
                        <h3>Filter by</h3>
                        <FilterContainer
                          margin="10px 0 0 0"
                          gap="0px"
                          justifyContent="flex-start"
                          onClick={(e) => e.stopPropagation()}
                          className="media-filter"
                        >
                          <CheckboxList
                            className="first"
                            title=""
                            data={[]}
                            checkedItems={{}}
                            onSelectionChange={(_val) => {
                              setStatusSelected([])
                            }}
                            allText="All"
                            isCheckedAll={!statusFilters?.length}
                          />
                          <CheckboxList
                            title="Status"
                            className="first"
                            data={typeDropdown}
                            checkedItems={statusSelected}
                            onSelectionChange={(val) => {
                              handleFilterChange(val, setStatusSelected)
                            }}
                            hideAllCheckbox
                          />
                        </FilterContainer>
                      </DropdownContent>
                    ) : null}
                  </DropdownContainer>
                )}
              </>
            }
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={addNewClientModal}>
        <AddNewClientModal
          setShowAddNewClientModal={setShowAddNewClientModal}
          setDetailsUpdate={setDetailsUpdate}
          detailsUpdate={detailsUpdate}
          setShowAddCityModal={setShowAddCityModal}
          onClose={() => {
            setShowAddNewClientModal(false)
          }}
          referrerDropdownData={referrerDropdownData}
          refererres={refererres}
          setReferrerValue={setReferrerValue}
        />
      </CustomModal>
      <CustomModal show={referrerModal}>
        <ReferrerModal
          onClose={() => {
            setShowReferrerModal(false)
            // setEditReferrerVals(null)
          }}
          onComplete={() => {
            initFetchReferrers()
          }}
        />
      </CustomModal>
      <CustomModal show={showAddCityModal}>
        <AddCityModal setShowAddCityModal={setShowAddCityModal} action="Add City" setDetailsUpdate={setDetailsUpdate} />
      </CustomModal>
    </SettingsCont>
  )
}

export default Client
