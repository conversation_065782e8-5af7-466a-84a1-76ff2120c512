import { Field, Form, Formik } from 'formik'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import * as Yup from 'yup'

import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { getProjectTypes } from '../../../../logic/apis/projects'
import { createStep, updateStep } from '../../../../logic/apis/sales'
import Checkbox from '../../../../shared/checkbox/Checkbox'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { onlyNumber, onlyText, onlyTextAndNumbers, onlyTextWithSpaces } from '../../../../shared/helpers/regex'
import {
  getDataFromLocalStorage,
  getParentIdFromName,
  getParentNameFromId,
  isSuccess,
  notify,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import { I_Step } from '../../CrmSettings'
import * as Styled from './styles'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'
import useFetch from '../../../../logic/apis/useFetch'
import { getCompanyAllForms } from '../../../../logic/apis/form'

interface I_NewStepModal {
  stageId: string
  onClose: any
  lastSequence: number
  onSuccess: any
  stepData?: I_Step | null
  allSteps: string[]
  steps?: any
  allStepsForSequence?: any
  stepAction?: string
  allStageData?: any[]
}

interface I_initialValues {
  name: string
  fieldType: string
  formType: string | undefined
  lable: string | undefined
  dropDownOptions: string[]
  isDisplay: boolean
  dropdownField: string
  parent: string
  isRequire: boolean
  activityType: string
  // location: string[]
  singleState: string
  description: string
  // projectType: string[]
  singleProject: string
  stageId?: string
}

const NewStepModal: React.FC<I_NewStepModal> = (props) => {
  const {
    onClose,
    stageId,
    lastSequence,
    onSuccess,
    stepData,
    allSteps,
    allStageData,
    steps,
    allStepsForSequence,
    stepAction,
  } = props

  const [projectTypesDrop, setProjectTypesDrop] = useState<Record<string, string>>({})
  const [selectedStates, setSelectedStates] = useState<string[]>(
    stepData?.location?.length > 0 ? stepData?.location : stepAction === 'New' ? [] : ['All']
  )

  const { data: formsData } = useFetch({
    fetchFn: () => getCompanyAllForms({ whereIsUse: 'all' }),
  })

  const isParentStep = !stepData?.parent

  const [selectedProject, setSelectedProject] = useState<string[]>([])

  const initialValues: I_initialValues = {
    name: stepData ? stepData.name : '',
    fieldType: stepData ? stepData.fieldType : '',
    formType: stepData ? stepData.formType : '',
    lable: stepData ? stepData.lable : '',
    dropDownOptions: stepData ? stepData.dropDownOptions : [],
    isDisplay: stepData ? stepData.isDisplay : false,
    dropdownField: stepData ? stepData.dropdownField : '',
    parent: stepData ? getParentNameFromId(stepData.parent, steps) : '',
    isRequire: stepData ? stepData.isRequire : false,
    activityType: stepData?.activityType ? stepData.activityType : 'None',
    // location: stepData?.location ? stepData.location : [],
    singleState: '',
    stageId: allStageData?.find((itm) => itm?._id === stageId)?.name,
    description: stepData?.description ? stepData.description : '',
    // projectType: stepData?.projectTypeId
    //   ? Object.keys(projectTypesDrop).find((key) => projectTypesDrop[key] === stepData?.projectTypeId)
    //   : 'All',
    singleProject: '',
    // ...stepData,
  }

  const isUpdate = stepData ? true : false
  const [loading, setLoading] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, companySettingForAll } = globalSelector.company

  useEffect(() => {
    initFetchProjectType()
  }, [])

  const initFetchProjectType = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const result: Record<string, string> = {}
        for (const item of projectType) {
          result[item.name] = item._id
        }
        result['All'] = ''

        // setProjectTypes(projectType)

        setProjectTypesDrop(result)

        setSelectedProject(
          Object.keys(result).filter((key) => stepData?.projectTypeId.includes(result[key])).length > 0
            ? Object.keys(result).filter((key) => stepData?.projectTypeId.includes(result[key]))
            : stepAction === 'New'
            ? []
            : ['All']
        )
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)
    let response: any

    values.parent = getParentIdFromName(values.parent, steps)
    const filteredSteps = allStepsForSequence.filter((step: any) => step.parent !== '' && step.parent !== undefined)
    const filteredStepsById = filteredSteps.filter((step: any) => step.parent === values.parent)
    let projectTypeId: string[]
    if (selectedProject.includes('All')) {
      projectTypeId = []
    } else {
      // Map projectTypesFrop values based on selectedProject keys
      projectTypeId = selectedProject?.map((project) => projectTypesDrop[project])
    }
    let location: string[]
    if (selectedStates.includes('All')) {
      location = []
    } else {
      location = selectedStates
    }

    if (!stepData) {
      response = await createStep({
        ...values,
        stageId: stageId,
        formType:
          values.fieldType === 'Form'
            ? formsData?.forms?.find((form: any) => form?.name === values.formType)?._id
            : null,
        createdBy: currentMember._id!,
        sequence: values.parent !== '' ? filteredStepsById.length + 1 : lastSequence + 1,
        projectTypeId: projectTypeId,
        location: location,
        lable: values.lable === '' ? undefined : values.lable,
      })
    } else {
      const { _id, ...restData } = stepData
      restData.parent = values.parent

      response = await updateStep({
        ...restData,
        ...values,
        stepId: _id,
        formType:
          values.fieldType === 'Form'
            ? formsData?.forms?.find((form: any) => form?.name === values.formType)?._id
            : null,
        stageId: allStageData?.find((itm) => itm?.name === values?.stageId)?._id,
        createdBy: currentMember._id,
        projectTypeId: projectTypeId,
        location: location,
      })
    }

    setLoading(false)
    if (isSuccess(response)) {
      notify(`${isUpdate ? 'Updated' : 'Created new'} step!`, 'success')
      onSuccess()
      onClose()
    } else {
      notify(response.data.message ?? `Failed ${isUpdate ? 'Updation' : 'Creation!'} `, 'error')
      console.log('New step error', response)
    }
  }

  const handleAddDropdown = (setFieldValue: any, fields: Array<string>, newField: string) => {
    if (newField?.trim()) {
      setFieldValue('dropDownOptions', [...fields, newField])
      setFieldValue('dropdownField', '')
    }
  }
  const removeField = (setFieldValue: any, idx: number, fields: Array<string>) => {
    let newField = [...fields.slice(0, idx), ...fields.slice(idx + 1)]
    setFieldValue('dropDownOptions', [...newField])
  }

  const removeFieldMultiSelect = (cd: any, idx: number, fields: Array<any>) => {
    let newField = [...fields.slice(0, idx), ...fields.slice(idx + 1)]
    cd([...newField])
  }
  const newStepSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, 'Too Short!')
      .max(100, 'Too Long!')
      // .matches(onlyTextAndNumbers, 'Invalid characters.Only Alphanumeric And numeric characters are allowed.')
      .required('Required'),
    // .matches(onlyTextWithSpaces, 'Enter Valid Name'),
    fieldType: Yup.string().required('Required').matches(onlyText, 'Select Valid Name'),
    formType: Yup.string().when('fieldType', {
      is: 'Form',
      then: Yup.string().required('Required'),
    }),
    dropDownOptions: Yup.array().of(Yup.string()),
    dropdownField: Yup.string(),
    parent: Yup.string(),
    activityType: Yup.string(),
  })

  return (
    <Styled.StepModalContainer>
      <Formik
        key={formsData?.forms?.length}
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={newStepSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange, handleSubmit }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{stepAction === 'New' ? 'Add' : 'Edit'} Step</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <CustomSelect
                      dropDownData={allStageData?.map((itm) => itm?.name)!}
                      setValue={() => {}}
                      stateName="stageId"
                      value={values.stageId!}
                      error={touched.stageId && errors.stageId ? true : false}
                      setFieldValue={setFieldValue}
                      labelName="Stage"
                      disabled={!isParentStep}
                      innerHeight="52px"
                      margin="10px 0 0 0"
                    />
                    <InputWithValidation
                      labelName="Name*"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                      // disabled={stepAction !== 'New'}
                    />
                    <Styled.TextArea
                      component="textarea"
                      placeholder="Description"
                      as={Field}
                      name="description"
                      marginTop="8px"
                      height="52px"
                    ></Styled.TextArea>

                    <CustomSelect
                      dropDownData={['None', 'Form', 'Date', 'Text', 'Number', 'Currency', 'Dropdown']}
                      setValue={() => {}}
                      stateName="fieldType"
                      value={values.fieldType}
                      error={touched.fieldType && errors.fieldType ? true : false}
                      setFieldValue={setFieldValue}
                      labelName="Field*"
                      innerHeight="52px"
                      margin="10px 0 0 0"
                    />

                    {values.fieldType === 'Dropdown' ? (
                      <>
                        <SharedStyled.FlexRow
                          alignItems="center"
                          justifyContent="space-between"
                          gap="14px"
                          width="100%"
                        >
                          <InputWithValidation
                            labelName="Dropdown Field"
                            stateName="dropdownField"
                            value={values.dropdownField}
                          />
                          <Button
                            width="max-content"
                            type="button"
                            height="52px"
                            disabled={!values.dropdownField}
                            onClick={() =>
                              handleAddDropdown(setFieldValue, values.dropDownOptions, values.dropdownField)
                            }
                          >
                            Add
                          </Button>
                        </SharedStyled.FlexRow>
                        <Styled.OptionsWrapper>
                          {values?.dropDownOptions?.map((field, idx) => (
                            <div
                              className="option"
                              onClick={() => removeField(setFieldValue, idx, values.dropDownOptions)}
                            >
                              {field} <CrossIcon />
                            </div>
                          ))}
                        </Styled.OptionsWrapper>
                      </>
                    ) : null}

                    {values.fieldType === 'Form' && (
                      <CustomSelect
                        dropDownData={formsData?.forms?.map((form: any) => form?.name)}
                        setFieldValue={setFieldValue}
                        stateName="formType"
                        labelName="Choose Form*"
                        showInitialValue
                        value={
                          values.formType
                            ? formsData?.forms?.find((form: any) => form?._id === values.formType)?.name
                            : ''
                        }
                        margin="10px 0 0 0"
                        setValue={() => {}}
                        error={touched.formType && errors.formType ? true : false}
                      />
                    )}
                    {values.fieldType === 'Dropdown' ||
                    values.fieldType === 'Form' ||
                    values.fieldType === 'Date' ? null : (
                      <InputWithValidation
                        labelName="Label"
                        stateName="lable"
                        error={touched.lable && errors.lable ? true : false}
                      />
                    )}

                    {/* <CustomSelect
                      dropDownData={Object.keys(projectTypesDrop)}
                      setFieldValue={setFieldValue}
                      stateName="projectType"
                      labelName="Project Type"
                      value={values.projectType!}
                      margin="10px 0 0 0"
                      setValue={() => {}}
                      error={touched.projectType && errors.projectType ? true : false}
                    /> */}

                    <SharedStyled.FlexBox width="100%" gap="10px">
                      <CustomSelect
                        dropDownData={Object.keys(projectTypesDrop)}
                        setValue={() => {}}
                        stateName="singleProject"
                        value={values.singleProject}
                        error={touched.singleProject && errors.singleProject ? true : false}
                        setFieldValue={setFieldValue}
                        labelName="Display only on these Project Types"
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                      <SharedStyled.Button
                        maxWidth="50px"
                        marginTop="10px"
                        mediaHeight="52px"
                        type="button"
                        disabled={
                          selectedProject.includes(values.singleProject) ||
                          selectedProject.includes('All') ||
                          !values.singleProject
                        }
                        onClick={() => {
                          let val = values.singleProject
                          setSelectedProject((prev) => [...prev, val])
                        }}
                      >
                        <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                      </SharedStyled.Button>
                    </SharedStyled.FlexBox>

                    <SharedStyled.FlexRow margin="10px 0 0 0">
                      <SharedStyled.Text fontWeight="medium" fontSize="14px" width="100%" textAlign="flex-start">
                        {selectedProject.length > 0 && 'Selected projects :'}
                      </SharedStyled.Text>
                    </SharedStyled.FlexRow>
                    <Styled.OptionsWrapper>
                      {selectedProject?.map((field, idx) => (
                        <div
                          key={idx}
                          className="option"
                          onClick={() =>
                            removeFieldMultiSelect(
                              (values: string[]) => setSelectedProject(values),
                              idx,
                              selectedProject
                            )
                          }
                        >
                          {field} <CrossIcon />
                        </div>
                      ))}
                    </Styled.OptionsWrapper>

                    {/* <CustomSelect
                      dropDownData={[...cityDropdown, 'All']}
                      setFieldValue={setFieldValue}
                      stateName="location"
                      labelName="Location"
                      value={values.location}
                      margin="10px 0 0 0"
                      setValue={() => {}}
                      error={touched.location && errors.location ? true : false}
                    /> */}

                    <SharedStyled.FlexBox width="100%" gap="10px">
                      <CustomSelect
                        dropDownData={[...companySettingForAll?.workingStates, 'All']}
                        setValue={() => {}}
                        stateName="singleState"
                        value={values.singleState}
                        error={touched.singleState && errors.singleState ? true : false}
                        setFieldValue={setFieldValue}
                        labelName="Display only in these locations"
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                      <SharedStyled.Button
                        maxWidth="50px"
                        marginTop="10px"
                        mediaHeight="52px"
                        type="button"
                        disabled={
                          selectedStates.includes(values.singleState) ||
                          selectedStates.includes('All') ||
                          !values.singleState
                        }
                        onClick={() => {
                          let val = values.singleState
                          setSelectedStates((prev) => [...prev, val])
                        }}
                      >
                        <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                      </SharedStyled.Button>
                    </SharedStyled.FlexBox>

                    <SharedStyled.FlexRow margin="14px 0 0 0">
                      <SharedStyled.Text fontWeight="medium" fontSize="14px" width="100%" textAlign="flex-start">
                        {selectedStates.length > 0 && 'Selected states :'}
                      </SharedStyled.Text>
                    </SharedStyled.FlexRow>
                    <Styled.OptionsWrapper>
                      {selectedStates?.map((field, idx) => (
                        <div
                          key={idx}
                          className="option"
                          onClick={() =>
                            removeFieldMultiSelect((values: string[]) => setSelectedStates(values), idx, selectedStates)
                          }
                        >
                          {field} <CrossIcon />
                        </div>
                      ))}
                    </Styled.OptionsWrapper>

                    {/* <InputWithValidation labelName="Parent" stateName="parent" /> */}

                    <CustomSelect
                      dropDownData={['None', ...allSteps.filter((step) => (stepData ? step !== stepData.name : true))]}
                      setFieldValue={setFieldValue}
                      stateName="parent"
                      labelName="Parent"
                      value={values.parent}
                      margin="10px 0 0 0"
                      setValue={() => {}}
                      error={touched.parent && errors.parent ? true : false}
                    />

                    <CustomSelect
                      dropDownData={['None', 'Task', 'Call', 'Email', 'Text']}
                      setFieldValue={setFieldValue}
                      stateName="activityType"
                      labelName="Activity Type"
                      value={values.activityType}
                      margin="10px 0 0 0"
                      setValue={() => {}}
                      error={touched.activityType && errors.activityType ? true : false}
                    />

                    {values.fieldType !== 'None' && values.fieldType !== '' && (
                      <Checkbox
                        title="Display on opportunity card"
                        onChange={() => setFieldValue('isDisplay', !values.isDisplay)}
                        value={values.isDisplay}
                        margin="10px 0 0 0"
                        width="100%"
                      />
                    )}
                    <Checkbox
                      title="Required"
                      onChange={() => setFieldValue('isRequire', !values.isRequire)}
                      value={values.isRequire}
                      margin="10px 0 0 0"
                      width="100%"
                    />
                    {/* <InputWithValidation
                      labelName="Zip"
                      stateName="zip"
                      error={touched.zip && errors.zip ? true : false}
                    /> */}

                    {/* <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Notes
                    </Styled.LabelDiv>
                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      name="notes"
                      marginTop="8px"
                      height="52px"
                    ></Styled.TextArea> */}
                    <SharedStyled.ButtonContainer marginTop="20px">
                      <Button isLoading={loading} type="submit">
                        {isUpdate ? 'Update Step' : 'Add Step'}
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.StepModalContainer>
  )
}

export default NewStepModal
