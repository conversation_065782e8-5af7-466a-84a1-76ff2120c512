import React, { useState } from 'react'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './styles'
import { onlyNumber, onlyText, onlyTextWithSpaces } from '../../../../shared/helpers/regex'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'

import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import Checkbox from '../../../../shared/checkbox/Checkbox'
import { createStage } from '../../../../logic/apis/sales'
import { useParams } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { getDataFromLocalStorage, notify } from '../../../../shared/helpers/util'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'

interface I_NewStageModal {
  onClose: any
  totalSequence: number
  onSuccess: any
  groupStage?: string
}

const NewStageModal: React.FC<I_NewStageModal> = (props) => {
  const { onClose, totalSequence, onSuccess, groupStage } = props
  const initialValues = {
    name: '',
    description: '',
    PMRequired: false,
    projectRequired: false,
    orderRequired: false,
  }

  const [loading, setLoading] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)
    const response = await createStage({
      ...values,
      sequence: totalSequence + 1,
      createdBy: currentMember._id,
      stageGroup: groupStage,
    })
    setLoading(false)
    if (response?.status === 201 || response?.statusCode === 201) {
      notify('Stage created!', 'success')
      onSuccess()
      onClose()
    } else {
      notify(response?.data?.message ?? 'Failed Creation!', 'error')
      console.log('Stage create error', response)
    }
  }

  const newStepSchema = Yup.object().shape({
    name: Yup.string()
      .min(2, 'Too Short!')
      .max(50, 'Too Long!')
      .required('Required')
      .matches(onlyTextWithSpaces, 'Enter Valid Name'),
  })

  return (
    <Styled.StepModalContainer>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={newStepSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {({ values, errors, touched, resetForm, setFieldValue, handleChange }) => {
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>Add New Stage</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    resetForm()
                    onClose()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <InputWithValidation
                      labelName="Name"
                      stateName="name"
                      error={touched.name && errors.name ? true : false}
                    />

                    <InputWithValidation
                      labelName="Description"
                      stateName="description"
                      error={touched.description && errors.description ? true : false}
                    />

                    <Checkbox
                      title="Project Manager"
                      onChange={() => setFieldValue('PMRequired', !values.PMRequired)}
                      value={values.PMRequired}
                      margin="10px 0 0 0"
                      width="100%"
                    />

                    <Checkbox
                      title="Project Required"
                      onChange={() => setFieldValue('projectRequired', !values.projectRequired)}
                      value={values.projectRequired}
                      margin="10px 0 0 0"
                      width="100%"
                    />

                    <Checkbox
                      title="Order Required"
                      onChange={() => setFieldValue('orderRequired', !values.orderRequired)}
                      value={values.orderRequired}
                      margin="10px 0 0 0"
                      width="100%"
                    />
                    <SharedStyled.ButtonContainer marginTop="24px">
                      <Button type="submit" isLoading={loading}>
                        Add
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.StepModalContainer>
  )
}

export default NewStageModal
