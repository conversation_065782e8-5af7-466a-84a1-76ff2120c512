import { Fragment, useEffect, useState } from 'react'
import * as SharedStyled from '../../styles/styled'
import { Form, Formik } from 'formik'
import { SharedDate } from '../../shared/date/SharedDate'
import Button from '../../shared/components/button/Button'
import dayjs from 'dayjs'
import { getAdvertisingCost, updateCampaign, updateLeadSource } from '../../logic/apis/leadSource'
import { formatDollarAmount, isSuccess, notify } from '../../shared/helpers/util'
import { AdvertiseCont } from './style'
import { SLoader } from '../../shared/components/loader/Loader'

const getLastMonthAndYear = () => {
  const lastMonth = dayjs().subtract(1, 'month')
  return lastMonth.format('YYYY-MM')
}

export interface IData {
  advertisingCosts: AdvertisingCost[]
}

export interface AdvertisingCost {
  _id: string
  name: string
  leadSources: LeadSource[]
  estimatedCost: number
  actualCost: number
}

export interface LeadSource {
  _id: string
  name: string
  isActiveForTargetMonth: boolean
  campaigns: Campaign[]
  estimatedCost: number
  actualCost: any
}

export interface Campaign {
  _id: string
  name: string
  isActiveForTargetMonth: boolean
  estimatedCost: number
  actualCost: any
}

const Advertise = () => {
  const [initialValues, setInitialValues] = useState({
    date: getLastMonthAndYear(),
  })
  const [loading, setLoading] = useState(false)
  const [inputValues, setInputValues] = useState<any>({})
  const [selectedId, setSelectedId] = useState('')

  const [update, setUpdate] = useState(false)

  const [advertiseData, setAdvertiseData] = useState<AdvertisingCost[]>([])

  const getAdvertisingCostData = async (date: string) => {
    try {
      setLoading(true)
      const response = await getAdvertisingCost(date)
      if (isSuccess(response)) {
        setAdvertiseData(response.data?.data?.advertisingCosts)
      }
    } catch (error) {
      console.error('Error fetching advertising cost data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getAdvertisingCostData(initialValues?.date)
  }, [update])

  const handleDateSelection = (date: string) => {
    const newURL = `${window.location.pathname}?date=${date}`
    window.history.pushState({}, '', newURL)

    setInputValues({})
    getAdvertisingCostData(date)
  }

  useEffect(() => {
    handleDateSelection(initialValues.date)
  }, [])

  const handleSubmitForm = async (values: any) => {
    const selectedDate = values.date
    setInitialValues((prev) => ({
      ...prev,
      date: selectedDate,
    }))
    handleDateSelection(selectedDate)
  }

  const handleLSUpdate = async (data: any) => {
    try {
      const response = await updateLeadSource(data)
      if (isSuccess(response)) {
        notify(response?.data?.data?.message, 'success')
        setUpdate(!update)
      }
    } catch (error) {
      console.error('Error updating lead source:', error)
    } finally {
      setInputValues({})
    }
  }

  const handleCampaignUpdate = async (data: any, id: string) => {
    try {
      const response = await updateCampaign(data, id)
      if (isSuccess(response)) {
        notify(response?.data?.data?.message, 'success')
        setUpdate(!update)
      }
    } catch (error) {
      console.error('Error updating campaign:', error)
    } finally {
      setInputValues({})
    }
  }

  const handleInputChange = (id: string, value: any) => {
    setInputValues((prev: any) => ({
      ...prev,
      [id]: value,
    }))
  }

  return (
    <AdvertiseCont>
      <SharedStyled.SectionTitle
        style={{
          width: 'max-content',
        }}
        margin="0 auto"
      >
        Advertising Cost
      </SharedStyled.SectionTitle>
      <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
        {({ values, setFieldValue, touched, errors }) => (
          <Form>
            <SharedStyled.FlexBox
              width="100%"
              gap="10px"
              justifyContent="center"
              alignItems="flex-end"
              alignItemsM="center"
              column="column"
              margin="20px 0"
            >
              <SharedStyled.FlexRow width="max-content">
                <SharedDate
                  value={values.date}
                  labelName="Select Month"
                  stateName="date"
                  type="month"
                  error={touched.date && errors.date ? true : false}
                  setFieldValue={setFieldValue}
                  maxWidth="400px"
                  minWidth="300px"
                />
              </SharedStyled.FlexRow>

              <Button
                type="submit"
                isLoading={loading}
                // onClick={() => setbuttonCall(true)}
                disabled={!values.date}
                width="max-content"
                height="52px"
              >
                Get Cost
              </Button>
            </SharedStyled.FlexBox>
          </Form>
        )}
      </Formik>

      <SharedStyled.FlexCol margin="20px 0">
        {advertiseData?.length ? (
          <table>
            <thead>
              <tr>
                {/* <th>Lead Source</th>
                    <th>Start</th>
                    <th>End</th>
                    <th>Cost</th> */}

                <th></th>
                <th>ESTIMATED</th>
                <th>ACTUAL</th>
              </tr>
            </thead>
            <tbody>
              {advertiseData.map((channel) => (
                <Fragment key={channel._id}>
                  {/* Channel Row */}
                  <tr className="channel-row">
                    <td
                      style={{
                        padding: '10px 15px',
                      }}
                    >
                      <SharedStyled.FlexRow gap="20px">{channel.name}</SharedStyled.FlexRow>
                    </td>
                    <td>{formatDollarAmount(channel?.estimatedCost)}</td>
                    <td>{formatDollarAmount(channel?.actualCost)}</td>
                  </tr>

                  {/* Lead Sources */}
                  {channel.leadSources.map((leadSource) => (
                    <Fragment key={leadSource._id}>
                      {/* Lead Source Row */}
                      <tr className="lead-source-row">
                        <td style={{ padding: '8px 15px 8px 40px' }}>
                          <SharedStyled.FlexRow gap="20px">
                            <div className="lead-source">{leadSource.name}</div>
                          </SharedStyled.FlexRow>
                        </td>

                        <td>{formatDollarAmount(leadSource?.estimatedCost)}</td>
                        <td className="actual-cost">
                          <span>$</span>

                          <div
                            style={{
                              width: '100px',
                              height: '30px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'flex-start',
                            }}
                          >
                            {loading && selectedId === leadSource._id ? (
                              <SLoader width={20} />
                            ) : (
                              <input
                                type="number"
                                min={0}
                                value={inputValues[leadSource._id] ?? leadSource?.actualCost?.toFixed(2) ?? 0}
                                onChange={(e) => handleInputChange(leadSource._id, e.target.value)}
                                onBlur={(e) => {
                                  const value = Number(e.target.value)
                                  if (value !== leadSource?.actualCost) {
                                    const [year, month] = initialValues?.date?.split('-')
                                    const updatedData = {
                                      leadSourceId: leadSource?._id,
                                      actualCost: {
                                        month: Number(month),
                                        year: Number(year),
                                        cost: value,
                                      },
                                    }
                                    setSelectedId(leadSource._id)
                                    handleLSUpdate(updatedData)
                                  }
                                }}
                              />
                            )}
                          </div>
                        </td>
                      </tr>

                      {/* Campaigns */}
                      {leadSource.campaigns.map((campaign) => (
                        <tr key={campaign._id} className="campaign-row">
                          <td style={{ padding: '8px 15px 8px 60px' }}>{campaign.name}</td>
                          <td>{formatDollarAmount(campaign?.estimatedCost)}</td>
                          <td className="actual-cost">
                            <span>$</span>

                            <div
                              style={{
                                width: '100px',
                                height: '30px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'flex-start',
                              }}
                            >
                              {loading && selectedId === campaign._id ? (
                                <SLoader width={20} />
                              ) : (
                                <input
                                  type="number"
                                  min={0}
                                  value={inputValues[campaign._id] ?? campaign?.actualCost?.toFixed(2) ?? 0}
                                  onChange={(e) => handleInputChange(campaign._id, e.target.value)}
                                  onBlur={(e) => {
                                    const value = Number(e.target.value)
                                    if (value !== campaign?.actualCost) {
                                      const [year, month] = initialValues?.date?.split('-')
                                      const updatedData = {
                                        actualCost: {
                                          month: Number(month),
                                          year: Number(year),
                                          cost: value,
                                        },
                                      }
                                      setSelectedId(campaign._id)

                                      handleCampaignUpdate(updatedData, campaign._id)
                                    }
                                  }}
                                />
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </Fragment>
                  ))}
                </Fragment>
              ))}
            </tbody>
          </table>
        ) : (
          <>
            {loading ? null : (
              <SharedStyled.Text
                fontSize="20px"
                style={{
                  margin: '40px auto',
                }}
              >
                No Data found.
              </SharedStyled.Text>
            )}
          </>
        )}
      </SharedStyled.FlexCol>
    </AdvertiseCont>
  )
}

export default Advertise
