import { Fragment, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import { EditIcon } from '../../assets/icons/EditIcon'
import { getLeadSourceRules, getLeadSources, restoreCampaign } from '../../logic/apis/leadSource'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { dayjsFormat, getDataFromLocalStorage, hasValues, isSuccess, notify } from '../../shared/helpers/util'
import { Table } from '../../shared/table/Table'
import * as SharedStyled from '../../styles/styled'
import { ConfirmationPopUp } from './components/confirmationPopup/ConfirmationPopUp'
import { CreateLeadSourcePopUp } from './components/createLeadSourcePopUp/CreateLeadSourcePopUp'
import * as Styled from './style'
import TabBar from '../../shared/components/tabBar/TabBar'
import { SettingsCont, ButtonCont } from '../units/style'
import DeletedLeadSourceSettings from './components/deletedLeadSourceSettings/DeletedLeadSourceSettings'
import Button from '../../shared/components/button/Button'
import { StorageKey } from '../../shared/helpers/constants'
import { EditLeadSourcePopUp } from '../editLeadSourcePopUp/EditLeadSourcePopUp'
import Campaigns from './components/Campaigns'
import { CreateMarketingChannelPopUp } from '../marketingChannel/components/createMarketingChannelPopUp/CreateMarketingChannelPopUp'
import Toggle from '../../shared/toggle/Toggle'
import AddCampaignModal from './components/AddCampaignModal'
import dayjs from 'dayjs'
import Modal from '../../shared/customModal/Modal'
import { ModalDescription } from './components/confirmationPopup/style'
import { EditMarketingChannelPopUp } from '../marketingChannel/components/editMarketingChannelPopUp/EditMarketingChannelPopUp'
import ChannelConfirmationPopUp from '../marketingChannel/components/confirmationPopup/ConfirmationPopUp'
import { colors } from '../../styles/theme'
import {
  CreateRuleDialog,
  convertConditionsToStrings,
} from '../marketingChannel/components/createRuleDialog/CreateRuleDialog'

export interface IData {
  _id: string
  channelName: string
  channelDesc?: string
  order?: number
  deleted?: boolean
  leadSources: LeadSource[]
}

export interface LeadSource {
  _id: string
  name: string
  description: string
  channelId: string
  deleted: boolean
  campaigns: Campaign[]
  code?: string
  startMonth: number
  startYear: number
  endMonth?: number
  endYear: number
  cost: number
  isMonthly: boolean
}

export interface Campaign {
  _id: string
  companyId: string
  name: string
  description: string
  leadSourceIds: string[]
  startMonth: number
  startYear: number
  endMonth?: number
  endYear: number
  cost: number
  isMonthly: boolean
  deleted: boolean
}

export const getLeadSrcDropData = (data: any[]) => {
  return data?.reduce((acc: any, curr: any) => {
    return acc.concat(curr?.leadSources?.map((itm: any) => ({ ...itm, channelName: curr?.channelName })))
  }, [])
}

export const getFormattedLeadSrcData = (data: IData[]) => {
  const filterWithName: any = {}
  data.forEach((item) => {
    filterWithName[item.channelName] = item.leadSources.map((leadSource) => ({
      [leadSource.name]: leadSource.campaigns.map((campaign) => campaign.name),
    }))
  })

  return filterWithName
}

export const getLeadSrcDropdownName = (id: string, data: any[]) => {
  try {
    const trimmedId = id?.trim()

    for (const channel of data) {
      if (channel.leadSources?.length) {
        for (const leadSource of channel.leadSources) {
          // Check for campaign match first
          if (leadSource.campaigns?.length) {
            const matchingCampaign = leadSource.campaigns.find((campaign: any) => campaign._id === trimmedId)
            if (matchingCampaign) {
              return { sourceName: matchingCampaign.name }
            }
          }
          // If not a campaign, check for lead source match
          if (leadSource._id === trimmedId) {
            return { sourceName: leadSource.name }
          }
        }
      }
    }
  } catch (error) {
    console.log({ leaadSourceError: error })
  }

  return {} // Return empty object if no match found
}

export const getLeadSrcDropdownId = (name: string, data: any[]) => {
  const searchName = name.trim().toLowerCase()

  // Initialize result object
  const result = {
    leadSourceId: null,
    leadSourceName: null,
    campaignName: null,
    campaignId: null,
    leadSourceObject: {},
  }

  for (const channel of data) {
    if (channel.leadSources?.length) {
      for (const leadSource of channel.leadSources) {
        if (leadSource.name.toLowerCase() === searchName) {
          result.leadSourceId = leadSource._id
          result.leadSourceName = leadSource.name
          result.leadSourceObject = leadSource
          return result
        }

        if (leadSource.campaigns?.length) {
          const matchingCampaign = leadSource.campaigns.find(
            (campaign: any) => campaign.name.toLowerCase() === searchName
          )

          if (matchingCampaign) {
            result.leadSourceId = leadSource._id
            result.leadSourceName = leadSource.name
            result.campaignId = matchingCampaign._id
            result.campaignName = matchingCampaign.name
            return result
          }
        }
      }
    }
  }

  return result
}

const LeadSource = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [showConfirmationPopUp, setShowConfirmationPopUp] = useState<boolean>(false)
  const [showCreateLeadSourcePopUp, setShowCreateLeadSourcePopUp] = useState<boolean>(false)
  const [showEditLeadSourcePopUp, setShowEditLeadSourcePopUp] = useState<boolean>(false)
  const [showRestoreLeadSourcePopUp, setShowRestoreLeadSourcePopUp] = useState<boolean>(false)
  const [leadSourceData, setLeadSourceData] = useState<any>({})
  const [leadSourceRules, setLeadSourceRules] = useState<any>([])
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [data, setData] = useState<IData[]>([])
  const [showRestoreCampaign, setShowRestoreCampaign] = useState(false)
  const [marketingChannelData, setMarketingChannelData] = useState<any>({})
  const [showDeleteChannelModal, setShowDeleteChannelModal] = useState(false)

  const [isRestoreChannel, setIsRestoreChannel] = useState(false)
  const [addChannelModal, setAddChannelModal] = useState(false)
  const [editChannelModal, setEditChannelModal] = useState(false)
  const [isToggleOn, setIsToggleOn] = useState(true)
  const [showAddCampaign, setShowAddCampaign] = useState(false)
  const [leadSrcData, setLeadSrcData] = useState([])
  const [leadData, setLeadData] = useState([])
  const [selectedData, setSelectedData] = useState<any>({})
  const [restoreLoading, setRestoreLoading] = useState(false)
  const [showCreateRuleDialog, setShowCreateRuleDialog] = useState(false)
  // const [selectedRule, setSelectedRule] = useState<any>(null)
  const [trackingRuleId, setTrackingRuleId] = useState<any>(null)
  const fetchData = async () => {
    try {
      setLoading(true)

      const leadSourceResponse = await getLeadSources(
        {
          active: isToggleOn,
        },
        false
      )

      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource

        setLeadSrcData(getLeadSrcDropData(statusRes))
        setLeadData(statusRes)
        setData(statusRes)
      } else {
        notify(leadSourceResponse?.data?.message, 'error')
      }
    } catch (error) {
      console.error('TeamTable fetchData error', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [detailsUpdate, isToggleOn])

  useEffect(() => {
    if (!showAddCampaign) {
      setSelectedData({})
    }
  }, [showAddCampaign])

  const handleRestoreCampaign = async () => {
    try {
      setRestoreLoading(true)
      const restoreResponse = await restoreCampaign(selectedData?._id)
      if (isSuccess(restoreResponse)) {
        notify(restoreResponse?.data?.data?.message, 'success')
        setDetailsUpdate((p) => !p)
        setShowRestoreCampaign(false)
      }
    } catch (error) {
      console.error('restoreCampaign error', error)
    } finally {
      setRestoreLoading(false)
    }
  }

  const fetchLeadSourceRules = async () => {
    try {
      const leadSourceResponse = await getLeadSourceRules({})

      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.data?.rules
        setLeadSourceRules(statusRes)
      } else {
        notify(leadSourceResponse?.data?.message, 'error')
      }
    } catch (error) {
      console.error('getLeadSourceRules error', error)
    }
  }

  useEffect(() => {
    fetchLeadSourceRules()
  }, [])

  return (
    <SettingsCont gap="24px" className="new-lead">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Marketing Settings</SharedStyled.SectionTitle>
        <ButtonCont>
          <Button
            onClick={() => {
              setAddChannelModal(true)
            }}
          >
            New Channel
          </Button>
        </ButtonCont>
      </SharedStyled.FlexRow>

      <TabBar
        tabs={[
          {
            title: 'Lead Sources',
            render: () => (
              <>
                <SharedStyled.FlexRow>
                  <Toggle
                    title="Show Active Only"
                    className="text bold"
                    customStyles={{ margin: '0px', justifyContent: 'flex-end' }}
                    isToggled={isToggleOn}
                    onToggle={() => {
                      setIsToggleOn((prev) => !prev)
                    }}
                  />
                </SharedStyled.FlexRow>

                {data?.length ? (
                  <table>
                    <thead>
                      <tr>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.map((channel) => (
                        <Fragment key={channel._id}>
                          {/* Channel Row */}
                          <tr className={channel?.deleted ? 'deleted' : 'channel-row'}>
                            <td
                              style={{
                                padding: '10px 15px',
                              }}
                            >
                              <SharedStyled.FlexRow gap="20px">
                                <SharedStyled.TooltipContainer
                                  width="250px"
                                  positionLeft="4px"
                                  positionBottom="0px"
                                  positionLeftDecs="120px"
                                  positionBottomDecs="100%"
                                >
                                  {channel?.channelDesc && (
                                    <span className="tooltip-content">{channel?.channelDesc}</span>
                                  )}

                                  <span
                                    className="lead-source"
                                    onClick={() => {
                                      setMarketingChannelData({
                                        marketingChannelName: channel?.channelName,
                                        description: channel?.channelDesc,
                                        order: channel?.order,
                                        id: channel?._id,
                                      })

                                      if (channel?.deleted) {
                                        setIsRestoreChannel(true)
                                        setShowDeleteChannelModal(true)
                                      } else {
                                        setIsRestoreChannel(false)
                                        setEditChannelModal(true)
                                      }
                                    }}
                                  >
                                    {channel.channelName}
                                  </span>
                                </SharedStyled.TooltipContainer>

                                <Button
                                  width="max-content"
                                  className="btn"
                                  type="button"
                                  onClick={() => {
                                    setLeadSourceData({
                                      channel: channel?.channelName || '-',
                                    })
                                    setShowCreateLeadSourcePopUp(true)
                                  }}
                                >
                                  &#x271A;
                                </Button>
                              </SharedStyled.FlexRow>
                            </td>
                            <td>START</td>
                            <td>END</td>
                            <td>COST</td>
                          </tr>

                          {/* Lead Sources */}
                          {channel.leadSources.map((leadSource) => (
                            <Fragment key={leadSource._id}>
                              {/* Lead Source Row */}
                              <tr className={leadSource?.deleted ? 'deleted' : 'lead-source-row'}>
                                <td style={{ padding: '8px 15px 8px 40px' }}>
                                  <SharedStyled.FlexRow gap="20px">
                                    <div
                                      className="lead-source"
                                      style={{
                                        color: dayjs(`${leadSource?.endYear}-${leadSource?.endMonth}`)
                                          .endOf('month')
                                          .isBefore(dayjs())
                                          ? '#757575'
                                          : 'inherit',
                                      }}
                                      onClick={() => {
                                        setLeadSourceData({
                                          leadSourceName: leadSource?.name || '-',
                                          description: leadSource?.description,
                                          channel: channel?.channelName || '-',
                                          id: leadSource?._id,
                                          isDeleted: leadSource?.deleted,
                                        })
                                        if (leadSource?.deleted) {
                                          setShowRestoreLeadSourcePopUp(true)
                                        } else {
                                          setShowEditLeadSourcePopUp(true)
                                        }
                                      }}
                                    >
                                      <SharedStyled.TooltipContainer
                                        width="250px"
                                        positionLeft="4px"
                                        positionBottom="0px"
                                        positionLeftDecs="120px"
                                        positionBottomDecs="100%"
                                      >
                                        {leadSource?.description && (
                                          <span className="tooltip-content">{leadSource?.description}</span>
                                        )}

                                        {leadSource.name}
                                      </SharedStyled.TooltipContainer>
                                    </div>
                                    <Button
                                      width="max-content"
                                      type="button"
                                      className="btn"
                                      onClick={() => {
                                        setSelectedData({
                                          leadSourceName: leadSource.name,
                                          leadSourceStartDate: leadSource?.startYear
                                            ? dayjs(`${leadSource?.startYear}-${leadSource?.startMonth}`).format(
                                                'YYYY-MM'
                                              )
                                            : null,
                                        })
                                        setShowAddCampaign(true)
                                      }}
                                    >
                                      &#x271A;
                                    </Button>
                                  </SharedStyled.FlexRow>
                                </td>
                                <td>{`${
                                  leadSource?.startYear
                                    ? dayjs(`${leadSource?.startYear}-${leadSource?.startMonth}`)
                                        .startOf('month')
                                        .format('M/D/YY')
                                    : '---'
                                }`}</td>
                                <td>{`${
                                  leadSource?.endYear
                                    ? dayjs(`${leadSource?.endYear}-${leadSource?.endMonth}`)
                                        .endOf('month')
                                        .format('M/D/YY')
                                    : '---'
                                }`}</td>
                                <td>
                                  {leadSource?.cost
                                    ? `$${leadSource?.cost?.toFixed()}${leadSource?.isMonthly ? '/month' : ''}`
                                    : '---'}
                                </td>
                              </tr>

                              {/* Campaigns */}
                              {leadSource.campaigns.map((campaign) => (
                                <tr
                                  key={campaign._id}
                                  style={{
                                    cursor: 'pointer',
                                    color: dayjs(`${campaign?.endYear}-${campaign?.endMonth}`)
                                      .endOf('month')
                                      .isBefore(dayjs())
                                      ? '#757575'
                                      : 'inherit',
                                  }}
                                  onClick={() => {
                                    setSelectedData({
                                      ...campaign,
                                      leadSourceName: leadSource.name,
                                      isDeleted: campaign?.deleted,
                                      leadSourceStartDate: leadSource?.startYear
                                        ? dayjs(`${leadSource?.startYear}-${leadSource?.startMonth}`).format('YYYY-MM')
                                        : null,
                                    })
                                    if (campaign?.deleted) {
                                      setShowRestoreCampaign(true)
                                    } else {
                                      setShowAddCampaign(true)
                                    }
                                  }}
                                  className={campaign?.deleted ? 'deleted' : 'campaign-row'}
                                >
                                  <td>
                                    <SharedStyled.TooltipContainer
                                      width="250px"
                                      positionLeft="4px"
                                      positionBottom="0px"
                                      positionLeftDecs="120px"
                                      positionBottomDecs="100%"
                                    >
                                      {campaign?.description && (
                                        <span className="tooltip-content">{campaign?.description}</span>
                                      )}

                                      {campaign.name}
                                    </SharedStyled.TooltipContainer>
                                  </td>
                                  <td>{`${
                                    campaign?.startYear
                                      ? dayjs(`${campaign?.startYear}-${campaign?.startMonth}`)
                                          .startOf('month')
                                          .format('M/D/YY')
                                      : '---'
                                  }`}</td>
                                  <td>{`${
                                    campaign?.endYear
                                      ? dayjs(`${campaign?.endYear}-${campaign?.endMonth}`)
                                          .endOf('month')
                                          .format('M/D/YY')
                                      : '---'
                                  }`}</td>
                                  <td>
                                    {campaign?.cost
                                      ? `$${campaign?.cost?.toFixed()}${campaign?.isMonthly ? '/month' : ''}`
                                      : '---'}
                                  </td>
                                </tr>
                              ))}
                            </Fragment>
                          ))}
                        </Fragment>
                      ))}
                    </tbody>
                  </table>
                ) : null}
              </>
            ),
          },
          {
            title: 'Marketing Rules',
            render: () => (
              <div>
                <table style={{ width: '100%', borderCollapse: 'collapse', marginTop: '20px' }}>
                  <thead style={{ visibility: 'visible' }}>
                    <tr>
                      <th
                        style={{
                          textAlign: 'left',
                          padding: '10px 15px',
                          width: '30%',

                          fontSize: '11px',
                          fontWeight: 700,
                          lineHeight: '20px',
                          textTransform: 'uppercase',
                          visibility: 'visible',
                        }}
                      >
                        LEAD SOURCE
                      </th>
                      <th
                        style={{
                          textAlign: 'left',
                          padding: '10px 15px',
                          width: '45%',

                          fontSize: '11px',
                          fontWeight: 700,
                          lineHeight: '20px',
                          textTransform: 'uppercase',
                          visibility: 'visible',
                        }}
                      >
                        CONDITIONS
                      </th>
                      <th
                        style={{
                          textAlign: 'left',
                          padding: '10px 15px',
                          width: '25%',

                          fontSize: '11px',
                          fontWeight: 700,
                          lineHeight: '20px',
                          textTransform: 'uppercase',
                          visibility: 'visible',
                        }}
                      >
                        LAST USED
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {leadSourceRules?.map((leadSourceRule: any, idx: number) => (
                      <tr
                        key={idx}
                        onClick={() => {
                          setTrackingRuleId(leadSourceRule?._id)
                          setShowCreateRuleDialog(true)
                        }}
                        style={{ cursor: 'pointer' }}
                      >
                        <td style={{ padding: '10px 15px', width: '30%' }}>
                          {getLeadSrcDropdownName(leadSourceRule?.campaignId || leadSourceRule?.leadSourceId, leadData)
                            ?.sourceName || '--'}
                        </td>
                        <td style={{ padding: '10px 15px', width: '45%' }}>
                          {convertConditionsToStrings(leadSourceRule?.conditions)?.join(', ') || '--'}
                        </td>
                        <td style={{ padding: '10px 15px', width: '25%' }}>
                          {dayjsFormat(leadSourceRule?.createdAt, 'M/D/YY h:mm a')}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ),
          },
        ]}
      />

      <CustomModal show={showConfirmationPopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowConfirmationPopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Delete Lead Source"
          leadSourceData={leadSourceData}
        />
      </CustomModal>
      <CustomModal show={showRestoreLeadSourcePopUp}>
        <ConfirmationPopUp
          setShowConfirmationPopUp={setShowRestoreLeadSourcePopUp}
          setDetailsUpdate={setDetailsUpdate}
          header="Restore Lead Source"
          leadSourceData={leadSourceData}
        />
      </CustomModal>

      <CustomModal show={showCreateLeadSourcePopUp}>
        <CreateLeadSourcePopUp
          setShowCreateLeadSourcePopUp={setShowCreateLeadSourcePopUp}
          setDetailsUpdate={setDetailsUpdate}
          leadSourceData={leadSourceData}
        />
      </CustomModal>
      <CustomModal show={showEditLeadSourcePopUp}>
        <EditLeadSourcePopUp
          setShowEditLeadSourcePopUp={setShowEditLeadSourcePopUp}
          setDetailsUpdate={setDetailsUpdate}
          leadSourceData={leadSourceData}
          onDeleteClick={() => {
            setShowEditLeadSourcePopUp(false)
            setShowConfirmationPopUp(true)
          }}
        />
      </CustomModal>

      <CustomModal show={addChannelModal}>
        <CreateMarketingChannelPopUp
          setShowCreateMarketingChannelPopUp={setAddChannelModal}
          setDetailsUpdate={setDetailsUpdate}
        />
      </CustomModal>

      <CustomModal show={editChannelModal}>
        <EditMarketingChannelPopUp
          onDeleteClick={() => {
            setEditChannelModal(false)
            setShowDeleteChannelModal(true)
          }}
          setShowEditMarketingChannelPopUp={setEditChannelModal}
          setDetailsUpdate={setDetailsUpdate}
          marketingChannelData={marketingChannelData}
        />
      </CustomModal>

      <CustomModal show={showAddCampaign}>
        <AddCampaignModal
          setShowAddCampaign={setShowAddCampaign}
          setDetailsUpdate={setDetailsUpdate}
          selectedData={selectedData}
          leadSrcData={leadSrcData}
          isEdit={hasValues(selectedData) && !!selectedData?._id}
        />
      </CustomModal>

      <CustomModal show={showRestoreCampaign}>
        <Modal title="Restore Campaign" onClose={() => setShowRestoreCampaign(false)}>
          <ModalDescription>Are you sure you want to restore this Campaign?</ModalDescription>
          <SharedStyled.FlexBox
            width="100%"
            alignItems="center"
            justifyContent="space-around"
            marginTop="20px"
            gap="5px"
          >
            <Button maxWidth="200px" isLoading={restoreLoading} onClick={handleRestoreCampaign}>
              Yes
            </Button>
            <Button maxWidth="200px" className="gray" onClick={() => setShowRestoreCampaign(false)}>
              No
            </Button>
          </SharedStyled.FlexBox>
        </Modal>
      </CustomModal>

      <CustomModal show={showDeleteChannelModal}>
        <ChannelConfirmationPopUp
          setShowConfirmationPopUp={setShowDeleteChannelModal}
          setDetailsUpdate={setDetailsUpdate}
          header={isRestoreChannel ? 'Restore Marketing Channel' : 'Delete Marketing Channel'}
          marketingChannelData={marketingChannelData}
        />
      </CustomModal>

      <CustomModal show={showCreateRuleDialog}>
        <CreateRuleDialog
          leadSrcData={leadData}
          trackingData={{
            adName: '',
            sessionSource: '',
            utmContent: '',
            utmCampaign: '',
            utmMedium: '',
            utmSource: '',
          }}
          onComplete={() => {
            setShowCreateRuleDialog(false)
            fetchLeadSourceRules()
          }}
          trackingRuleId={trackingRuleId}
          onClose={() => {
            setShowCreateRuleDialog(false)
            setTrackingRuleId(null)
          }}
        />
      </CustomModal>
    </SettingsCont>
  )
}

export default LeadSource
