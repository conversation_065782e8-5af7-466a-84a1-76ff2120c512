import { AxiosInstance } from '.'
import { filterUndefinedAndNull } from '../../shared/helpers/util'

export const getDashboardLeadActionList = async (csrId: string, limit?: number) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/lead-action-list/csrId/${csrId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDashboardLeadActionList error', error)
    return error?.response
  }
}

export const getDashboardLeadNoActionList = async (csrId: string, limit?: number) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/lead-no-action-list/csrId/${csrId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDashboardLeadNoActionList error', error)
    return error?.response
  }
}

export const getDashboardLeadsActions = async (inputObj: { csrId: string; endDate: string }) => {
  const { csrId, endDate } = inputObj
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/lead-actions/csrId/${csrId}/dateEnd/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDashboardLeadsActions error', error)
    return error?.response
  }
}

export const getDashboardSalesPersonActionList = async (salesPersonId: string, limit?: number) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/sales-person-action-list/salesPerson/${salesPersonId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDashboardSalesPersonActionList error', error)
    return error?.response
  }
}

export const assignOppsToSalesPerson = async (salesPersonId: string, oppIds: string[]) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(
      `/dashboard/assign-project-to-sales-person/salesPerson/${salesPersonId}`,
      oppIds,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )

    return response
  } catch (error: any) {
    console.error('assignOpsToSalesPerson error', error)
    return error?.response
  }
}

export const getDashboardBacklog = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/backlog`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDashboardBacklog error', error)
    return error?.response
  }
}
export const getMembersNotIn = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/member-not-or-in-many-crew`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getMembersNotIn error', error)
    return error?.response
  }
}
export const getNotClockedInData = async (date: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/not-clocked-in/${date}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getNotClockedInData error', error)
    return error?.response
  }
}
export const getCrewMemberData = async (
  memberId: string,
  date: string,
  startDate: string,
  signal?: any,
  setLoading?: React.Dispatch<React.SetStateAction<boolean>>
) => {
  try {
    const token: any = localStorage.getItem('token')
    setLoading?.(true)
    const response = await AxiosInstance.get(`/dashboard/crew/${memberId}/${date}/${startDate}`, {
      signal,
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    setLoading?.(false)

    return response
  } catch (error: any) {
    console.error('getCrewMemberData error', error)
    return error?.response
  }
}

export const getUnapprovedCardData = async (inputObj: { crewLeadId: string; dayStart: string }) => {
  const { crewLeadId, dayStart } = inputObj
  try {
    const token: any = localStorage.getItem('token')

    const response =
      crewLeadId &&
      (await AxiosInstance.get(`/dashboard/unapproved-cards-list/crewLead/${crewLeadId}/${dayStart}`, {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }))

    return response
  } catch (error: any) {
    return error?.response
  }
}
export const getDayReportList = async (inputObj: { dayStart: string; dayEnd: string }) => {
  const { dayEnd, dayStart } = inputObj

  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/day-report-list/dayStart/${dayStart}/${dayEnd}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDayReportList error', error)
    return error?.response
  }
}

export const getMissingDailyLogs = async (inputObj: { dayStart: string; dayEnd: string }) => {
  const { dayEnd, dayStart } = inputObj

  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/missing-dailylog/${dayStart}/${dayEnd}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getMissingDailyLogs error', error)
    return error?.response
  }
}
export const getDashboardSalesPersonNoActionList = async (salesPersonId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/sales-person-no-action-list/salesPerson/${salesPersonId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit: 100,
      },
    })

    return response
  } catch (error: any) {
    console.error('getDashboardSalesPersonActionList error', error)
    return error?.response
  }
}

export const getSalesPersonReport = async (inputObj: { salesPersonId: string; currentDate: string }) => {
  const { salesPersonId, currentDate } = inputObj
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(
      `/dashboard/sales-person-report/salesPerson/${salesPersonId}/${currentDate}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )

    return response
  } catch (error: any) {
    console.error('getSalesPersonReport error', error)
    return error?.response
  }
}
export const getDashboardActions = async (inputObj: { salesPersonId: string; endDate: string }) => {
  const { salesPersonId, endDate } = inputObj
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/dashboard/actions/salesPerson/${salesPersonId}/dateEnd/${endDate}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getSalesPersonReport error', error)
    return error?.response
  }
}
