import { AxiosInstance } from '.'
import { notify, simplifyBackendError } from '../../shared/helpers/util'

export type FilePayload = {
  name: string
  _id: string
  mimetype: string
  url: string
  stepId?: string
  thumbnail?: string
  tags?: string[]
  location?: {
    type: string
    coordinates?: string[] // [longitude, latitude]
  }
  createdAt: string
  createdBy: string
  builderFormId?: string
  formId?: string
  formFieldByName?: string
}

export const getMediaSettings = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/company/media-setting`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getAllTags error', error)
    return error?.response
  }
}

export const updateMediaSettings = async (dataObj: {
  tags?: string[]
  maxImageSizeMB?: number
  maxVideoSizeMB?: number
  maxAudioSizeMB?: number
  maxMediaPerOpportunity?: number
  allowedMediaTypes?: string[]
}) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(
      `/company/media-setting`,
      {
        ...dataObj,
      },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('updateTag error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const deleteTag = async (tag: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/company/tag`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: {
        tag,
      },
    })
    return response
  } catch (error: any) {
    console.error('updateTag error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const decodeUrlToken = async (linkId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/media/share-url/${linkId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('decodeUrlToken error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const generateTokenUrl = async (
  oppId: string,
  filterPayload: { tags?: string[]; imageIds?: string[]; types?: string[]; createdBy?: string[] }
) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(
      `/media/share-url`,
      {
        oppId,
        filters: {
          imageIds: filterPayload?.imageIds,
          tags: filterPayload?.tags,
          types: filterPayload?.types,
          createdBy: filterPayload?.createdBy,
        },
      },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('generateTokenUrl error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getOpportunityMedia = async (
  oppId?: string,
  payloadFilter?: {
    imagesIds?: string
    tags: string
  }
) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/media/oppId/${oppId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        imagesIds: payloadFilter?.imagesIds,
        tags: payloadFilter?.tags,
      },
    })
    return response
  } catch (error: any) {
    console.error('updateTag error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getAllMedia = async (payloadFilter?: {
  imagesIds?: string
  tags?: string
  createdBy?: string
  types?: string
  limit?: number
  skip?: number
}) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/media/all`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        imagesIds: payloadFilter?.imagesIds,
        tags: payloadFilter?.tags,
        createdBy: payloadFilter?.createdBy,
        types: payloadFilter?.types,
        limit: payloadFilter?.limit,
        skip: payloadFilter?.skip,
      },
    })
    return response
  } catch (error: any) {
    console.error('getAllMedia error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getPresignedUrlMedia = async (
  pathType: string,
  payload: { fileName: string; mimetype: string }[],
  memberId: string,
  opportunityId?: string
) => {
  try {
    const token: any = localStorage.getItem('token')

    const res = await AxiosInstance.post(
      `/s3/pre-signed-urls/${pathType}`,
      { files: payload, memberId, opportunityId },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return res
  } catch (error: any) {
    console.error('getPresignedUrlMedia error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const createMediaOpportunity = async (oppId: string | undefined, payload: FilePayload[]) => {
  try {
    const token: any = localStorage.getItem('token')

    const res = await AxiosInstance.post(
      `/media`,
      { oppId, images: payload },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return res
  } catch (error: any) {
    console.error('getPresignedUrlMedia error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const updateMediaOpportunity = async (oppId: string, payload: Partial<FilePayload>[]) => {
  try {
    const token: any = localStorage.getItem('token')

    const res = await AxiosInstance.put(
      `/media`,
      { oppId, images: payload },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return res
  } catch (error: any) {
    console.error('updateMediaOpportunity error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const deleteMediaOpportunity = async (imageId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const res = await AxiosInstance.delete(`/media/images?imageIds=${imageId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return res
  } catch (error: any) {
    console.error('deleteMediaOpportunity error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const deleteImageFromS3 = async (imageId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const res = await AxiosInstance.delete(`/s3/media`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: {
        mediaUrl: [imageId],
      },
    })

    return res
  } catch (error: any) {
    console.error('deleteImage error', error)
    notify(error?.response?.data?.message || 'Failed to delete image', 'error')
    return error?.response
  }
}

export const getDownloadUrl = async (mediaUrl: string, isEdit?: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const res = await AxiosInstance.get(`/s3/download-url`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        imageUrl: mediaUrl,
        isEdit,
      },
    })
    return res
  } catch (error: any) {
    console.error('getDownloadUrl error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}
