import { AxiosInstance } from '.'

interface I_CreateCity {
  city: string
  state: string
  createdBy: string
}

interface I_UpdateCity {
  id: string
  city: string
  state: string
  createdBy: string
}

interface I_DeleteCity {
  id: string
}

interface I_RestoreCity {
  id: string
}

interface I_GetCity {
  deleted: boolean
  skip?: string
  limit?: string
}

export const createCity = async (data: I_CreateCity) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/city/create-city`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('createCity error', error)
    return error?.response
  }
}

export const updateCity = async (data: I_UpdateCity) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/city/update-city`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('updateCity error', error)
    return error?.response
  }
}

export const getCities = async (data: I_GetCity, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/city/get-city/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        skip: data.skip,
        limit: data.limit,
      },
    })

    return response
  } catch (error: any) {
    console.error('getCities error', error)
    return error?.response
  }
}

export const deleteCity = async (data: I_DeleteCity) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/city/delete-city`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })

    return response
  } catch (error: any) {
    console.error('deleteCity error', error)
    return error?.response
  }
}

export const restoreCity = async (data: I_RestoreCity) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/city/restore-city`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('restoreCity error', error)
    return error?.response
  }
}
