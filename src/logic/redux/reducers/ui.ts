import { <PERSON><PERSON><PERSON><PERSON> } from '../../../shared/helpers/constants'
import { getDataFromLocalStorage } from '../../../shared/helpers/util'
import { ACTIONS } from '../actions'

const initialState = {
  navCollapsed: getDataFromLocalStorage(StorageKey.navCollapsed),
  showMobileSideNav: false,
  showCompanyCreationForm: false,
  showAddOptions: false,
  isPositionNotAvailable: false,
  filterSaleBy: {
    name: '',
    _id: '',
  },
  filterOperationsBy: {
    name: '',
    _id: '',
  },
  contactsSearchValue: '',
  contactsAdvancedFilters: {},
}

export const uiReducer = (state = initialState, action: { type: string; payload: boolean }) => {
  switch (action.type) {
    case ACTIONS.SET_NAVCOLLAPSED:
      return { ...state, navCollapsed: action.payload }
    case ACTIONS.SET_SHOW_MOBILE_SIDE_NAV:
      return { ...state, showMobileSideNav: action.payload }
    case ACTIONS.SET_SHOW_COMPANY_CREATION_FORM:
      return { ...state, showCompanyCreationForm: action.payload }
    case ACTIONS.SET_SHOW_ADD_OPTIONS:
      return { ...state, showAddOptions: action.payload }
    case ACTIONS.SET_FILTER_BY:
      return { ...state, filterSaleBy: action.payload }
    case ACTIONS.SET_FILTER_OPPS_BY:
      return { ...state, filterOperationsBy: action.payload }
    case ACTIONS.SET_POSITION_AVAILABLE:
      return { ...state, isPositionNotAvailable: action.payload }
    case ACTIONS.SET_CONTACTS_SEARCH_VALUE:
      return { ...state, contactsSearchValue: action.payload }
    case ACTIONS.SET_CONTACTS_ADVANCED_FILTERS:
      return { ...state, contactsAdvancedFilters: action.payload }
    default:
      return state
  }
}
