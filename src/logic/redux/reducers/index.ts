import { combineReducers } from 'redux'
import { authReducer } from './auth'
import { companyReducer } from './company'
import { invitationReducer } from './invitation'
import { timeZoneReducer } from './timeZone'
import { uiReducer } from './ui'
import { paginationReducer } from './pagination'

export const reducers = combineReducers({
  auth: authReducer,
  company: companyReducer,
  invitation: invitationReducer,
  timeZone: timeZoneReducer,
  ui: uiReducer,
  pagination: paginationReducer,
})
