import { ACTIONS } from '../actions'

interface PaginationState {
  settings: {
    [key: string]: {
      pageIndex: number
      pageSize: number
    }
  }
}

const initialState: PaginationState = {
  settings: {}
}

export const paginationReducer = (
  state = initialState, 
  action: { 
    type: string; 
    payload?: { 
      pageKey: string; 
      pageIndex: number; 
      pageSize: number 
    } 
  }
) => {
  switch (action.type) {
    case ACTIONS.SET_PAGINATION_SETTINGS:
      if (!action.payload) return state;
      
      const { pageKey, pageIndex, pageSize } = action.payload;
      return {
        ...state,
        settings: {
          ...state.settings,
          [pageKey]: { pageIndex, pageSize }
        }
      }
    case ACTIONS.CLEAR_PAGINATION_SETTINGS:
      return {
        ...state,
        settings: {}
      }
    default:
      return state
  }
}