import { ACTIONS } from './index'

export const setNavCollapsed = (data: boolean) => {
  return {
    type: ACTIONS.SET_NAVCOLLAPSED,
    payload: data,
  }
}
export const setShowMobileSideNav = (data: boolean) => {
  return {
    type: ACTIONS.SET_SHOW_MOBILE_SIDE_NAV,
    payload: data,
  }
}
export const setShowCompanyCreationModal = (data: boolean) => {
  return {
    type: ACTIONS.SET_SHOW_COMPANY_CREATION_FORM,
    payload: data,
  }
}
export const setShowAddOptions = (data: boolean) => {
  return {
    type: ACTIONS.SET_SHOW_ADD_OPTIONS,
    payload: data,
  }
}
export const setFilterSaleBy = (data: { name: string; _id: string }) => {
  return {
    type: ACTIONS.SET_FILTER_BY,
    payload: data,
  }
}
export const setFilterOppsBy = (data: { name: string; _id: string }) => {
  return {
    type: ACTIONS.SET_FILTER_OPPS_BY,
    payload: data,
  }
}
export const setPositionNotAvailable = (data: any) => {
  return {
    type: ACTIONS.SET_POSITION_AVAILABLE,
    payload: data,
  }
}
export const setTriggerRetch = (data: boolean) => {
  return {
    type: ACTIONS.TRIGGER_REFETCH,
    payload: data,
  }
}

export const setContactsSearchValue = (data: string) => {
  return {
    type: ACTIONS.SET_CONTACTS_SEARCH_VALUE,
    payload: data,
  }
}

export const setContactsAdvancedFilters = (data: Record<string, any[]>) => {
  return {
    type: ACTIONS.SET_CONTACTS_ADVANCED_FILTERS,
    payload: data,
  }
}
