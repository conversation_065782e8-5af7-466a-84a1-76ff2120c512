export const ACTIONS = {
  IS_LOGGED_IN: 'IS_LOGGED_IN',
  TRIG<PERSON><PERSON>_REFETCH: 'TRIGGE<PERSON>_REFETCH',
  COMPANIES: 'COMPANIES',
  SET_CURRENT_COMPANY: 'SET_CURRENT_COMPANY',
  IS_INVITED: 'IS_INVITED',
  JUST_INVITED: 'JUST_INVITED',
  SET_MEMBER_DATA: 'SET_MEMBER_DATA',
  SET_CURRENT_POSITION: 'SET_CURRENT_POSITION',
  SET_CURRENT_TIME_ZONE: 'SET_CURRENT_TIME_ZONE',
  SET_CURRENT_ROLE: 'SET_CURRENT_ROLE',
  SET_NAVCOLLAPSED: 'SET_NAVCOLLAPSED',
  SET_SHOW_MOBILE_SIDE_NAV: 'SET_SHOW_MOBILE_SIDE_NAV',
  SET_SHOW_COMPANY_CREATION_FORM: 'SET_SHOW_COMPANY_CREATION_FORM',
  SET_SHOW_ADD_OPTIONS: 'SET_SHOW_ADD_OPTIONS',
  SET_PROFILE_INFO: 'SET_PROFILE_INFO',
  SET_FILTER_BY: 'SET_FILTER_BY',
  SET_FILTER_OPPS_BY: 'SET_FILTER_OPPS_BY',
  SET_CURRENT_POSITION_Details: 'SET_CURRENT_POSITION_Details',
  SET_POSITION_PERMISSIONS: 'SET_POSITION_PERMISSIONS',
  SET_COMPANY_SETTING_FOR_ALL: 'SET_COMPANY_SETTING_FOR_ALL',
  SET_POSITION_AVAILABLE: 'SET_POSITION_AVAILABLE',
  // Add pagination action types
  SET_PAGINATION_SETTINGS: 'SET_PAGINATION_SETTINGS',
  CLEAR_PAGINATION_SETTINGS: 'CLEAR_PAGINATION_SETTINGS',
  // Add contacts filter and search action types
  SET_CONTACTS_SEARCH_VALUE: 'SET_CONTACTS_SEARCH_VALUE',
  SET_CONTACTS_ADVANCED_FILTERS: 'SET_CONTACTS_ADVANCED_FILTERS',
}
