import { ChangeEvent } from 'react'
import * as Styled from './style'

interface CheckboxProps {
  title?: string
  value: boolean
  onChange: (event: ChangeEvent<HTMLInputElement>) => void
  margin?: string
  textAlign?: string
  cursor?: string
  width?: string
  disabled?: boolean
  hideTitle?: boolean
  style?: React.CSSProperties
}

const Checkbox: React.FC<CheckboxProps> = ({ title, value, onChange, children, hideTitle, disabled, ...restProps }) => {
  return (
    <Styled.Label htmlFor={`checkbox-${title}`} {...restProps}>
      <input id={`checkbox-${title}`} type="checkbox" checked={value} onChange={onChange} disabled={disabled} />
      {/* {children ?? ''} */}
      {children ?? hideTitle ? '' : title}
    </Styled.Label>
  )
}

export default Checkbox
