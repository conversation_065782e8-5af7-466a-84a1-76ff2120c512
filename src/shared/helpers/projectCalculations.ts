import { roundTo2 } from '../../modules/contract/constant'
import { skylightTypeEnum } from '../../modules/newProject/types'

const getRawData = (
  bVentInputs: any,
  stemVentInputs: any,
  chimneyInputs: any,
  skylightInputs: any,
  sunTunnelInputs: any
) => {
  const rawData: any = {}

  rawData.bVentAmt = bVentInputs.length
  for (let i = 0; i < bVentInputs.length; i++) {
    rawData[`bVentNum${i + 1}`] = Number(bVentInputs[i])
  }

  rawData.stemVentAmt = stemVentInputs.length
  for (let i = 0; i < stemVentInputs.length; i++) {
    rawData[`stemVentNum${i + 1}`] = Number(stemVentInputs[i])
  }

  rawData.chimneyAmt = chimneyInputs.length
  for (let i = 0; i < chimneyInputs.length; i++) {
    const { width: chimneyWid, length: chimneyLen } = chimneyInputs[i]
    const index = i + 1
    rawData[`chimneyWidNum${index}`] = Number(chimneyWid)
    rawData[`chimneyLenNum${index}`] = Number(chimneyLen)
  }

  rawData.skylightAmt = skylightInputs.length
  for (let i = 0; i < skylightInputs.length; i++) {
    const { width: skylightWid, length: skylightLen, skylightType } = skylightInputs[i]
    const index = i + 1
    if (skylightType === skylightTypeEnum.CUSTOM) {
      rawData[`skyId${index}`] = 'custom'
      rawData[`skyName${index}`] = 'Custom Size'
    } else if (skylightType === skylightTypeEnum.CRUB2x2) {
      rawData[`skyId${index}`] = 'curb2x2'
      rawData[`skyName${index}`] = 'Curb Mount 2x2 (curb: 25.5" x 25.5")'
    } else if (skylightType === skylightTypeEnum.CRUB2x4) {
      rawData[`skyId${index}`] = 'curb2x4'
      rawData[`skyName${index}`] = 'Curb Mount 2x4 (curb: 25.5" x 49.5")'
    }
    rawData[`skylightWidNum${index}`] = Number(skylightWid)
    rawData[`skylightLenNum${index}`] = Number(skylightLen)
  }

  rawData.sunTunnelAmt = sunTunnelInputs.length
  for (let i = 0; i < sunTunnelInputs.length; i++) {
    rawData[`sunTunnelNum${i + 1}`] = Number(sunTunnelInputs[i])
  }

  return rawData
}

const getRoofSQ = (reroofAreas: any) => {
  const roofSQ = {
    rmvFlat: 0,
    rmvLow: 0,
    rmvSteep: 0,
    rmvFlatAdd: 0,
    rmvLowAdd: 0,
    rmvSteepAdd: 0,
    instFlat: 0,
    instLow: 0,
    instSteep: 0,
    instPly: 0,
    rmvPly: 0,
    noAccess: 0,
    twoStory: 0,
    ventArea: 0,
  }

  for (const { pitch, layers, install, instPly, rmvPly, noAccess, twoStory, ventArea } of reroofAreas) {
    if (pitch >= 0 && pitch < 2) {
      roofSQ.instFlat += install
      roofSQ.rmvFlat += layers >= 1 ? install : 0
      roofSQ.rmvFlatAdd += layers >= 1 ? (layers - 1) * install : 0
    } else if (pitch >= 2 && pitch < 4) {
      roofSQ.instLow += install
      roofSQ.rmvLow += layers >= 1 ? install : 0
      roofSQ.rmvLowAdd += layers > 1 ? (layers - 1) * install : 0
    } else if (pitch >= 4) {
      roofSQ.instSteep += install
      roofSQ.rmvSteep += layers >= 1 ? install : 0
      roofSQ.rmvSteepAdd += layers >= 1 ? (layers - 1) * install : 0
    }

    roofSQ.instPly += instPly
    roofSQ.rmvPly += rmvPly
    roofSQ.noAccess += noAccess
    roofSQ.twoStory += twoStory
    roofSQ.ventArea += ventArea
  }

  return roofSQ
}

const getIceShield = (roofSQ: any, eave: any, valley: any, sideWalls: any, endWalls: any) => {
  const { instFlat, instLow, instSteep } = roofSQ
  const roofSize = instFlat + instLow + instSteep
  //pull values from the add project form and calc how much ice shield on each
  //all ice shield on low slope and flat
  const lowSlope = Number(instFlat + instLow)
  //get total steep slope squares for percentage calc
  const steepSlope = Number(instSteep)
  //get percentage of low slope against whole roof to prevent double calcing on low slope areas
  const lowPercent = lowSlope / (lowSlope + steepSlope)
  //get modifier to multiply ice shield calcs to remove estimated amount on low slope
  const lowMod = 1 - lowPercent
  //2 rows on eaves
  const eaves = (eave * 6 * lowMod) / 100
  //1 row in valleys
  const valleys = (valley * 3 * lowMod) / 100
  //2' out on side walls
  const sideWall = (sideWalls * 2 * lowMod) / 100
  //2' out on end walls
  const endWall = (endWalls * 2 * lowMod) / 100
  // Remove valleys to account for them separately
  let iceShield = eaves + sideWall + endWall + lowSlope // + valleys
  if (iceShield > roofSize) iceShield = roofSize

  return iceShield
}

const getVents = (
  ventArea: any,
  bVentInputs: any,
  stemVentInputs: any,
  sunTunnelInputs: any,
  pipesInputs: any,
  existingVents: any
) => {
  const bVentCounts = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
  const stemVentCounts = [0, 0, 0]
  const sunTunnelCounts = [0, 0, 0]

  const pipesCounts = [0, 0, 0, 0, 0, 0]

  for (const bVent of bVentInputs) {
    if (bVent !== '') {
      bVentCounts[bVent - 3]++
    }
  }

  for (const stemVent of stemVentInputs) {
    if (stemVent !== '') {
      stemVentCounts[stemVent / 2 - 2]++
    }
  }

  for (const sun of sunTunnelInputs) {
    if (sun !== '') {
      if (sun === 10) sunTunnelCounts[0]++
      if (sun === 14) sunTunnelCounts[1]++
      if (sun === 22) sunTunnelCounts[2]++
    }
  }
  for (const pipe of pipesInputs) {
    if (pipe !== '') {
      if (pipe === '1 1/4') pipesCounts[0]++
      if (pipe === '1 1/2') pipesCounts[1]++
      if (pipe === '2') pipesCounts[2]++
      if (pipe === '3') pipesCounts[3]++
      if (pipe === '4') pipesCounts[4]++
      if (pipe === '2 split boot') pipesCounts[5]++
    }
  }

  // const canVentsNeeded = Math.ceil((((ventArea * 100) / 300 / 2) * 144) / 49);
  const canVentsNeeded = Math.ceil(ventArea * 0.4897)
  const canVentsTtl = Math.max(canVentsNeeded, existingVents)

  return {
    bVent3: bVentCounts[0],
    bVent4: bVentCounts[1],
    bVent5: bVentCounts[2],
    bVent6: bVentCounts[3],
    bVent7: bVentCounts[4],
    bVent8: bVentCounts[5],
    bVent9: bVentCounts[6],
    bVent10: bVentCounts[7],
    bVent11: bVentCounts[8],
    bVent12: bVentCounts[9],
    stemVent4: stemVentCounts[0],
    stemVent6: stemVentCounts[1],
    stemVent8: stemVentCounts[2],
    sunTunnel10: sunTunnelCounts[0],
    sunTunnel14: sunTunnelCounts[1],
    sunTunnel22: sunTunnelCounts[2],
    'pipeFlash1/4': pipesCounts[0],
    'pipeFlash1/2': pipesCounts[1],
    pipeFlash2: pipesCounts[2],
    pipeFlash3: pipesCounts[3],
    pipeFlash4: pipesCounts[4],
    splitPipe2: pipesCounts[5],
    canVentsTtl,
  }
}

const getChimneys = (chims: any) => {
  let chimFront = 0
  let chimBack = 0
  let chimSides = 0
  const chimAmt = chims.length

  for (let i = 0; i < chimAmt; i++) {
    const chimWidth = (Number(chims[i].width) + 2) / 12
    const chimLength = (Number(chims[i].length) + 2) / 12
    chimFront += chimWidth
    chimBack += chimWidth
    chimSides += chimLength * 2
  }

  return {
    chimFront,
    chimBack,
    chimSides,
    chimAmt,
  }
}

const getSkylights = (skys: any) => {
  let skyFront = 0
  let skyBack = 0
  let skySides = 0
  let skyAmt = 0
  const skyCustom = []
  let curb2x2 = 0
  let curb2x4 = 0

  for (let i = 0; i < skys.length; i++) {
    const skyWidth = (Number(skys[i].width) + 3) / 12
    const skyLength = (Number(skys[i].length) + 3) / 12

    switch (skys[i].skylightType) {
      case skylightTypeEnum.CUSTOM:
        skyCustom.push({ [`custom${skyCustom.length + 1}`]: `${skyWidth}x${skyLength}` })
        break
      case skylightTypeEnum.CRUB2x2:
        curb2x2++
        break
      case skylightTypeEnum.CRUB2x4:
        curb2x4++
        break
    }

    skyFront += skyWidth
    skyBack += skyWidth
    skySides += skyLength * 2
    skyAmt++
  }

  return {
    skyFront,
    skyBack,
    skySides,
    skyAmt,
    skyCustom,
    curb2x2,
    curb2x4,
  }
}

const loadReroofInputsArray = (
  allInputs: any,
  roofSQ: any,
  ice: any,
  vents: any,
  chimneys: any,
  skylights: any,
  otherInputs: any,
  // companyId: string,
  measurements: any
) => {
  try {
    const inputs = allInputs.filter((v) => v.code && v.code !== null)
    // const measurements = [];

    const propertyLookup = {
      ...otherInputs,
      iceShield: ice,
      skyCustom: skylights.skyCustom.length,
    }

    for (const input of inputs) {
      const id = input._id
      const code = input.code

      // Check if the id is already present in measurements
      const idExists = measurements.some((measurement: any) => measurement._id === id)

      let value
      if (roofSQ[code] >= 0) value = roofSQ[code]
      else if (vents[code] >= 0) value = vents[code]
      else if (chimneys[code] >= 0) value = chimneys[code]
      else if (skylights[code] >= 0) value = skylights[code]
      else value = propertyLookup[code] || 0

      value = roundTo2(value)
      const name = input.name
      const unit = input.unit

      if (!idExists) {
        measurements.push({
          _id: id,
          name,
          value,
          unit,
        })
      }
    }

    return measurements
  } catch (error: any) {
    console.error(error)
  }
}

export const projectMethod = (
  restObject: any,
  projectTypes: any,
  // companyId: string,
  inputs: any
  // projectInputs: any
) => {
  try {
    // Values for Reroof project
    const { customData, ...restValues } = restObject

    const reroofAreas = customData.roofAreas
    // const reroofAreas = this.loadReroofAreaArray(createProjectDto.customData.roofAreas);
    //collect all raw bvents, stemvents, chimneys, skylights, suntunnels

    const rawData = getRawData(
      customData.bVents,
      customData.stemVents,
      customData.chimneys,
      customData.skylights,
      customData.sunTunnels
    )

    //put all square values into 1 object
    const roofSQ = getRoofSQ(reroofAreas)
    const iceShield = getIceShield(
      roofSQ,
      customData.eaves,
      customData.valleys,
      customData.sideWall,
      customData.endWall
    )

    const vents = getVents(
      roofSQ.ventArea,
      customData.bVents,
      customData.stemVents,
      customData.sunTunnels,
      customData?.pipes,
      customData?.canVentsExist || 0
    )

    const chimneys = getChimneys(customData.chimneys)

    const skylights = getSkylights(customData.skylights)

    const otherInputs = {
      eaves: customData.eaves,
      rakes: customData.rakes,
      sideWall: customData.sideWall,
      endWall: customData.endWall,
      ridges: customData.ridges,
      valleys: customData.valleys,
      hips: customData.hips,
      pitchChange: customData.pitchChange,
      // pipeFlash123: customData?.pipeFlash123,
      canVentsExist: customData.canVentsExist,
      ridgeVentExist: customData.ridgeVentExist,
    }
    // const otherDetailsArray = [...customData?.otherDetails]
    const otherDetailsArray = [...restValues?.projectInputs]

    const reroofInputs = loadReroofInputsArray(
      inputs,
      roofSQ,
      iceShield,
      vents,
      chimneys,
      skylights,
      otherInputs,
      // companyId,
      otherDetailsArray
    )
    // projectInputs = projectInputs.concat(reroofInputs)
    let projectInputs = reroofInputs
    //NOTE: add check to only allow inputs with value > 0
    projectInputs = projectInputs
      .map(({ _id, value }: { _id: string; value: string }) => ({ _id, value }))
      .filter((input: any) => input.value > 0)
    // .map(({ _id, value }: { _id: string; value: string }) => ({ _id, value }))
    // .map(({ _id, value }: { _id: string; value: string }) => ({ _id, value }))
    // .filter((input: any) => input.value > 0)
    // .map(({ _id, value }: { _id: string; value: string }) => ({ _id, value }))

    // submittedValues.projectInputs = submittedValues.projectInputs.map(({ _id, value }) => ({ _id, value }))
    // Gather all roof areas into one object with labor mods to adjust for difficulty/pitch
    // const roofSizeDetail = getRoofSizeDetail(reroofAreas, projectTypes?.pitchMod)
    return {
      ...restValues,
      projectInputs,
      customData: {
        ...customData,
        pitch: undefined,
        otherDetails: undefined,
        reroofAreas,
        // roofSizeDetail,
        roofSQ,
        // rawData,
        roofAreas: undefined,
      },
      currDate: new Date().toISOString(),
    }
  } catch (error) {
    console.error(error)
  }
}
