export const COMPANIES = ['Rapid Innovation', 'Codezero2pi Pvt Ltd', 'Team Logger']
export const MANAGERS: any = ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>']
export const DEPARTMENT: any = ['Office', 'Crew', 'Sales']
export const POSITIONS: any = ['Crew Member', 'Crew Lead', 'Sales Person', 'Sales Manager']
export const PAYSCHEDULE: any = ['Twice Monthly Salary', 'Every Friday Sun-Sat']
export const PER: any = ['Hour', 'Month', 'Year']
export const PER_OBJ: any = { 1: 'Year', 2: 'Month', 3: 'Hour' }
export const PER_OBJ1: any = { Year: '1', Month: '2', Hour: '3' }
export const ROLES: any = { 1: 'Owner', 2: 'Admin', 3: 'Member' }
export const ROLES_NAME: any = ['Owner', 'Admin', 'Member']
export const ROLES_OBJ = { Owner: '1', Admin: '2', Member: '3' }
export const PERIOD: any = ['Every week', 'Every other week', 'Twice per month', 'Once per month']
export const PERIOD_OBJ: any = {
  ['Every week']: '1',
  ['Every other week']: '2',
  ['Twice per month']: '3',
  ['Once per month']: '4',
}
export const PERIOD_OBJ1: any = { 1: 'Every week', 2: 'Every other week', 3: 'Twice per month', 4: 'Once per month' }
export const WORK_TYPE: any = { roofing: '1' }
export const WORK_TYPE1: any = { 1: 'roofing' }
export const UNITS: any = ['SQ', 'SF', 'LF', 'EA', '%']

export const MONTHS: any = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
export const WEEK_DAYS = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']

export const TIME_ZONES: any = ['PST', 'IST']

export enum TimeCardStatusEnum {
  Active = <any>'Active',
  Unapproved = <any>'Unapproved',
  Lead_Approved = <any>'LeadApproved',
  Approved = <any>'Approved',
}

export const STATUS: any = ['Unapproved', 'Lead Approved', 'Approved']
export const DAILY_LOG_STATUS: any = ['Unapproved', 'LeadApproved', 'Approved']
export const EXTRA_TEAR_OFF: any = [
  { name: 'Install Sheet', value: '', unit: 'EA', codeName: 'instSheet' },
  { name: 'Rmv Sheet', value: '', unit: 'EA', codeName: 'rmvSheet' },
  { name: 'Cut Cans', value: '', unit: 'EA', codeName: 'tCutCan' },
  { name: 'Cut Ridge', value: '', unit: 'LF', codeName: 'tCutRV' },
  { name: 'Install Baffle', value: '', unit: 'EA', codeName: 'tBaffle' },
  { name: '2nd Felt Lyr', value: '', unit: 'SQ', codeName: 'tExtraFelt' },
]

export const EXTRA_ROOFING: any = [
  { name: 'Ridge Cap', value: '', unit: 'LF', codeName: 'rCap' },
  { name: 'Ridge Vent', value: '', unit: 'LF', codeName: 'rVent' },
  { name: `2'x2' C/F`, value: '', unit: 'EA', codeName: 'cf2x2' },
  { name: 'Eyebrows', value: '', unit: 'EA', codeName: 'eyebrows' },
  { name: `2'x4'+ C/F`, value: '', unit: 'EA', codeName: 'cf2x4' },
  { name: 'Bay Window', value: '', unit: 'EA', codeName: 'bayWindows' },
  { name: 'Chim Flash', value: '', unit: 'EA', codeName: 'chimSkyFlash' },
  { name: 'R&R Skylight', value: '', unit: 'EA', codeName: 'replaceSkylight' },
  { name: 'Legacy', value: '', unit: 'SQ', codeName: 'install50Yr' },
  { name: 'Windsor', value: '', unit: 'SQ', codeName: 'installDesigner' },
]

export const Permissions: any = {
  ['Full']: 1,
  ['Managed']: 2,
  ['Self']: 3,
  ['None']: 4,
}
export const PermissionsNum: any = {
  1: 'Full',
  2: 'Managed',
  3: 'Self',
  4: 'None',
}

export const DailyLogKeyValue: any = [
  {
    header: 'Labor Budget',
    value: '$138',
  },
  {
    header: 'Labor Actual',
    value: '$138',
  },
  {
    header: 'Real Revenue',
    value: '$138',
  },
  {
    header: 'Volume',
    value: '$138',
  },
  {
    header: 'RR / $',
    value: '$138',
  },
  {
    header: 'Crew Working',
    value: '$138',
  },
  {
    header: 'Time Worked',
    value: '$138',
  },
]
export const subDailyLogKeyValue: any = [
  {
    header: 'Labor Budget',
    value: 'budget',
    showAlways: false,
  },
  {
    header: 'Labor Actual',
    value: 'actual',
    showAlways: true,
  },
  {
    header: 'Real Revenue',
    value: 'rr',
    showAlways: false,
  },
  {
    header: 'Volume',
    value: 'vol',
    showAlways: false,
  },
  {
    header: 'RR / $',
    value: 'rrDollar',
    showAlways: false,
  },
]

export const subInitialProjData = {
  oppPO: '',
  oppId: '',
  notes: '',
  isTypeValue: false,
  tearOffSQ: 0,
  instSheet: 0,
  rmvSheet: 0,
  nonBillPly: 0,
  tExtraFelt: 0,
  tCutRV: 0,
  tCutCan: 0,
  tVentPlug: 0,
  tBaffle: 0,

  roofingSQ: 0,
  install50Yr: 0,
  chimCF: 0,
  replaceSkylight: 0,
  eyebrows: 0,
  bayWindows: 0,

  addManHours: 0,
  nonBillHours: 0,
  addMaterials: 0,
  instFascia: 0,
  rmvFascia: 0,
  handLoadSQ: 0,
  highRoof: 0,

  percentDone: 0,
}

export enum Nue {
  thin = 'NueThin',
  regular = 'NueRegular',
  medium = 'NueMedium',
  italic = 'NueItalic',
  bold = 'NueBold',
  black = 'NueBlack',
}

export const StorageKey = {
  token: 'token',
  id: 'id',
  currentCompany: 'currentCompany',
  navCollapsed: 'navCollapsed',
  privacy: 'privacy',
  networkError: 'networkError',
  plan: 'plan',
  lastPassInstalled: 'lastPassInstalled',
  companyName: 'companyName',
}

export const usStatesShortNames = [
  'AL',
  'AK',
  'AZ',
  'AR',
  'CA',
  'CO',
  'CT',
  'DE',
  'FL',
  'GA',
  'HI',
  'ID',
  'IL',
  'IN',
  'IA',
  'KS',
  'KY',
  'LA',
  'ME',
  'MD',
  'MA',
  'MI',
  'MN',
  'MS',
  'MO',
  'MT',
  'NE',
  'NV',
  'NH',
  'NJ',
  'NM',
  'NY',
  'NC',
  'ND',
  'OH',
  'OK',
  'OR',
  'PA',
  'RI',
  'SC',
  'SD',
  'TN',
  'TX',
  'UT',
  'VT',
  'VA',
  'WA',
  'WV',
  'WI',
  'WY',
]

export const pieceWorkObject = {
  rCap: 'Ridge cap',
  rVent: 'Ridge vent',
  cf2x2: `2'x2' C/F`,
  cf2x4: `2'x4' C/F`,
  eyebrows: 'Eyebrows',
  bayWindows: 'Bay windows',
  extraTime: 'Extra hours',
  instSheet: 'R&R Plywood',
  rmvSheet: 'Remove sheets',
  tCutCan: 'Cut in cans',
  tCutRV: 'Cut in ridge vent',
  tBaffle: 'Install baffles',
  tExtraFelt: 'Extra felt layers (SQ)',
  chimSkyFlash: 'Chim/sky base flashing',
  replaceSkylight: 'Replace skylight',
  install50Yr: 'Install Legacy',
  installDesigner: 'Install Designer',
  tHours: 'Extra hours tear Off',
  rHours: 'Extra hours roofing',
  tEarned: 'Earned tear off',
  rEarned: 'Earned roofing',
  otherHours: 'Other hours',
  sl15: 'SL-15',
  classic: 'Classic',
  locRib: 'Loc-Rib',
  gutters: 'Gutters',
}

export const ARROWMARK = '->'

export enum FormAccess {
  Opportunity = 'opportunity',
  Profile = 'profile',
}

export enum SubscriptionPlanType {
  FREE = 'free',
  PRO = 'pro',
  PROPLUS = 'proplus',
}

export enum SubscriptionStatusEnum {
  ACTIVE = 'active',
  CANCELLED = 'canceled',
}

export enum SubscriptionRenewalPeriod {
  MONTH = 'month',
  YEAR = 'year',
}

export enum InvitationStatusEnum {
  Pending = 1,
  Accepted = 2,
  Rejected = 3,
  Revoked = 4,
  Resend = 5,
}

export enum SupportStatusEnum {
  OPEN = 1,
  REPLIED = 2,
  CLOSED = 3,
}
export enum ResponseSupportEnum {
  'Created' = 1,
  'Replied' = 2,
  'Closed' = 3,
}

export enum FilePathTypeEnum {
  Project = 'project',
  Profile = 'profile',
  Company = 'company',
  Member = 'member',
}

export enum ContentBlockTypeEnum {
  contract = 1,
  email = 2,
  forms = 3,
}

export const MAX_IMAGE_SIZE = 5 * 1024 * 1024 // 5MB
export const MAX_IMAGE_WIDTH = 512
export const MAX_IMAGE_HEIGHT = 512
export const allowedImageTypes = ['image/jpeg', 'image/png']

export const timeOffPositions = ['Owner', 'Admin', 'GeneralManager', 'OperationsAdmin', 'ProjectManager']

export const SUPPORT_EMAIL = 'mailto:<EMAIL>'
export const PITCHES = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
export const LAYERS = [1, 2, 3, 4]

export const GPSObject: Record<number, string> = {
  1: 'Client',
  2: 'Travel',
  3: 'Other',
}

export const FinanceFeeVisibleTo = ['Wells Fargo Financing', 'Credit Card (3.5% fee)']

// for reference
const ICONS = {
  plus: '&#x2B;',
}

export enum StageGroupEnum {
  Leads = 'leads',
  Sales = 'sales',
  Operations = 'operations',
}

export const moduleNames = {
  dashboard: {
    oppsToDo: 'opps to do',
    missingDailyLogs: 'missing dailylogs',
    timecardsMissingUnapproved: 'timecards missing/unapproved',
    leads: 'leads',
  },
  reports: {
    weeklySales: 'weekly sales',
    kpi: 'kpi',
    crewPayroll: 'crew payroll',
    nonCrewPayroll: 'non crew payroll',
    salesPerson: 'sales person',
    customSales: 'custom sales',
    conversion: 'conversion',
    commission: 'commission',
    weeklyProduction: 'weekly production',
    production: 'production',
    weeklyProject: 'weekly project',
    crewJobCost: 'crew job cost',
    jobCost: 'job cost',
    crewScoreboard: 'crew scoreboard',
  },
  settings: {
    settings: 'settings',
  },
  crm: {
    opportunity: 'opportunity',
    opportunityInfo: 'opportunity info',
    operations: 'operations',
    sales: 'sales',
    projects: 'projects',
    clients: 'clients',
    leads: 'leads',
  },
  module: {
    team: 'team',
    crew: 'crew',
    timeCards: 'timeCards',
    dailyLog: 'dailyLog',
    subcontractor: 'subcontractor',
    customProjects: 'custom projects',
    gps: 'gps',
  },
}

export const mimeTypesMap: Record<string, string> = {
  png: 'image/png',
  jpg: 'image/jpeg',
  heic: 'image/heic',
  jpeg: 'image/jpeg',
  mp4: 'video/mp4',
  mov: 'video/quicktime',
  mp3: 'audio/mp3',
  mpeg: 'video/mpeg',
  avi: 'video/x-msvideo',
  pdf: 'application/pdf',
}

export enum UserRolesEnum {
  Owner = 1,
  Admin = 2,
  Member = 3,
  Contractor = 4,
}

export const ActionColors: Record<string, string> = {
  pastelPurple: '#E6E6FA',
  pastelBlue: '#B8E6FF',
  pastelGreen: '#C8F7C5',
  pastelYellow: '#FFF4B8',
  pastelOrange: '#FFD4B8',
  softPink: '#FFB8C1',
}
