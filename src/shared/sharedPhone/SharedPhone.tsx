import { ErrorMessage } from 'formik'
import { <PERSON><PERSON><PERSON><PERSON>, useEffect, useState } from 'react'
import { getDataFromLocalStorage, getDigitsFromPhone } from '../helpers/util'
import * as Styled from './style'
import { StorageKey } from '../helpers/constants'

interface I_InputProps {
  stateName: string
  labelName: string
  twoInput?: boolean
  disabled?: boolean
  error?: boolean
  value?: string
  onChange?: Function
  onBlur?: any
}

export const SharedPhone = (props: I_InputProps) => {
  const { stateName, labelName, twoInput, disabled, error, value, onChange, onBlur } = props

  const [curValue, setCurValue] = useState<string>(value ?? '')

  const isValidPhoneNumber = (str: string) => {
    // Strip all non-numeric characters from the input
    const strippedInput = str.replace(/\D/g, '')
    return strippedInput.length <= 10
  }

  const formatPhoneNumber = (input: string, prevInput: string) => {
    if (input.length < prevInput.length) return input
    // Strip all non-numeric characters from the input
    const strippedInput = input.replace(/\D/g, '')

    // Use regex to split the input into groups of three digits and a group of up to four digits

    const match = strippedInput.match(/^(\d{0,3})(\d{0,3})(\d{0,4})$/)

    // Reformat the input with parentheses around the first three digits and a hyphen between the second and third groups
    let formattedInput = ''
    if (match) {
      if (strippedInput.length >= 6) formattedInput = `(${match[1]}) ${match[2]}-${match[3]}`
      else if (strippedInput.length >= 3) formattedInput = `(${match[1]}) ${match[2]}${match[3]}`
      else formattedInput = `${match[1]}${match[2]}${match[3]}`
    } else {
      formattedInput = strippedInput
    }
    return formattedInput
  }

  const handleChange = (e: any) => {
    let str = e.target.value
    if (isValidPhoneNumber(str)) {
      const newPhone = formatPhoneNumber(e.target.value, curValue)
      setCurValue(newPhone)
      onChange && onChange(getDigitsFromPhone(newPhone))
    }
  }

  useEffect(() => {
    if (value) {
      let formatedValue = formatPhoneNumber(value, '')
      setCurValue(formatedValue)
    } else {
      setCurValue('')
    }
  }, [value])

  const isLastPassInstalled = getDataFromLocalStorage(StorageKey.lastPassInstalled)

  return (
    <>
      <Styled.InputWithValidationContainer twoInput={twoInput}>
        <Styled.InputFive $error={error} className={isLastPassInstalled ? 'lastpass' : ''}>
          <div className={curValue === '' ? 'label-float empty' : 'label-float'}>
            <Styled.InputField
              name={stateName}
              type="text"
              // marginTop="8px"
              disabled={disabled}
              $error={error}
              value={curValue}
              onChange={handleChange}
              placeholder=" "
              onBlur={onBlur}
            />
            <label>{labelName}</label>
          </div>
        </Styled.InputFive>
        {error && (
          <Styled.ErrorContainer>
            <Styled.ErrorMsg>
              <ErrorMessage name={stateName} />
            </Styled.ErrorMsg>
          </Styled.ErrorContainer>
        )}
      </Styled.InputWithValidationContainer>
    </>
  )
}
