import styled from 'styled-components'
import { colors, screenSizes } from '../../styles/theme'
import { Nue } from '../helpers/constants'
import { getTimeCardColor } from '../helpers/util'

export const TimeCardApproveNoApproveDiv = styled.div<any>`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 5px;
  border: 0.5px solid ${colors.lightGrey8};
  border-radius: 4px;
  cursor: pointer;
  /* background: ${(props) => (props.isApproved ? `${colors.lightGreen}` : `transparent`)}; */
  :hover {
    filter: saturate(300%);

    ${(props) =>
      getTimeCardColor(props.bgColor) === '#ebecfc'
        ? `
      filter:none;
  background: rgba(188, 192, 245, 0.7);
  `
        : ``}
  }

  background: ${(props) => getTimeCardColor(props.bgColor)};

  &.active {
    background-color: ${getTimeCardColor('Active')};
  }
`
export const TimeCardApproveNoApproveLeftDiv = styled.div`
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
  height: fit-content;

  &.report {
    .project {
      font-family: ${Nue.regular};
    }

    .work {
      font-size: 12px;
    }

    .date {
      font-size: 12px;
    }
  }
`

export const KeyValueSpan = styled.span`
  font-size: 12px;
  font-family: ${Nue.regular};
  &.bolder {
    font-family: ${Nue.bold};
  }
  &.extra {
    font-size: 14px;
  }
  &.dollar {
    font-size: 14px;
  }
  &.header {
    font-size: 16px;
  }
  &.work {
    font-size: 14px;
  }
  &.note {
    font-size: 12px;
    font-style: italic;
  }
  &.managerNotes {
    font-size: 12px;
    font-style: italic;
    font-family: ${Nue.bold};
  }
  &.timeInOut {
    font-size: 20px;
    @media (min-width: ${screenSizes.M}px) {
      font-size: 24px;
    }
  }
  .project {
    font-family: ${Nue.medium};
    font-size: 16px;
    @media (min-width: ${screenSizes.M}px) {
      font-size: 18px;
    }
  }
`

export const WorkDoneTimeCardContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`

export const WorkDoneTimeCardHeader = styled.p`
  margin: 0;
  font-size: 14px;
  font-family: ${Nue.regular};
`

export const IconContainer = styled.div``

export const TimeCardApproveNoApproveRightDiv = styled.div<any>`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 5px;
  svg {
    width: 35px;
    height: 35px;
    margin-left: 2px;
  }
  svg path {
    stroke: ${(props) => (props.isApproved ? `${colors.errorRed}` : `${colors.green}`)};
  }
`
