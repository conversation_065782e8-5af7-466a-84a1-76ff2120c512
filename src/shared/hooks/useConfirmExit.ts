import { useBlocker } from 'react-router-dom'
import { useEffect } from 'react'

function useConfirmExit(when: boolean, msg: string) {
  const blocker = useBlocker(when)

  useEffect(() => {
    if (blocker.state === 'blocked') {
      const confirm = window.confirm(msg || 'Are you sure you want to leave?')
      if (confirm) {
        blocker.proceed()
      } else {
        blocker.reset()
      }
    }
  }, [blocker])
}

export default useConfirmExit
