import { ErrorMessage, Field } from 'formik'
import { useEffect, useState } from 'react'
import { daysInMonth, getDataFromLocalStorage } from '../helpers/util'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/en'

import * as Styled from './style'
import { useSelector } from 'react-redux'
import { MONTHS, StorageKey, WEEK_DAYS } from '../helpers/constants'
import { onlyMmDd } from '../helpers/regex'

dayjs.extend(relativeTime)

interface I_SharedDate {
  value: any
  labelName: string
  stateName: string
  firstHalf?: boolean
  secondHalf?: boolean
  error?: boolean
  min?: string
  max?: string
  setFieldValue?: any
  disabled?: boolean
  onBlur?: any
  isSmall?: boolean
  showBorder?: boolean // if small
  maxWidth?: string
  minWidth?: string
  disableFocus?: boolean
  passRef?: any
  type?: string
}

export const SharedDate = (props: I_SharedDate) => {
  const {
    value,
    labelName,
    stateName,
    firstHalf,
    secondHalf,
    error,
    min,
    setFieldValue,
    disabled,
    onBlur,
    isSmall,
    showBorder,
    maxWidth,
    minWidth,
    disableFocus,
    max,
    passRef,
    type,
  } = props

  const [minDate, setMinDate] = useState('')
  const [maxDate, setMaxDate] = useState('')
  const [onFocused, setOnFocused] = useState<boolean>(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentTimeZone } = globalSelector.timeZone

  const setMinMaxDateForFirstHalf = () => {
    const date = new Date()
    let firstDay = dayjs(new Date(date.getFullYear(), date.getMonth(), 1)).format('YYYY-MM-DD')
    const currentYear = date.getFullYear()
    const currentMonth = date.getMonth()
    let days = Number(daysInMonth(currentYear, currentMonth))
    let maxDateInFirstHalf = dayjs(new Date(date.getFullYear(), date.getMonth(), 1))
      .add(Math.floor(days / 2), 'days')
      .locale('en')
      .fromNow()
    setMinDate(firstDay)
    setMaxDate(dayjs(maxDateInFirstHalf).format('YYYY-MM-DD'))
  }

  const setMinMaxDateForSecondHalf = () => {
    const date = new Date()
    let lastDay = dayjs(new Date(date.getFullYear(), date.getMonth() + 1, 0)).format('YYYY-MM-DD')
    const currentYear = date.getFullYear()
    const currentMonth = date.getMonth()
    let days = Number(daysInMonth(currentYear, currentMonth))
    let minDateInSecondHalf = dayjs(new Date(date.getFullYear(), date.getMonth(), 1))
      .add(Math.floor(days / 2) + 1, 'days')
      .locale('en')
      .fromNow() // .calender
    setMinDate(dayjs(minDateInSecondHalf).format('YYYY-MM-DD'))
    setMaxDate(lastDay)
  }

  const getDate = () => {
    try {
      let value1 = value //dayjs(new Date(value)).format('YYYY-MM-DD')
      if (value1 !== 'Invalid Date') {
        // const dateValue = new Date(value1)
        // // let day = WEEK_DAYS[dateValue.getUTCDay()]
        // let date = dateValue.getDate()
        // let month = MONTHS[dateValue.getMonth()]
        // let year = dateValue.getFullYear()

        return dayjs(value1).format(type ? 'MMM, YYYY' : 'MMM DD, YYYY') //`${month} ${date}, ${year}`
      } else {
        return 'Invalid Date'
      }
    } catch (error) {
      console.error('getDate error', error)
    }
  }

  const handleOnFocus = () => {
    try {
      setOnFocused(true)
    } catch (error) {
      console.error('handleOnBlur error', error)
    }
  }

  const handleOnBlur = () => {
    try {
      setOnFocused(false)
      // if (value !== '' && onlyMmDd.test(value)) {
      //   let currentYear = new Date().getUTCFullYear()
      //   let value1 = `${value}/${currentYear}`
      //   value1 = dayjs(new Date(value1)).format('YYYY-MM-DD')
      //   setFieldValue(stateName, value1)
      // }
      // if (value !== '' && !onlyMmDd.test(value)) {
      //   let value1 = dayjs(new Date(value)).format('YYYY-MM-DD')
      //   setFieldValue(stateName, value1)
      // }
      if (setFieldValue) {
        setFieldValue(stateName, value)
      }
      onBlur && onBlur()
    } catch (error) {
      console.error('handleOnBlur error', error)
    }
  }

  useEffect(() => {
    if (firstHalf) {
      setMinMaxDateForFirstHalf()
    } else {
      setMinDate('')
      setMaxDate('')
    }
  }, [firstHalf])

  useEffect(() => {
    if (secondHalf) {
      setMinMaxDateForSecondHalf()
    } else {
      setMinDate('')
      setMaxDate('')
    }
  }, [secondHalf])

  const isLastPassInstalled = getDataFromLocalStorage(StorageKey.lastPassInstalled)

  return (
    <Styled.InputLabelDiv maxWidth={maxWidth} className={isLastPassInstalled ? 'lastpass' : ''}>
      {!isSmall ? (
        <Styled.InputFive
          $error={error ? error : value === 'Invalid Date'}
          minWidth={minWidth}
          small={isSmall}
          className={onFocused ? 'focus' : ''}
          onClick={() => {
            setOnFocused(true)
          }}
        >
          <div className="label-float">
            <Styled.InputField
              placeholder=" "
              name={stateName}
              small={isSmall}
              height={isSmall ? '24px' : '52px'}
              padding={isSmall ? '0px 4px' : null}
              type={type || 'date'}
              $error={error ? error : value === 'Invalid Date'}
              onFocus={() => handleOnFocus()}
              onBlur={() => handleOnBlur()}
              min={min}
              showborder={showBorder}
              isfocused={onFocused ? 'true' : 'false'}
              disabled={disabled}
              innerRef={passRef}
            />
            {!isSmall && (value || onFocused) ? <label>{labelName}</label> : null}
            {!isSmall && !value && !onFocused ? <span>{labelName}</span> : null}
          </div>
          {!onFocused && !disableFocus && (
            <Styled.DateContent
              active={value}
              $error={value === 'Invalid Date'}
              small={isSmall}
              onClick={(e: any) => {
                if (disabled) {
                  e.stopPropagation()
                  return
                }
                setOnFocused(true)
              }}
              className={'date-value'}
            >
              {value !== '' && getDate()}
            </Styled.DateContent>
          )}
        </Styled.InputFive>
      ) : (
        <Styled.SmallDateStyle>
          <Field
            placeholder=" "
            name={stateName}
            type="date"
            error={error ? error : value === 'Invalid Date'}
            onFocus={() => handleOnFocus()}
            onBlur={() => handleOnBlur()}
            min={min}
            max={max}
            showborder={showBorder}
            isfocused={onFocused ? 'true' : 'false'}
            disabled={disabled}
          />
        </Styled.SmallDateStyle>
      )}
      {error && (
        <Styled.ErrorMsg>
          <ErrorMessage name={stateName} />
        </Styled.ErrorMsg>
      )}
    </Styled.InputLabelDiv>
  )
}
