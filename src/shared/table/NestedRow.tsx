import { useEffect, useState, useCallback, useMemo, useRef } from 'react'

import * as Styled from './style'
import dayjs from 'dayjs'

import { NestedRowCont, RightSection, TableHead } from './style'
import * as SharedStyled from '../../styles/styled'
import { StorageKey } from '../helpers/constants'
import { dayjsFormat, getDataFromLocalStorage, getFormattedDate, notify } from '../helpers/util'
import NestedCard from './NestedCard'
import Button from '../components/button/Button'
import { Table } from './Table'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { getCrewById, getCrewMembers } from '../../logic/apis/crew'
import PromoteIcon from '../../assets/newIcons/promote.svg'
import EditIcon from '../../assets/newIcons/edit.svg'
import RemoveIcon from '../../assets/newIcons/delete.svg'
import { DoubleArrowUpIcon } from '../../assets/icons/DoubleArrowUpIcon'
import { CustomModal } from '../customModal/CustomModal'
import { AddCrewMemberForm } from '../../modules/crew/components/CrewMember/components/addCrewMemberForm/AddCrewMemberForm'
import { CrewPopup } from '../../modules/crew/components/CrewMember/components/crewPopup/CrewPopup'
import { EditCrewForm } from '../../modules/crew/components/CrewMember/components/editCrewForm/EditCrewForm'
import { colors } from '../../styles/theme'
import { RoundButton } from '../components/button/style'

interface INestedRowProps {
  selectedData: any
  refetchCrew?: any
}

const NestedRow = (props: INestedRowProps) => {
  const { selectedData, refetchCrew } = props

  const crewId = selectedData?._id

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
      },
      {
        Header: 'Date Joined',
        accessor: 'dateJoined',
      },
      {
        Header: 'Actions',
        accessor: 'actions',
      },
    ],
    []
  )

  const globalSelector = useAppSelector((state: any) => state)
  const { currentCompany, position } = globalSelector.company
  const [startDate, setStartDate] = useState<any>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [apiStartDate, setApiStartDate] = useState<any>('')

  const [showAddCrewMemberForm, setShowAddCrewMemberForm] = useState<boolean>(false)
  const [showEditCrewMemberForm, setShowEditCrewMemberForm] = useState<boolean>(false)
  const [showPromoteCrewMemberForm, setShowPromoteCrewMemberForm] = useState<boolean>(false)
  const [showRemoveCrewMemberForm, setShowRemoveCrewMemberForm] = useState<boolean>(false)
  const [showEditCrewForm, setShowEditCrewForm] = useState<boolean>(false)
  const [showRetireCrewForm, setShowRetireCrewForm] = useState<boolean>(false)
  const [currentCrewMemberId, setCurrentCrewMemberId] = useState('')
  const [currentMemberId, setCurrentMemberId] = useState('')
  const [currentCrewDate, setCurrentCrewDate] = useState('')

  const [detailsUpdate, setDetailsUpdate] = useState<boolean>(false)
  const [data, setData] = useState<any>([])
  const [noData, setNoData] = useState(true)
  const [crewName, setCrewName] = useState('')
  const fetchIdRef = useRef(0)
  const [update, setUpdate] = useState<boolean>(false)
  const [pageCount, setPageCount] = useState<number>(10)
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)

  const fetchData = useCallback(
    async ({ pageSize, pageIndex }: any) => {
      try {
        // This will get called when the table needs new data
        setLoading(true)
        let receivedData: any = []

        let crId: any = crewId
        let currentCompanyData: any = localStorage.getItem('currentCompany')

        const statusResponse = await getCrewMembers({ crewId: crId, deleted: false })

        if (statusResponse?.data?.statusCode === 200) {
          let statusRes = statusResponse?.data?.data?.crewMembers
          if (statusRes.length > 0) {
            setNoData(false)
          }

          statusRes.forEach((res: any, index: number) => {
            receivedData.push({
              name: res.promoted ? <Styled.PromotedName>{res.memberName}</Styled.PromotedName> : res.memberName,
              dateJoined: dayjsFormat(res.startDate, 'dddd, MMM D, YYYY'),
              // new Date(res.startDate.slice(0, 10)).toLocaleDateString('en-us', {
              //   weekday: 'long',
              //   year: 'numeric',
              //   month: 'short',
              //   day: 'numeric',
              // }),
              actions: (
                <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                  <>
                    <SharedStyled.TooltipContainer width={'100px'} className="crew">
                      <span className="tooltip-content">Edit</span>

                      <RoundButton
                        className="edit"
                        onClick={() => {
                          setShowEditCrewMemberForm(true)
                          setCurrentCrewDate(res.startDate)
                          setCurrentCrewMemberId(res._id)
                          setCurrentMemberId(res.memberId)
                        }}
                      >
                        <img src={EditIcon} alt="edit icon" />
                      </RoundButton>
                    </SharedStyled.TooltipContainer>
                  </>
                  {!res.promoted && (
                    <>
                      <SharedStyled.TooltipContainer width={'100px'} className="crew">
                        <span className="tooltip-content">Promote</span>
                        <RoundButton
                          className="promote"
                          onClick={() => {
                            setShowPromoteCrewMemberForm(true)
                            setCurrentCrewDate(res.startDate)
                            setCurrentCrewMemberId(res._id)
                            setCurrentMemberId(res.memberId)
                          }}
                        >
                          <img src={PromoteIcon} alt="promote icon" />
                        </RoundButton>
                      </SharedStyled.TooltipContainer>
                    </>
                  )}
                  {!res.promoted && (
                    <>
                      <SharedStyled.TooltipContainer width={'100px'} className="crew">
                        <span className="tooltip-content">Remove</span>
                        <RoundButton
                          className="remove"
                          onClick={() => {
                            setShowRemoveCrewMemberForm(true)
                            setCurrentCrewDate(res.startDate)
                            setCurrentCrewMemberId(res._id)
                            setCurrentMemberId(res.memberId)
                          }}
                        >
                          <img src={RemoveIcon} alt="remove icon" />
                        </RoundButton>
                      </SharedStyled.TooltipContainer>
                    </>
                  )}
                </SharedStyled.FlexBox>
              ),
            })
          })
        } else {
          notify(statusResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current

        // Set the loading state

        // We'll even set a delay to simulate a server here
        // setTimeout(() => {
        // Only update the data if this is the latest fetch
        if (fetchId === fetchIdRef.current) {
          const startRow = pageSize * pageIndex
          const endRow = startRow + pageSize
          setData(receivedData.slice(startRow, endRow))

          // Your server could send back total page count.
          // For now we'll just fake it, too
          // setPageCount(Math.ceil(receivedData.length / pageSize))
        }
        // }, 1000)
      } catch (error) {
        console.error('CrewMemberTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [update]
  )

  const getDetails = async () => {
    try {
      let crId: any = crewId

      // if (Object.keys(currentCompany).length > 0) {
      const statusResponse = await getCrewMembers({ crewId: crId, deleted: false })
      const statusResponse1 = await getCrewById({ _id: crId })

      if (statusResponse?.data?.statusCode === 200 && statusResponse1?.data?.statusCode === 200) {
        let statusRes = statusResponse?.data?.data?.crewMembers
        let statusRes1 = statusResponse1?.data?.data?.crew[0]
        setCrewName(statusRes1.name)
        setApiStartDate(statusRes1?.startDate)
        setStartDate(getFormattedDate(dayjs(statusRes1?.startDate).format('YYYY-MM-DD'), 'YYYY-MM-DD'))

        if (statusRes.length > 0) {
          setNoData(false)
          setShimmerLoading(false)
        } else {
          setNoData(true)
          setShimmerLoading(false)
        }
      } else {
        notify(statusResponse?.data?.message, 'error')
        setShimmerLoading(false)
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
      setShimmerLoading(false)
    }
  }

  useEffect(() => {
    if (crewId) getDetails()
  }, [detailsUpdate, crewId])

  const triggerRefetch = () => {
    refetchCrew?.()
  }

  return (
    <NestedRowCont>
      {/* <NestedCard
        data={selectedData}
        onEditClick={() => setShowEditCrewForm(true)}
        onRetireClick={() => setShowRetireCrewForm(true)}
        onRemoveClick={() => {}}
      /> */}

      <RightSection>
        <SharedStyled.FlexCol gap="12px">
          <SharedStyled.FlexRow justifyContent="space-between">
            <TableHead>Members of {selectedData?.name}</TableHead>
            <Button className="fit" onClick={() => setShowAddCrewMemberForm(true)}>
              Add Crew Member
            </Button>
          </SharedStyled.FlexRow>

          <Table
            columns={columns}
            data={data}
            loading={loading}
            // pageCount={pageCount}
            fetchData={fetchData}
          />
          <CustomModal show={showAddCrewMemberForm}>
            <AddCrewMemberForm
              setShowAddCrewMemberForm={setShowAddCrewMemberForm}
              setUpdate={setUpdate}
              setNoData={setNoData}
              startDate={startDate}
              crewId={crewId}
              apiStartDate={apiStartDate}
            />
          </CustomModal>

          <CustomModal show={showEditCrewMemberForm}>
            <CrewPopup
              setShowCrewPopup={setShowEditCrewMemberForm}
              header="Edit Crew Member"
              inputLabel="Date Joined"
              submitButtonColor={colors.lightBlue}
              buttonText="Change Date"
              loaderText="Changing.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              setUpdate={setUpdate}
              startDate={startDate}
              crewId={crewId}
              currentCrewDate={currentCrewDate}
            />
          </CustomModal>
          <CustomModal show={showPromoteCrewMemberForm}>
            <CrewPopup
              setShowCrewPopup={setShowPromoteCrewMemberForm}
              header="Promote Crew Member"
              inputLabel="Date Promoted To Foreman"
              submitButtonColor={colors.lightBlue}
              buttonText="Promote"
              loaderText="Promoting.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              setUpdate={setUpdate}
              startDate={startDate}
              crewId={crewId}
              currentCrewDate={currentCrewDate}
            />
          </CustomModal>
          <CustomModal show={showRemoveCrewMemberForm}>
            <CrewPopup
              setShowCrewPopup={setShowRemoveCrewMemberForm}
              header="Remove Crew Member"
              inputLabel="Last Day On Crew"
              submitButtonColor={colors.error}
              buttonText="Remove Member"
              loaderText="Removing.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              setUpdate={setUpdate}
              startDate={startDate}
              crewId={crewId}
              currentCrewDate={currentCrewDate}
            />
          </CustomModal>
          <CustomModal show={showRetireCrewForm}>
            <CrewPopup
              setShowCrewPopup={setShowRetireCrewForm}
              header="Retire Crew"
              inputLabel="Retire Crew Date"
              submitButtonColor={colors.error}
              buttonText="Retire Crew"
              loaderText="Retiring Crew.."
              currentCrewMemberId={currentCrewMemberId}
              currentMemberId={currentMemberId}
              startDate={startDate}
              crewId={crewId}
              triggerRefetch={triggerRefetch}
            />
          </CustomModal>
          {/* <CustomModal show={showEditCrewForm}>
            <EditCrewForm
              setShowEditCrewFormPopup={setShowEditCrewForm}
              setDetailsUpdate={setDetailsUpdate}
              startDate={startDate}
              crewId={crewId}
              triggerRefetch={triggerRefetch}
            />
          </CustomModal> */}
        </SharedStyled.FlexCol>
      </RightSection>
    </NestedRowCont>
  )
}

export default NestedRow
