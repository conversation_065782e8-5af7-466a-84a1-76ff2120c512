import { rgba } from 'polished'
import styled from 'styled-components'
import { colors, screenSizes } from '../../styles/theme'
import { Nue } from '../helpers/constants'

export const TableContainer = styled.table<any>`
  border-collapse: collapse;
  min-width: ${({ minWidth }: any) => minWidth ?? '768px'};

  width: 100%;
  min-height: 90px;
  tbody {
    cursor: ${(props) => (props.noLink ? '' : 'pointer')};

    /* td {
      :hover {
        background: ${(props) => (props.edit ? colors.grey2 : '')};
        /* input {
          background: ${colors.grey2};
        } */
    /* }
    } */
  }

  @media (max-width: ${screenSizes.M}px) {
    max-width: 768px;
  }

  border-radius: 12px 12px 0px 0px;
  border: 1px solid ${colors.lightGray};
`

export const TableOuterContainer = styled.div<any>`
  /* max-width: 700px; */
  /* margin-top: 20px; */
  width: 100%;
  overflow-x: ${(props) => (props.noOverflow ? '' : 'auto')};
  overflow-y: hidden;
  @media (max-width: ${screenSizes.M}px) {
    overflow-x: auto;
    overflow-y: hidden;
    overflow: -moz-scrollbars-none;
    :-webkit-scrollbar {
      height: 6px;
    }
    scrollbar-width: thick;
  }
`

export const ShowEntriesDiv = styled.div`
  display: flex;
  align-items: center;
  gap: 5px;
`
export const ShowText = styled.p`
  margin: 0;
  font-size: 12px;
  font-weight: 300;
`

export const SelectDiv = styled.select`
  cursor: pointer;
  padding: 6px;
  font-size: 12px;
`

export const SelectOption = styled.option``

export const SearchContainer = styled.div`
  min-width: 768px;
  width: 100%;
  padding: 10px;
  border: 1px solid ${colors.grey3};
  border-radius: 5px 5px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`

export const SearchInput = styled.input`
  padding: 6px 14px;
  font-size: 16px;
  max-width: 200px;
  width: 100%;
  border: none;
  outline: none;
  border: 0.5px outset ${colors.grey3};
  border-right: none;
  border-radius: 5px;
  @media (min-width: ${screenSizes.M}px) {
    max-width: 320px;
    padding: 10px 14px;
  }
`

export const TableHeader = styled.thead`
  th {
    color: ${colors.gray};
    font-family: ${Nue.medium};
    font-size: 11px;
    font-weight: 500;
    line-height: 20px;
    text-transform: uppercase;
    text-align: left;
    padding: 10px 12px;
    background: #f9f9f9;
    white-space: nowrap;

    @media (min-width: ${screenSizes.S}px) {
      padding: 14px 24px;
    }
  }
`
export const TableDataDrag = styled.td<any>`
  border: 1px solid ${colors.grey3};
  border-width: ${(props) => (props.noBorder ? '1px 0px' : `1px`)};
  text-align: left;
  padding: ${(props) => props.padding ?? '5px 7px'};
  width: 10px;
  color: ${(props) => (props?.color ? props?.color : colors.black)};
  /* font-family: ${Nue.medium}; */
  font-size: 12px;
  border: 1px solid ${colors.lightGray};
  border-left: none;
  border-right: none;

  @media (min-width: ${screenSizes.S}px) {
    padding: ${(props) => props.padding ?? '10px 14px'};
  }

  p {
    font-family: ${Nue.regular};
  }
`

export const TableData = styled.td<any>`
  border: 1px solid ${colors.grey3};
  border-width: ${(props) => (props.noBorder ? '1px 0px' : `1px`)};
  text-align: left;
  padding: ${(props) => props.padding ?? '12px 16px'};

  color: ${(props) => (props?.color ? props?.color : colors.black)};
  /* font-family: ${Nue.medium}; */
  font-size: 12px;
  border: 1px solid ${colors.lightGray};
  border-left: none;
  border-right: none;

  @media (min-width: ${screenSizes.S}px) {
    padding: ${(props) => props.padding ?? '20px 24px'};
  }

  p {
    font-family: ${Nue.regular};
  }
`
interface I_TableHeading {
  noBorder?: boolean
  padding?: boolean
}

export const TableHeading = styled.th<I_TableHeading>`
  /* border: ${`1px solid ${colors.grey3}`}; */
  /* border-width: ${(props) => (props.noBorder ? '1px 0px' : `1px`)}; */
  text-align: left;
  padding: ${(props) => props.padding ?? '8px'};
`
export const TableRow = styled.tr<any>`
  color: ${(props) => props.color};
  /* Table Link */
  display: table-row;
  text-decoration: none;
  vertical-align: middle;

  .dragging-row {
    width: 100% !important;
    display: table-row !important;
    color: red;
  }

  /* background-color: ${(props) => (props.type === 'even' ? '#f2f2f2' : '')}; */
  :hover {
    background: ${(props) => (props.noLink ? '' : colors.grey2)};
    cursor: ${(props) => (props.noLink ? 'default' : 'pointer')};
  }

  &.active {
    background: #effbeb;
  }
  &.inactive {
    /* background: #dddddd; */
    td {
      font-family: ${Nue.italic};
      color: ${colors.gray};
    }
  }
`

export const TableBody = styled.tbody<any>`
  position: relative;
  background: ${(props) => (props.filterLoading ? rgba('#e1e1e1', 0.7) : '#fff')};
`

export const Pagination = styled.div<any>`
  /* min-width: ${(props) => props?.minWidth ?? '768px'}; */
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 10px;
  border: 1px solid ${colors.grey3};
  border-radius: 0px 0px 5px 5px;
`

export const PaginationNavigationDiv = styled.div``

export const PageSpan = styled.span`
  font-size: 12px;
`

export const PageStrong = styled.strong`
  color: ${colors.darkGrey};
`

export const InputNumber = styled.input``

// interface I_IconContainer {
//     disa
// }

export const IconContainer = styled.button`
  border: none;
  background: none;
  opacity: ${(props) => (props.disabled ? '0.5' : '1')};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  svg {
    width: 15px;
    height: 15px;
  }

  font-size: 12px;
  @media (min-width: ${screenSizes.S}px) {
    svg {
      width: 25px;
      height: 25px;
    }
  }
`

export const LoaderContainer = styled.tr`
  position: absolute;
  padding: 5px 0px;
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  .loading:after {
    content: ' . . .';
    animation: dots 1.2s steps(5, end) infinite;
  }

  @keyframes dots {
    0%,
    20% {
      color: rgba(0, 0, 0, 0);
      text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    40% {
      color: white;
      text-shadow: 0.25em 0 0 rgba(0, 0, 0, 0), 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    60% {
      text-shadow: 0.25em 0 0 white, 0.5em 0 0 rgba(0, 0, 0, 0);
    }
    80%,
    100% {
      text-shadow: 0.25em 0 0 white, 0.5em 0 0 white;
    }
  }

  border: 1px solid ${colors.lightGray};
`

export const LoaderContent = styled.p`
  /* font-family: NunitoSansSemiBold; */
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  color: ${colors.darkGrey};
`

// ========================================= New =========================================

export const StyledTable = styled.table`
  width: 100%;
  border-spacing: 0;
  border-radius: 12px 12px 0px 0px;
  border: 1px solid ${colors.lightGray};
  position: relative;

  thead {
    background: #f9f9f9;
  }

  th {
    color: ${colors.gray};
    font-family: ${Nue.medium};
    font-size: 11px;
    font-weight: 500;
    line-height: 20px;
    text-transform: uppercase;
    text-align: left;
    padding: 10px 12px;
    border-radius: 12px 12px 0px 0px;

    @media (min-width: ${screenSizes.S}px) {
      padding: 14px 24px;
    }
  }

  td {
    padding: 12px 16px;

    color: ${colors.black};
    font-family: ${Nue.medium};
    font-size: 12px;
    border: 1px solid ${colors.lightGray};
    border-left: none;
    border-right: none;
    text-transform: capitalize;

    @media (min-width: ${screenSizes.S}px) {
      padding: 20px 24px;
    }
  }

  &.scroll {
    display: block;
    overflow-x: scroll;
    th,
    td {
      white-space: nowrap;
    }
    @media (min-width: ${screenSizes.S}px) {
      display: table;
      overflow-x: hidden;
    }
  }

  tbody {
    tr:hover {
      transition: 250ms ease-in-out;
      background: ${rgba(colors.gray, 0.2)};
    }
    overflow-y: scroll;
  }

  &.pointer {
    tbody {
      tr {
        cursor: pointer;
      }
    }
  }

  .empty {
    text-align: center;
    pointer-events: none;
    h5 {
      font-size: 14px;
      user-select: none;
    }
  }

  .nestedRow {
    &:hover {
      background: transparent;
    }
  }

  .rotate {
    transform: rotate(180deg);
  }
`

export const NestedRowCont = styled.section`
  display: flex;
  gap: 32px;
`

export const NestedCardCont = styled.div`
  padding: 16px 24px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(156, 145, 145, 0.25);
  max-width: 280px;

  p {
    color: ${colors.black};
    font-family: ${Nue.medium};
    font-size: 12px;
  }

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 1px solid ${colors.gray1};
  }

  span {
    color: ${colors.gray};
    font-family: ${Nue.medium};
    font-size: 11px;
    text-transform: uppercase;
  }

  .delete {
    margin-top: auto;
  }

  .height {
    height: 100%;
  }
`

export const RightSection = styled.section`
  width: 100%;

  table {
    flex: 1 0 100%;

    td {
      padding: 8px 24px;
      text-align: left;
    }
  }
`

export const TableHead = styled.p`
  color: ${colors.black};
  font-family: ${Nue.medium};
  font-size: 14px;
`

export const PromotedName = styled.p`
  margin: 0;
  font-size: 12px;
  font-weight: bold;
`

export const NewTableValue = styled.p`
  font-family: ${Nue.regular};
  color: ${colors.gray};
  /* white-space: nowrap; */
  &.smallText {
    font-size: 12px;
  }
`

export const NewTableBoldValue = styled.p`
  font-family: ${Nue.bold} !important;
  font-size: 16px;
  color: ${colors.black};
`

export const PageInput = styled.input`
  width: 40px;
  text-align: center;
  padding: 4px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin: 0 8px;
  font-size: 12px;
`
