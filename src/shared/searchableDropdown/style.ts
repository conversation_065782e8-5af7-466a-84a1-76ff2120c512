import { Field } from 'formik'
import styled from 'styled-components'
import { colors } from '../../styles/theme'
import { Nue } from '../helpers/constants'

export const DropDownContentContainer = styled.div<{ $visibility?: boolean; height?: string; borderRadius?: string }>`
  display: ${(props) => (props.$visibility ? 'block' : 'none')};
  position: absolute;
  border: 1px solid ${colors.darkGrey};
  width: 100%;
  max-height: ${(props) => (props.height ? props.height : 'fit-content')};
  top: 68px;
  left: 0px;
  height: ${(props) => (props.height ? props.height : '320px')};
  overflow-y: auto;
  background: ${colors.white};
  z-index: 2;

  // Force hardware acceleration
  transform: translateZ(0);
  will-change: transform;
`

export const DropDownItem = styled.div<any>`
  font-size: 14px;
  padding: 6px;
  font-weight: ${(props) => props.fontWeight};
  border-radius: ${(props) => (props.borderRadius ? props.borderRadius : '8px')};
  width: 100%;
  background: ${(props) => (props.active ? `${colors.darkGrey}` : `${colors.white}`)};
  color: ${(props) => (props.active ? `${colors.white}` : `${colors.darkGrey}`)};
  :hover {
    background: ${(props) => (props.noHover ? '' : `${colors.darkGrey}`)};
    color: ${(props) => (props.noHover ? '' : ` ${colors.white}`)};
  }
  /* text-transform: capitalize; */

  .project-info {
    .label {
      color: #888;
    }
  }
`

export const DropDownOuterContainer = styled.div<{ height?: string }>`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  &.clientHeight {
    ${DropDownContentContainer} {
      max-height: 300px;
    }
  }

  &.reverse {
    ${DropDownContentContainer} {
      top: inherit;
      bottom: 68px;
    }
  }

  &.relative {
    ${DropDownContentContainer} {
      position: relative;
      top: 10px;
    }
  }

  &.referral {
    margin-left: -6px;

    cursor: pointer;

    .label-float {
      padding: 0px;
    }

    select {
      border: none;
    }

    input {
      width: 250px !important;
      border: none;
      height: 20px;
      cursor: pointer;
    }

    ${DropDownItem} {
      font-size: 13px;
    }

    ${DropDownContentContainer} {
      top: 34px;
    }
  }
`

export const DropDownContainer = styled.div<any>`
  position: relative;
  width: 100%;
`

export const DropDownLabel = styled.label`
  color: ${colors.darkGrey};
  position: absolute;
  font-size: 12px;
  top: 5px;
  left: 17px;
`
