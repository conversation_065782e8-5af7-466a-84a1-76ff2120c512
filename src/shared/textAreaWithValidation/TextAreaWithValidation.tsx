import { ErrorMessage, Field } from 'formik'
import { useState } from 'react'
import * as Styled from './style'

interface I_TextAreaProps {
  stateName: string
  labelName: string
  disabled?: boolean
  error?: boolean
  value?: string
  passRef?: any
  height?: string
  maxWidth?: string
  onBlur?: any
  onChange?: any
  placeholder?: string
  marginTop?: string
}

export const TextAreaWithValidation = (props: I_TextAreaProps) => {
  const { stateName, labelName, disabled, error, passRef, height, maxWidth, onBlur, onChange, placeholder, marginTop } =
    props

  return (
    <Styled.TextAreaWithValidationContainer maxWidth={maxWidth}>
      <Styled.TextAreaWrapper $error={error} marginTop={marginTop}>
        <div className="label-float">
          <Field name={stateName}>
            {({ field, form }: any) => (
              <Styled.TextAreaField
                {...field}
                component="textarea"
                placeholder=" "
                disabled={disabled}
                $error={error}
                height={height}
                onBlur={(e: any) => {
                  field.onBlur(e)
                  onBlur && onBlur(e)
                }}
                onChange={(e: any) => {
                  field.onChange(e)
                  onChange && onChange(e)
                }}
                ref={passRef}
              />
            )}
          </Field>
          <label>{labelName}</label>
        </div>
      </Styled.TextAreaWrapper>

      {error && (
        <Styled.ErrorContainer>
          <Styled.ErrorMsg>
            <ErrorMessage name={stateName} />
          </Styled.ErrorMsg>
        </Styled.ErrorContainer>
      )}
    </Styled.TextAreaWithValidationContainer>
  )
}
