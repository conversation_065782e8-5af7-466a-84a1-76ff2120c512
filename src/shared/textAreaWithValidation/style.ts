import styled from 'styled-components'
import { colors, screenSizes } from '../../styles/theme'
import { Nue } from '../helpers/constants'

interface ITextAreaContainerProps {
  maxWidth?: string
}

export const TextAreaWithValidationContainer = styled.div<ITextAreaContainerProps>`
  width: 100%;
  max-width: ${(props) => props.maxWidth || '100%'};
  position: relative;
`

interface ITextAreaWrapperProps {
  $error?: boolean
  marginTop?: string
}

export const TextAreaWrapper = styled.div<ITextAreaWrapperProps>`
  width: 100%;
  margin-top: ${(props) => props.marginTop || '0px'};

  .label-float {
    width: 100%;
    position: relative;
    padding-top: 8px;
    z-index: 0;
  }

  .label-float textarea {
    border-radius: 8px;
    outline: none;
    width: 100%;
    transition: all 0.1s linear;
    -webkit-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    -webkit-appearance: none;
    resize: vertical;
  }

  .label-float textarea:focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 2px 0px;
    background-color: ${colors.white};
  }

  .label-float textarea::placeholder {
    color: transparent;
  }

  .label-float label {
    pointer-events: none;
    position: absolute;
    top: calc(50% - 11px);
    left: 11px;
    font-size: 16px;
    transition: all 0.1s linear;
    -webkit-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    background-color: white;
    padding: 5px;
    box-sizing: border-box;
    color: ${colors.lightBlack};
    background: transparent;
    font-family: ${Nue.regular};

    @media (max-width: ${screenSizes.XS}px) {
      font-size: 12px;
    }
  }

  .label-float textarea:focus + label,
  .label-float textarea:not(:placeholder-shown) + label,
  .label-float textarea:-webkit-autofill + label {
    font-size: 12px;
    top: 9px;
    color: ${colors.text};
    font-family: ${Nue.regular};
  }

  .label-float textarea:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 30px white inset;
    box-shadow: 0 0 0 30px white inset;
  }

  .label-float textarea:-webkit-autofill ~ label {
    font-size: 12px;
    top: 9px;
    color: ${colors.text};
    font-family: ${Nue.regular};
  }
`

interface ITextAreaFieldProps {
  $error?: boolean
  height?: string
}

export const TextAreaField = styled.textarea<ITextAreaFieldProps>`
  border: ${(props) => (props.$error ? `1px solid ${colors.lightRed1}` : `1px solid ${colors.lightGray}`)};
  box-sizing: border-box;
  border-radius: 8px;
  width: 100%;
  font-family: ${Nue.medium};
  font-size: 16px;
  font-weight: 500;
  resize: vertical;
  height: ${(props) => props.height || '80px'};
  padding: 20px 16px 8px;
  min-height: 80px;
  background: ${(props) =>
    props.disabled ? `${colors.grey2}` : props.$error ? `${colors.lightRed}` : `${colors.white}`};
  color: ${colors.lightBlack};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'text')};

  @media (min-width: ${screenSizes.M}px) {
    height: ${(props) => props.height || '80px'};
  }

  @media (max-width: ${screenSizes.XS}px) {
    font-size: 12px;
  }
`

export const ErrorContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
`

export const ErrorMsg = styled.div`
  color: ${colors.errorRed};
  font-size: 10px;
  padding-top: 5px;
  transition: all 0.3s ease;
`
