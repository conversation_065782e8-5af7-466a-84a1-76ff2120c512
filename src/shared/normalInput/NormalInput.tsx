import { ErrorMessage } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { EyeIcon } from '../../assets/icons/EyeIcon'
import { EyeSlashIcon } from '../../assets/icons/EyeSlashIcon'
import { TickmarkIcon } from '../../assets/icons/TickmarkIcon'
import { oneLowercase, oneNumber, oneSpecialCharacter, oneUppercase } from '../helpers/regex'
import { useClickOutside } from '../hooks/useClickOutside'
import * as Styled from './style'

interface I_InputProps {
  type?: string
  stateName: string
  labelName: string
  twoInput?: boolean
  disabled?: boolean
  error?: boolean
  value?: string
  showValidationCard?: boolean
  passRef?: any
  padding?: string
  onChange?: any
  notFormik?: boolean
  defaultValue?: string
  onFocusCallback?: any
  noMessage?: any
  onTab?: () => void
  selectTextOnFocus?: boolean
}

interface I_PasswordValidations {
  eightCharacters: object
  oneLowercase: object
  oneUppercase: object
  oneNumber: object
}

export const NormalInput = (props: I_InputProps) => {
  const {
    type,
    stateName,
    labelName,
    twoInput,
    disabled,
    error,
    onFocusCallback,
    value,
    showValidationCard,
    passRef,
    padding,
    notFormik,
    onChange,
    noMessage,
    selectTextOnFocus,
    onTab,
    defaultValue,
  } = props

  const [passwordValidationCard, setShowPasswordValidationCard] = useState<boolean>(false)
  const [showPassword, setShowPassword] = useState<boolean>(false)
  const [passwordType, setPasswordType] = useState<string>('password')

  const [passwordValidations, setPasswordValidations] = useState<any>({
    eight: { value: false, text: 'at least 8 characters' },
    lowercase: { value: false, text: 'at least 1 lowercase letter' },
    uppercase: { value: false, text: 'at least 1 uppercase' },
    number: { value: false, text: 'at least 1 number' },
    special: { value: false, text: 'at least 1 special character' },
  })

  // const ref = useRef(null)
  // useClickOutside(ref, setShowPasswordValidationCard)

  const onChangePassword = (value: any) => {
    const obj = { ...passwordValidations }
    if (value?.length >= 8) {
      obj.eight.value = true
    } else {
      obj.eight.value = false
    }

    if (oneLowercase?.test(value)) {
      obj.lowercase.value = true
    } else {
      obj.lowercase.value = false
    }
    if (oneUppercase?.test(value)) {
      obj.uppercase.value = true
    } else {
      obj.uppercase.value = false
    }
    if (oneNumber?.test(value)) {
      obj.number.value = true
    } else {
      obj.number.value = false
    }
    if (oneSpecialCharacter?.test(value)) {
      obj.special.value = true
    } else {
      obj.special.value = false
    }
    setPasswordValidations(obj)
  }

  // const handlePasswordVisibility = () => {
  //   if()
  //   setShowPassword((prev) => !prev)
  // }

  useEffect(() => {
    if (value) {
      onChangePassword(value)
    } else {
      setPasswordValidations({
        eight: { value: false, text: 'at least 8 characters' },
        lowercase: { value: false, text: 'at least 1 lowercase letter' },
        uppercase: { value: false, text: 'at least 1 uppercase' },
        number: { value: false, text: 'at least 1 number' },
        special: { value: false, text: 'at least 1 special character' },
      })
    }
  }, [value])

  return (
    <>
      <Styled.InputWithValidationContainer twoInput={twoInput}>
        <Styled.InputFive error={error} className={type === 'password' && passwordValidationCard ? 'password' : ''}>
          <div className="label-float">
            <Styled.InputField
              padding={padding}
              type={type === 'time' ? type : type ? (showPassword ? 'text' : 'password') : null}
              placeholder=" "
              name={stateName}
              // marginTop="8px"
              defaultValue={defaultValue}
              disabled={disabled}
              error={error}
              onFocus={(e: any) => {
                selectTextOnFocus && e.target.select()
                setShowPasswordValidationCard(true)
                onFocusCallback && onFocusCallback()
              }}
              onBlur={() => {
                onTab?.()
                setTimeout(() => setShowPasswordValidationCard(false), 200)
              }}
              innerRef={passRef}
              onChange={onChange}
              value={value}
              autoComplete={'off'}
            />
            <label>{labelName}</label>
          </div>
        </Styled.InputFive>
        {type === 'password' && passwordValidationCard && showValidationCard && (
          <Styled.PasswordValidationCard>
            {Object.entries(passwordValidations).map(([key, values]: any, index: number) => (
              <Styled.TickmarkContainer key={index}>
                <Styled.TickmarkIconDiv matched={values?.value}>
                  <TickmarkIcon />
                </Styled.TickmarkIconDiv>
                <Styled.TickmarkForValidation matched={values?.value}>{values?.text}</Styled.TickmarkForValidation>
              </Styled.TickmarkContainer>
            ))}
          </Styled.PasswordValidationCard>
        )}
        {type === 'password' && passwordValidationCard ? (
          ''
        ) : (
          <>
            {error && !notFormik && (
              <Styled.ErrorContainer>
                {!noMessage && (
                  <Styled.ErrorMsg>
                    {/* @ts-ignore */}
                    <ErrorMessage name={stateName} />
                  </Styled.ErrorMsg>
                )}
              </Styled.ErrorContainer>
            )}
          </>
        )}
        {type === 'password' && (
          <Styled.ShowPasswordDiv onClick={() => setShowPassword((prev) => !prev)}>
            {showPassword ? <EyeIcon /> : <EyeSlashIcon />}
          </Styled.ShowPasswordDiv>
        )}
      </Styled.InputWithValidationContainer>
    </>
  )
}
