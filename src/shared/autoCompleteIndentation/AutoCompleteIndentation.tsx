import { ErrorMessage } from 'formik'
import React, { useEffect, useRef, useState } from 'react'

import { DownArrowIcon } from '../../assets/icons/DownArrowIcon'
import { UpArrowIcon } from '../../assets/icons/UpArrowIcon'
import { getNameFrom_Id } from '../helpers/util'
import { useClickOutside } from '../hooks/useClickOutside'
import { NormalInput } from '../normalInput/NormalInput'
import * as Styled from './style'
import { SearchLoader } from '../components/loader/style'
import { Text } from '../../styles/styled'

type DataStructure = Record<string, Record<string, string[]>>

const filterItems = (data: DataStructure, searchText: string): DataStructure => {
  if (!searchText) return data

  const lowerSearchText = searchText.toLowerCase()
  let filteredData: any = {}

  Object.entries(data)?.forEach(([category, subcategoriesData]) => {
    // Handle data structure type "b" (array of objects)
    if (Array.isArray(subcategoriesData)) {
      const filteredSubcategories = subcategoriesData
        .map((subcatObj) => {
          const subcatName = Object.keys(subcatObj)[0]
          const items = subcatObj[subcatName]

          // Filter items that match search text
          const filteredItems = items?.filter((item) => item.toLowerCase().includes(lowerSearchText))

          // Include subcategory if its name matches search or if it has matching items
          if (filteredItems.length > 0 || subcatName.toLowerCase().includes(lowerSearchText)) {
            return { [subcatName]: filteredItems }
          }
          return null
        })
        .filter(Boolean) as Array<Record<string, string[]>>

      if (filteredSubcategories.length > 0) {
        filteredData[category] = filteredSubcategories
      }
    }
    // Handle data structure type "a" (object with subcategories)
    else {
      let filteredSubcategories: Record<string, string[]> = {}

      Object.entries(subcategoriesData)?.forEach(([subcategory, items]) => {
        // Filter items that match search text
        const filteredItems = items?.filter((item) => item.toLowerCase().includes(lowerSearchText))

        // Include subcategory if its name matches search or if it has matching items
        if (filteredItems.length > 0 || subcategory.toLowerCase().includes(lowerSearchText)) {
          filteredSubcategories[subcategory] = filteredItems
        }
      })

      if (Object.keys(filteredSubcategories).length > 0) {
        filteredData[category] = filteredSubcategories
      }
    }
  })

  return filteredData
}
interface I_AutoCompleteProps {
  addNewText?: string
  apiSearch?: boolean
  className?: string
  disabled?: boolean
  error?: boolean
  labelName: string
  onAddClick?: any
  onChange?: any
  options: Array<string>
  setFieldValue?: any
  setSearchTerm?: any
  setValueOnClick?: any
  showAddOption?: boolean
  stateName: string
  value?: string
  setTypeForAction?: any
  dropdownHeight?: string
  borderRadius?: string
  selectedValue?: string
  validate?: boolean
  preSelected?: boolean
  searchLoader?: boolean
  isIndentation?: boolean
  formatedOptions?: any
  isLeadSource?: boolean
  onBlur?: (name: string) => void
  onDropdownToggle?: (isOpen: boolean) => void
}

const AutoCompleteIndentation: React.FC<I_AutoCompleteProps> = (props) => {
  const {
    addNewText,
    apiSearch,
    className,
    disabled,
    error,
    labelName,
    onAddClick,
    onChange,
    options,
    setFieldValue,
    setSearchTerm,
    setValueOnClick,
    showAddOption,
    stateName,
    value,
    setTypeForAction,
    dropdownHeight,
    borderRadius,
    selectedValue,
    validate,
    searchLoader,
    preSelected,
    isIndentation,
    formatedOptions,
    isLeadSource,
    onBlur,
    onDropdownToggle,
  } = props

  const [filterOptions, setFilterOptions] = useState(() => options)
  const [showDropdown, setShowDropdownState] = useState<boolean>(false)
  const [onFocused, setOnFocused] = useState<boolean>(false)
  const [localSearchTerm, setLocalSearchTerm] = useState('')

  const setShowDropdown = (value: boolean | ((prev: boolean) => boolean)) => {
    const newValue = typeof value === 'function' ? value(showDropdown) : value
    setShowDropdownState(newValue)
    onDropdownToggle?.(newValue)
  }

  const [filteredData, setFilteredData] = useState<any>({})

  const ref = useRef(null)
  const ref1 = useRef(null)

  useClickOutside(ref, setShowDropdown)
  useClickOutside(ref1, setOnFocused)

  useEffect(() => {
    if (apiSearch || !value) setFilterOptions(options)
  }, [options])

  useEffect(() => {
    if (preSelected && !localSearchTerm) setFilterOptions(options)
  }, [options, localSearchTerm])

  const handleFilterOptions = (str: string) => {
    setFilterOptions((prev) => {
      if (str === '') return options
      let newOptions = options?.filter((item) => item.toLowerCase().includes(str.toLowerCase()))
      return newOptions
    })

    setFilteredData(filterItems(formatedOptions, str))
  }
  const handleSelectOption = (name: string) => {
    if (name) {
      !preSelected && handleFilterOptions(name)
      if (setValueOnClick) setValueOnClick(name)
      setShowDropdown((prev) => !prev)
      if (preSelected) {
        setLocalSearchTerm('')
      }
    }

    onBlur && onBlur(name)
  }
  const handleInputChange = (str: string) => {
    setShowDropdown(true)

    setFieldValue && setFieldValue(stateName, str)
    onChange && onChange(str)
    handleFilterOptions(str)
    if (setSearchTerm) {
      setSearchTerm(str)
    }

    if (isIndentation) {
      setLocalSearchTerm(str)
    }
    if (str && preSelected) {
      setLocalSearchTerm(str)
    }
    if (setTypeForAction) {
      setTypeForAction(str)
    }
  }

  return (
    <Styled.DropDownOuterContainer
      ref={ref1}
      className={className}
      onBlur={() => {
        if (!filterOptions?.length && validate) {
          setFieldValue(stateName, selectedValue)
        }
      }}
    >
      <Styled.DropDownContainer
        marginTop="8px"
        onClick={
          disabled
            ? () => {}
            : () => {
                setShowDropdown((prev) => !prev)
                setOnFocused((prev) => !prev)
              }
        }
        ref={ref}
        focus={onFocused}
        error={error}
        disabled={disabled}
        onKeyDown={(e: KeyboardEvent) => {
          if (e.key === 'Tab') {
            setOnFocused(false)
            setShowDropdown(false)
          }
        }}
      >
        <NormalInput
          padding={'20px 40px 8px 16px'}
          labelName={labelName}
          stateName={stateName}
          error={error}
          twoInput={true}
          value={value}
          noMessage
          disabled={disabled}
          selectTextOnFocus
          onChange={(e: any) => {
            handleInputChange(e.target.value)
          }}
        />

        <Styled.DropdownIconDiv
          onClick={
            disabled
              ? () => {}
              : (e) => {
                  e.stopPropagation()
                  setShowDropdown((prev) => !prev)
                }
          }
        >
          {searchLoader ? (
            <SearchLoader />
          ) : className === 'lead-source' ? null : showDropdown ? (
            <UpArrowIcon />
          ) : (
            <DownArrowIcon />
          )}
        </Styled.DropdownIconDiv>
        <Styled.DropDownContentContainer $visibility={showDropdown} height={dropdownHeight} borderRadius={borderRadius}>
          {isIndentation ? (
            Object?.entries(localSearchTerm ? filteredData : formatedOptions)?.map(([category, subcategoriesData]) => (
              <div key={category}>
                {/* Category Header */}
                <Text
                  fontSize="16px"
                  margin="0 0 10px 10px"
                  fontWeight="700"
                  style={{
                    textDecoration: isLeadSource ? 'underline' : 'none',
                  }}
                >
                  {category}
                </Text>

                {/* Handle both data structures */}
                {Array.isArray(subcategoriesData)
                  ? subcategoriesData.map((subcatObj, index) => {
                      // For format b - where subcategories is an array of objects
                      const subcatName = Object.keys(subcatObj)[0]
                      const items = subcatObj[subcatName]

                      // For rendering the subcategory itself as a selectable option
                      const subcatFullPath = `${category}[${index}]["${subcatName}"]`
                      const isSubcatSelected = value === subcatName

                      return (
                        <div key={`${subcatName}-${index}`}>
                          {/* Render the subcategory as a selectable option */}
                          <Styled.DropDownItem
                            style={{ margin: '0 0 0 18px' }}
                            borderRadius={borderRadius}
                            onClick={() => {
                              setFieldValue && setFieldValue(stateName, subcatFullPath)
                              onChange && onChange(subcatFullPath)
                              setShowDropdown(false)

                              handleSelectOption(subcatName)
                            }}
                            active={isSubcatSelected}
                          >
                            {/* <Text fontSize="14px" fontWeight="600"> */}
                            <b>{subcatName}</b>
                            {/* </Text> */}
                          </Styled.DropDownItem>

                          {/* Render the items if they exist and match filter */}
                          {items && items.length > 0 && (
                            <>
                              {items
                                .filter((v) => !options || options.includes(v))
                                .map((data, itemIndex) => {
                                  const itemFullPath = `${category}[${index}]["${subcatName}"][${itemIndex}]`
                                  return (
                                    <Styled.DropDownItem
                                      style={{ margin: '0 0 0 28px' }}
                                      key={itemIndex}
                                      borderRadius={borderRadius}
                                      onClick={() => {
                                        setFieldValue && setFieldValue(stateName, itemFullPath)
                                        onChange && onChange(itemFullPath)
                                        setShowDropdown(false)
                                        handleSelectOption(data)
                                      }}
                                      active={value === data}
                                    >
                                      {data}
                                    </Styled.DropDownItem>
                                  )
                                })}
                            </>
                          )}
                        </div>
                      )
                    })
                  : Object.entries(subcategoriesData).map(([subcategory, items]) => {
                      // For format a - where subcategories is an object
                      // Make the subcategory itself selectable
                      const subcatFullPath = `${category}.${subcategory}`

                      return (
                        <div key={subcategory}>
                          {/* Render the subcategory as a selectable option */}

                          <Text fontSize="14px" fontWeight="600" style={{ margin: '0 0 0 18px' }}>
                            {subcategory}
                          </Text>

                          {/* Render the items */}
                          {items && items.length > 0 && (
                            <>
                              {items
                                .filter((v) => !options || options.includes(v))
                                .map((data, index) => {
                                  const itemFullPath = `${category}.${subcategory}[${index}]`
                                  return (
                                    <Styled.DropDownItem
                                      style={{ margin: '0 0 0 28px' }}
                                      key={index}
                                      borderRadius={borderRadius}
                                      onClick={() => {
                                        setFieldValue && setFieldValue(stateName, data)
                                        onChange && onChange(data)
                                        setShowDropdown(false)
                                        handleSelectOption(data)
                                      }}
                                      active={value === data}
                                    >
                                      {data}
                                    </Styled.DropDownItem>
                                  )
                                })}
                            </>
                          )}
                        </div>
                      )
                    })}
              </div>
            ))
          ) : // : filterOptions.length > 0 ? (
          //   filterOptions.map((data, index) => {
          //     if (isCrewProjectReport) {
          //       const [titleClient, start, done] = data?.split('|')?.map((part) => part?.trim())
          //       const [title, clientName] = titleClient?.split(':')?.map((part) => part?.trim())
          //       const startDate = start?.replace('start: ', '')
          //       const doneDate = done?.replace('done: ', '')

          //       return (
          //         <Styled.DropDownItem
          //           borderRadius={borderRadius}
          //           key={index}
          //           onClick={() => {
          //             setFieldValue && setFieldValue(stateName, data)
          //             onChange && onChange(data)
          //             setShowDropdown(false)
          //             handleSelectOption(data)
          //           }}
          //           active={value === data}
          //         >
          //           <div className="project-info">
          //             <span>{title}: </span>
          //             <span>{clientName}</span>
          //             {startDate && (
          //               <>
          //                 <span> | </span>
          //                 <span className="label">start: </span> <span className="date">{startDate}</span>{' '}
          //               </>
          //             )}
          //             {doneDate && (
          //               <>
          //                 <span> | </span>
          //                 <span className="label">done: </span> <span className="date">{doneDate}</span>{' '}
          //               </>
          //             )}
          //           </div>
          //         </Styled.DropDownItem>
          //       )
          //     }

          //     return (
          //       <>
          //         <Styled.DropDownItem
          //           borderRadius={borderRadius}
          //           key={index}
          //           onClick={() => {
          //             setFieldValue && setFieldValue(stateName, data)
          //             onChange && onChange(data)
          //             setShowDropdown(false)
          //             handleSelectOption(data)
          //           }}
          //           active={value === data}
          //         >
          //           {data}
          //         </Styled.DropDownItem>
          //       </>
          //     )
          //   })
          // )
          !showAddOption ? (
            <Styled.DropDownItem noHover={true}>Nothing found</Styled.DropDownItem>
          ) : null}
          {showAddOption ? (
            <Styled.DropDownItem
              borderRadius={borderRadius}
              fontWeight={700}
              onClick={() => {
                onAddClick && onAddClick(value)
                setShowDropdown(false)
              }}
            >
              {addNewText ?? '+ Add New'}
            </Styled.DropDownItem>
          ) : null}
        </Styled.DropDownContentContainer>
      </Styled.DropDownContainer>
      {error && (
        <Styled.ErrorMsg width="100%">
          {/* @ts-ignore */}
          <ErrorMessage component="div" name={stateName} />
        </Styled.ErrorMsg>
      )}
    </Styled.DropDownOuterContainer>
  )
}

export default AutoCompleteIndentation
