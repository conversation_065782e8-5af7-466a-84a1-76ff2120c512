import React, { useEffect, useRef, useState } from 'react'
import { LoadScript, Autocomplete } from '@react-google-maps/api'
import * as SharedStyled from '../../styles/styled'
import { getConfig } from '../../config'
import { SLoader } from '../components/loader/Loader'
import { getDataFromLocalStorage } from '../helpers/util'
import { StorageKey } from '../helpers/constants'

const lib = ['places']

const AutoCompleteAddress = ({
  complemteAddress,
  street,
  city,
  state,
  zip,
  duration,
  setFieldValue,
  initialText,
  sourceAddress,
  companyLatLong,
  setLat,
  setLong,
  setDistance,
  setDuration,
  height,
  fontSize,
  distance,
  noLoadScript,
}: {
  complemteAddress?: string
  street?: string
  city?: string
  state?: string
  zip?: string
  duration?: string
  distance?: string
  initialText?: string
  setFieldValue: any
  sourceAddress?: string
  companyLatLong?: any
  setLat?: React.Dispatch<React.SetStateAction<string>>
  setLong?: React.Dispatch<React.SetStateAction<string>>
  setDistance?: React.Dispatch<React.SetStateAction<number>>
  setDuration?: React.Dispatch<React.SetStateAction<number>>
  setAddress?: React.Dispatch<React.SetStateAction<string>>
  height?: string
  fontSize?: string
  noLoadScript?: boolean
}) => {
  const inputRef: any = useRef()
  const [drivingInfo, setDrivingInfo] = useState(0)
  const [latlangObj, setLatlangObj] = useState({ east: 0, north: 0, south: 0, west: 0 })

  const handlePlaceChanged = (place: any) => {
    // const [place] = inputRef.current.getPlaces()
    if (place) {
      const addressComponents = place.address_components
      let address = ''
      let gCity = ''
      let gState1 = ''
      let country = ''
      let gZip = ''

      // Loop through the address components to extract the required information
      // addressComponents.forEach((component: any) => {
      //   const componentType = component.types[0]
      //   if (componentType === 'street_number' || componentType === 'route') {
      //     address += component.long_name + ' '
      //   } else if (componentType === 'locality' || componentType === 'neighborhood') {
      //     gCity = component.long_name
      //   } else if (componentType === 'administrative_area_level_1') {
      //     gState1 = component.short_name
      //   } else if (componentType === 'country') {
      //     country = component.long_name
      //   } else if (componentType === 'postal_code') {
      //     gZip = component.long_name
      //   }
      // })

      addressComponents.forEach((component: any) => {
        const types = component.types

        if (types.includes('street_number') || types.includes('route')) {
          address += component.long_name + ' '
        } else if (types.includes('locality') || types.includes('neighborhood')) {
          gCity = component.long_name
        } else if (types.includes('administrative_area_level_1')) {
          gState1 = component.short_name
        } else if (types.includes('country')) {
          country = component.long_name
        } else if (types.includes('postal_code')) {
          gZip = component.long_name
        }
      })
      if (setLat) {
        setLat(place.geometry.location.lat().toString())
      }
      if (setLong) {
        setLong(place.geometry.location.lng().toString())
      }
      setFieldValue(street, address.trim())
      setFieldValue(city, gCity)
      setFieldValue(state, gState1)
      setFieldValue(zip, gZip)
      complemteAddress && setFieldValue(complemteAddress, place?.formatted_address)

      // Calculate driving distance and duration
      const directionsService = new google.maps.DirectionsService()
      const companyLocation = new google.maps.LatLng(
        Number(companyLatLong?.latitude),
        Number(companyLatLong?.longitude)
      ) // Replace with your company's coordinates

      if (sourceAddress) {
        const route: any = {
          origin: place.formatted_address,
          destination: sourceAddress,
          travelMode: 'DRIVING',
        }

        directionsService.route(route, (response: any, status) => {
          if (status === 'OK') {
            const directionsData = response.routes[0].legs[0]
            const distanceInfo = Math.ceil(Number(directionsData.distance.value / 1609.34))

            const durationInfo = Math.round(directionsData?.duration?.value / 60) ?? 0
            if (setDistance) {
              setDistance(distanceInfo || 1)
              setFieldValue(distance, distanceInfo || 1)
            }
            if (setDuration) {
              setDuration(durationInfo || 1)
              if (duration) {
                setFieldValue(duration, durationInfo || 1)
              }
            }
            // const distanceInfo = `Driving distance is ${directionsData.distance.text} (${directionsData.duration.text}).`
            setDrivingInfo(distanceInfo)
          } else {
            setDrivingInfo(0)
          }
        })
      }
    }
  }

  useEffect(() => {
    if (companyLatLong) {
      setLatlangObj({
        // east: Number(-116.9853388) + 0.1,
        // north: Number(47.*********) + 0.1,
        // south: Number(47.*********) - 0.1,
        // west: Number(-116.9853388) - 0.1,

        east: Number(companyLatLong?.longitude) + 0.1,
        north: Number(companyLatLong?.latitude) + 0.1,
        south: Number(companyLatLong?.latitude) - 0.1,
        west: Number(companyLatLong?.longitude) - 0.1,
      })
    }
  }, [companyLatLong])

  const isLastPassInstalled = getDataFromLocalStorage(StorageKey.lastPassInstalled)

  return (
    <>
      {noLoadScript ? (
        <Autocomplete
          onLoad={(autocomplete: any) => (inputRef.current = autocomplete)}
          onPlaceChanged={() => handlePlaceChanged(inputRef.current.getPlace())}
          bounds={latlangObj}
        >
          <SharedStyled.InputFive fontSize={fontSize} className={isLastPassInstalled ? 'lastpass' : ''}>
            <div className="label-float">
              <SharedStyled.SimpleInput type="text" height={height} defaultValue={initialText} />
              <label>{'Google Search Street, City, State'}</label>
            </div>
          </SharedStyled.InputFive>
        </Autocomplete>
      ) : (
        <LoadScript
          googleMapsApiKey={getConfig().googleAddressApiKey}
          //  @ts-ignore
          libraries={lib}
          loadingElement={<SLoader height={35} width={100} isPercent />}
        >
          <Autocomplete
            onLoad={(autocomplete: any) => (inputRef.current = autocomplete)}
            onPlaceChanged={() => handlePlaceChanged(inputRef.current.getPlace())}
            bounds={latlangObj}
          >
            <SharedStyled.InputFive fontSize={fontSize} className={isLastPassInstalled ? 'lastpass' : ''}>
              <div className="label-float">
                <SharedStyled.SimpleInput type="text" height={height} defaultValue={initialText} />
                <label>{'Google Search Street, City, State'}</label>
              </div>
            </SharedStyled.InputFive>
          </Autocomplete>
        </LoadScript>
      )}
    </>
  )
}

export default AutoCompleteAddress
