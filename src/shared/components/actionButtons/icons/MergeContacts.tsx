import React from 'react'

interface MergeContactsIconProps {
  color?: string
  width?: string
  height?: string
}

const MergeContacts: React.FC<MergeContactsIconProps> = ({ color = '#000000', width = '20', height = '20' }) => {
  return (
    <svg width={width} height={height} viewBox="0 0 64.54 64.54" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M42.19 48.66H6.47C2.9 48.66 0 45.76 0 42.19V6.47C0 2.9 2.9 0 6.47 0h35.73c3.57 0 6.47 2.9 6.47 6.47v35.72c0 3.57-2.9 6.47-6.47 6.47ZM6.47 5c-.81 0-1.47.66-1.47 1.47v35.72c0 .81.66 1.47 1.47 1.47h35.72c.81 0 1.47-.66 1.47-1.47V6.47c0-.81-.66-1.47-1.47-1.47H6.47Z"
        fill={color}
      />
      <path
        d="M58.07 64.54H22.35c-3.57 0-6.47-2.9-6.47-6.47V22.35c0-3.57 2.9-6.47 6.47-6.47h35.73c3.57 0 6.47 2.9 6.47 6.47v35.72c0 3.57-2.9 6.47-6.47 6.47ZM22.35 20.88c-.81 0-1.47.66-1.47 1.47v35.72c0 .81.66 1.47 1.47 1.47h35.72c.81 0 1.47-.66 1.47-1.47V22.35c0-.81-.66-1.47-1.47-1.47H22.35Z"
        fill={color}
      />
      <path
        d="M18.38 48.66c-.64 0-1.28-.24-1.77-.73-.98-.98-.98-2.56 0-3.54L44.4 16.6c.98-.98 2.56-.98 3.54 0 .98.98.98 2.56 0 3.54L20.15 47.93c-.49.49-1.13.73-1.77.73Z"
        fill={color}
      />
      <path
        d="M18.38 34.77c-.64 0-1.28-.24-1.77-.73-.98-.98-.98-2.56 0-3.54l13.89-13.89c.98-.98 2.56-.98 3.54 0 .98.98.98 2.56 0 3.54l-13.89 13.89c-.49.49-1.13.73-1.77.73Z"
        fill={color}
      />
      <path
        d="M32.27 48.66c-.64 0-1.28-.24-1.77-.73-.98-.98-.98-2.56 0-3.54l13.89-13.89c.98-.98 2.56-.98 3.54 0 .98.98.98 2.56 0 3.54l-13.89 13.89c-.49.49-1.13.73-1.77.73Z"
        fill={color}
      />
    </svg>
  )
}

export default MergeContacts
