import React from 'react'

interface AddIconProps {
  color?: string
  width?: string
  height?: string
}

const AddIcon: React.FC<AddIconProps> = ({ 
  color = "#000000", 
  width = "20", 
  height = "20" 
}) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <path 
        d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" 
        fill={color}
      />
    </svg>
  )
}

export default AddIcon
