import styled from 'styled-components'
import { FlexRow } from '../../../styles/styled'
import { colors } from '../../../styles/theme'
import { Nue } from '../../helpers/constants'

export const ProfileCont = styled(FlexRow)`
  p {
    color: ${colors.gray};
    font-size: 13px;
    font-family: ${Nue.regular};
  }

  h1 {
    font-family: ${Nue.medium};
    font-size: 13px;
    text-transform: capitalize;
  }

  img {
    width: 40px;
    border-radius: 50%;
    aspect-ratio: 1/1;
    border: 1px solid ${colors.gray1};
  }
`

export const NameCont = styled.div<{ bg?: string }>`
  display: flex;
  justify-content: center;
  align-items: center;
  background: ${(props: any) => props.bg};
  width: 40px;
  border-radius: 50%;
  aspect-ratio: 1/1;

  &.drop {
    height: 40px;
  }
  &.profile-name {
    width: 72px;
    height: 72px;
  }
  &.profile-edit {
    width: 172px;
    height: 172px;
    span {
      font-size: 26px;
    }
  }

  &.media {
    width: 50px;
    height: 50px;
  }
`

export const Info = styled.span`
  color: white;
  font-weight: 700;
`

export const NestedRowCont = styled.section`
  display: flex;
  gap: 32px;
`

export const NestedCardCont = styled.div`
  padding: 16px 24px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 2px 20px 0px rgba(156, 145, 145, 0.25);
  max-width: 280px;
  max-height: 250px;

  p {
    color: ${colors.black};
    font-family: ${Nue.medium};
    font-size: 12px;
  }

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 1px solid ${colors.gray1};
  }

  span {
    color: ${colors.gray};
    font-family: ${Nue.medium};
    font-size: 11px;
    text-transform: uppercase;
  }

  .delete {
    margin-top: auto;
  }

  .height {
    height: 100%;
  }
`

export const RightSection = styled.section`
  width: 100%;

  table {
    flex: 1 0 100%;

    td {
      padding: 8px 24px;
      text-align: left;
    }
  }
`

export const TableHead = styled.p`
  color: ${colors.black};
  font-family: ${Nue.medium};
  font-size: 14px;
`
