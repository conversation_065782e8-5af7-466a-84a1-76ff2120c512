import { FlexCol } from '../../../styles/styled'
import { Info, NameCont, ProfileCont } from './style'
import { AvatarSvg } from '../../helpers/images'
import { getInitials, uuidToColor } from '../../helpers/util'

interface IProfile {
  name?: string
  email?: string
  image?: string
  id?: string
  users?: {
    firstName?: string
    lastName?: string
    preferredName?: string
    isBusiness?: boolean
    imageUrl?: string
  }
  imageUrl?: string
  isBusiness?: boolean
  businessName?: string
}

interface IProfileProps {
  data?: IProfile
  isTeam?: boolean
  isMedia?: boolean
  showImagePlaceholder?: boolean
}

const ProfileInfo = (props: IProfileProps) => {
  const { data, isTeam, isMedia, showImagePlaceholder = false } = props

  return (
    <ProfileCont>
      {(isTeam ? !data?.imageUrl : data?.users?.firstName) &&
      (isTeam ? !data?.imageUrl : !data?.users?.imageUrl) &&
      !showImagePlaceholder ? (
        <NameCont bg={uuidToColor(data?.id!)} className={isMedia ? 'media' : ''}>
          <Info className={!data?.users?.lastName ? 'last' : ''}>
            {getInitials(
              `${isTeam ? data?.name?.split(' ')?.[0] : data?.users?.firstName} ${
                isTeam ? data?.name?.split(' ')?.[1] : data?.users?.lastName
              }`
            )}
          </Info>
        </NameCont>
      ) : (
        <img
          src={isTeam ? data?.imageUrl : data?.users?.imageUrl || (showImagePlaceholder ? AvatarSvg : '')}
          alt="hello"
        />
      )}

      {isMedia ? (
        <FlexCol gap="2px">
          <h1>{data?.users?.firstName}</h1>

          <p>{data?.email}</p>
        </FlexCol>
      ) : (
        <FlexCol gap="2px">
          <h1>{isTeam ? data?.name : data?.isBusiness ? data?.name : data?.name}</h1>
          <p>{data?.email}</p>
        </FlexCol>
      )}
    </ProfileCont>
  )
}

export default ProfileInfo
