import React, { useState, useRef, useEffect, ReactElement, CSSProperties } from 'react'
import { createPortal } from 'react-dom'
import { TooltipContent } from './style'

interface TooltipProps {
  children: ReactElement
  content: React.ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right'
  customStyle?: CSSProperties
  isTooltipVisible?: boolean
  isTableTooltip?: boolean
}

export const TooltipPortal = (props: TooltipProps) => {
  const { children, content, position = 'top', isTooltipVisible = true, customStyle, isTableTooltip } = props
  const [isVisible, setIsVisible] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 })

  const triggerRef = useRef<HTMLElement>(null)

  const showTooltip = () => setIsVisible(true)
  const hideTooltip = () => setIsVisible(false)

  useEffect(() => {
    if (isVisible && triggerRef.current && isTooltipVisible) {
      const rect = triggerRef.current.getBoundingClientRect()
      let newPosition = { top: 0, left: 0 }

      switch (position) {
        case 'top':
          newPosition = {
            top: rect.top + window.scrollY - rect.height,
            left: rect.left + window.scrollX,
          }
          break
        case 'bottom':
          newPosition = {
            top: rect.bottom + window.scrollY,
            left: rect.left + window.scrollX,
          }
          break
        case 'left':
          newPosition = {
            top: rect.top + window.scrollY,
            left: rect.left + window.scrollX - rect.width,
          }
          break
        case 'right':
          newPosition = {
            top: rect.top + window.scrollY + 5,
            left: rect.right + window.scrollX,
          }
          break
        default:
          break
      }

      setTooltipPosition(newPosition)
    }
  }, [isVisible])

  return (
    <>
      {React.cloneElement(children, {
        ref: triggerRef,
        onMouseEnter: showTooltip,
        onMouseLeave: hideTooltip,
      })}
      {(isVisible && tooltipPosition?.top && isTooltipVisible) || isTableTooltip
        ? createPortal(
            <TooltipContent
              style={{ top: `${tooltipPosition?.top}px`, left: `${tooltipPosition.left}px`, ...customStyle }}
              className={`${position}`}
            >
              {content}
            </TooltipContent>,
            document.body
          )
        : null}
    </>
  )
}
