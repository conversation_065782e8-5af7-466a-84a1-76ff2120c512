import styled, { css, ThemeProps } from 'styled-components'
import { colors } from '../../../styles/theme'
import { Nue } from '../../helpers/constants'

const getYstyles = (gap: number) => css`
  transform: translate(-50%, ${gap}px);
`
const getLeftstyles = (gapX: number, gapY: number) => css`
  top: 0;
  left: 0%;
  transform: translate(${gapX}px, ${gapY}px);
`

const getRightstyles = (gapX: number, gapY: number) => css`
  color: var(--white);
  background: var(--blue);
  top: 0;
  left: 108%;
  transform: translate(${gapX}px, ${gapY}px);
`

export const TooltipCont = styled.span<{
  gapX: number
  gapY: number
  isVisible: boolean
  position: string
  width?: number
}>`
  position: relative;
  cursor: pointer;
  --blue: ${colors.blueDark};
  --white: ${colors.white};

  &::before {
    content: attr(data-tooltip);
    display: ${({ isVisible }) => (isVisible ? 'block' : 'none')};
    position: absolute;
    left: 50%;
    width: ${({ width }) => `${width}px` ?? 'max-content'};

    ${({ gapY }) => getYstyles(gapY)};

    background: rgba(51, 51, 51, 0.8);
    color: ${colors.white};
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 12px;

    ${({ position, gapX, gapY }) => position === 'left' && getLeftstyles(gapX, gapY)};

    ${({ position, gapX, gapY }) => position === 'right' && getRightstyles(gapX, gapY)};
  }
`

export const TooltipContent = styled.div`
  position: absolute;
  background-color: rgba(51, 51, 51, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  font-family: ${Nue.medium};
  pointer-events: none;

  .right {
    transform: translateY(100%);
  }
`
