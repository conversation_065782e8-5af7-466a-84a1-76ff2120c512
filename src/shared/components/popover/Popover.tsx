import { useRef, useState } from 'react'

import { <PERSON>over<PERSON>ontainer, PopoverContent, TriggerCont } from './style'
import { useClickOutside } from '../../hooks/useClickOutside'

interface IPopoverProps {
  triggerComponent?: React.ReactNode
  show?: boolean
  className?: string
  children?: React.ReactNode
}
const Popover = (props: IPopoverProps) => {
  const { triggerComponent, show = false, className, children } = props
  const [isActive, setIsActive] = useState(show)
  const dropDownRef = useRef(null)
  useClickOutside(dropDownRef, () => setIsActive(false))

  return (
    <PopoverContainer className={className}>
      <TriggerCont
        onClick={() => {
          setIsActive(true)
        }}
      >
        <>{triggerComponent}</>
      </TriggerCont>
      <PopoverContent
        isActive={isActive}
        ref={dropDownRef}
        onClick={(e) => {
          e.stopPropagation()
          setIsActive(false)
        }}
      >
        <>{children}</>
      </PopoverContent>
    </PopoverContainer>
  )
}

export default Popover
