import { useState } from 'react'
import { Indicator, TabButton, Tab<PERSON>ontainer, TabWrap, Title } from './style'

type TitleType = string | React.ReactNode

interface ITabData {
  title: TitleType
  render: () => JSX.Element
}

interface ITabBarProps {
  tabs: ITabData[]
  filterComponent?: React.ReactNode
  className?: string
  setDetailsUpdate?: React.Dispatch<React.SetStateAction<boolean>>
  onTabChange?: (idx: number) => void
  loading?: boolean
}

const TabBar = ({ tabs, filterComponent, setDetailsUpdate, className, onTabChange, loading }: ITabBarProps) => {
  const [activeTab, setActiveTab] = useState(0)
  return (
    <>
      <TabWrap>
        <TabContainer>
          {tabs.map((tab: { title: TitleType }, index: number) => (
            <TabButton
              key={index}
              onClick={() => {
                if (!loading) {
                  setActiveTab(index)
                  setDetailsUpdate?.((prev) => !prev)
                  onTabChange?.(index)
                }
              }}
              className={className}
            >
              <Title active={activeTab === index}>{tab.title}</Title>
              <Indicator active={activeTab === index} />
            </TabButton>
          ))}
        </TabContainer>
        {filterComponent}
      </TabWrap>
      {tabs[activeTab].render()}
    </>
  )
}

export default TabBar
