import { ButtonHTMLAttributes } from 'react'

import { SmallLoaderCont } from '../loader/style'
import { StyledButton } from './style'
import * as SharedStyled from '../../../styles/styled'

interface IButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode
  isLoading?: boolean
  bgColor?: string
  width?: string
  height?: string
  padding?: string
  maxWidth?: string
  tooltip?: string
  tooltipWidth?: string
  tooltipDivStyle?: React.CSSProperties
  positionLeftDecs?: string
}

const Button = (props: IButtonProps) => {
  const {
    children,
    padding,
    isLoading,
    bgColor,
    maxWidth,
    width,
    height,
    tooltip,
    tooltipWidth,
    positionLeftDecs,
    tooltipDivStyle,
    ...otherProps
  } = props
  return tooltip ? (
    <SharedStyled.TooltipContainer
      width={tooltipWidth}
      // positionLeft="0px"
      // positionBottom="0px"
      positionLeftDecs={positionLeftDecs}
      positionBottomDecs="100%"
      style={tooltipDivStyle}
    >
      <span className="tooltip-content">{tooltip}</span>
      <StyledButton
        {...otherProps}
        disabled={isLoading || otherProps?.disabled}
        className={
          isLoading ? `${otherProps.className} loading` : otherProps?.disabled ? 'disabled' : `${otherProps.className}`
        }
        bgColor={bgColor}
        width={width}
        height={height}
        padding={padding}
        maxWidth={maxWidth}
      >
        {children}
        {isLoading ? <SmallLoaderCont /> : null}
      </StyledButton>
    </SharedStyled.TooltipContainer>
  ) : (
    <StyledButton
      {...otherProps}
      disabled={isLoading || otherProps?.disabled}
      className={isLoading ? `${otherProps.className} loading` : `${otherProps.className}`}
      bgColor={bgColor}
      width={width}
      height={height}
      padding={padding}
      maxWidth={maxWidth}
    >
      {children}
      {isLoading ? <SmallLoaderCont /> : null}
    </StyledButton>
  )
}

export default Button
