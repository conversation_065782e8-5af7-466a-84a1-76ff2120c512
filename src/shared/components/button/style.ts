import { rgba } from 'polished'
import styled, { ThemeProps } from 'styled-components'
import { colors, screenSizes, Theme } from '../../../styles/theme'
import { Nue } from '../../helpers/constants'

export interface IStyledButtonProps {
  padding?: string
  width?: string
  bgColor?: string
  height?: string
  maxWidth?: string
}

export const StyledButton = styled.button<IStyledButtonProps>`
  padding: ${(props) => (props.padding ? props.padding : '14px 24px')};
  outline: none;
  white-space: nowrap;
  position: relative;
  background: ${(props) => (props.bgColor ? props.bgColor : colors.lightBlue)};
  max-width: ${(props) => props.maxWidth ?? props.maxWidth};
  font-family: ${Nue.medium};
  border-radius: 8px;
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : 'auto')};
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  border: none;
  color: ${colors.white};
  outline: none;
  user-select: none;
  white-space: nowrap;
  text-transform: capitalize;
  &:hover {
    filter: brightness(75%);
  }
  .disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;

    &:hover {
      filter: brightness(100%);
    }
  }

  &.fit {
    width: max-content;
    span {
      right: 43%;
    }
  }

  &.loading {
    color: transparent;
  }

  &.delete {
    background-color: ${colors.lightRed2};
  }

  &.outline {
    background: ${colors.white};
    color: ${colors.lightBlue};
    border: 1px solid ${colors.lightBlue};
  }

  &.yellow {
    background-color: #ffc107;
    color: ${colors.text};
  }
  &.yellow-white-text {
    background-color: #ffc107;
  }
  &.gray {
    background-color: #808080;
  }
  &.success {
    background-color: ${colors.green};
  }
  //changesMobile
  @media (max-width: ${screenSizes.M}px) {
    padding: 12px 18px;
    font-size: 10px;
  }
`

export const RoundButton = styled.div`
  display: flex;
  width: 32px;
  height: 32px;
  padding: 8px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  background: ${rgba(colors.gray, 0.1)};
  cursor: pointer;

  &:hover {
    filter: brightness(75%);
  }

  img {
    width: 15px;
  }

  @media (min-width: ${screenSizes.XS}px) {
    padding: 12px;
    width: 40px;
    height: 40px;
  }
`
