import { SkeletonLoader } from '../../../styles/styled'
import { LoaderCont } from './style'

export const SLoader = ({
  skeletonStyle,
  ...props
}: {
  width?: number
  height?: number
  margin?: string
  isPercent?: boolean
  style?: React.CSSProperties
  skeletonStyle?: React.CSSProperties
  justifyItem?: string
}) => {
  return (
    <SkeletonLoader {...props}>
      <div className="skeleton" style={{ ...skeletonStyle }}></div>
    </SkeletonLoader>
  )
}

export const FullpageLoader = () => {
  return (
    <LoaderCont className="loader-cont">
      <span className="loader"></span>
    </LoaderCont>
  )
}

export const RenderData = ({
  loading,
  loader,
  children,
}: {
  loading: boolean
  loader: React.ReactNode
  children: React.ReactNode
}) => {
  return loading ? loader : children
}
