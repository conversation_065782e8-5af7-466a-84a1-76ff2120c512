import { useState, useRef } from 'react'

import { ContentContainer, DropDownContainer, OptionItem, SelectedCont, SelectedDropdownCont } from './style'
import { CaretSvg } from '../../helpers/images'
import { useClickOutside } from '../../hooks/useClickOutside'
import { FlexRow } from '../../../styles/styled'

interface IDropdownProps {
  options: any
  children: React.ReactNode
  onSelectValue: (val: any) => void
  selectedValue?: any
  className?: string
  isOrganization?: boolean
  isChevron?: boolean
}

const Dropdown = (props: IDropdownProps) => {
  const { children, options, onSelectValue, selectedValue, isChevron, className, isOrganization } = props
  const [isActive, setIsActive] = useState(false)
  const dropDownRef = useRef(null)
  useClickOutside(dropDownRef, () => setIsActive(false))

  return (
    <DropDownContainer className={className} isOrganization={isOrganization} show={isActive}>
      {isOrganization ? (
        <SelectedCont
          onClick={() => {
            setIsActive(!isActive)
          }}
        >
          {children}
        </SelectedCont>
      ) : (
        <SelectedDropdownCont
          onClick={() => {
            setIsActive(!isActive)
          }}
          tabIndex={0}
        >
          {children}

          {isChevron ? '▼' : <img src={CaretSvg} className={isActive ? 'rotate' : ''} alt="caret" />}
        </SelectedDropdownCont>
      )}

      <ContentContainer style={{ display: isActive ? 'block' : 'none' }} ref={dropDownRef} className="dropContent">
        {options?.map((item: any) => (
          <OptionItem
            key={item?.label}
            onClick={() => {
              setIsActive(!isActive)
              onSelectValue(item)
            }}
            isActive={selectedValue?.label === item?.label}
          >
            <FlexRow gap="8px">
              {item?.image && <img src={item?.image} alt="organization image" />}
              <p>{item?.label}</p>
            </FlexRow>
          </OptionItem>
        ))}
      </ContentContainer>
    </DropDownContainer>
  )
}

export default Dropdown
