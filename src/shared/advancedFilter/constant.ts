interface FilterValue {
  type: string
  value?: any
  to?: number
  unit?: 'days' | 'hours'
  startDate?: string
  endDate?: string
}

interface FilterObject {
  [key: string]: FilterValue
}

interface ArrayFilterObject {
  [key: string]: FilterValue[]
}

export const convertFiltersToMongoQuery = (filters: ArrayFilterObject): Record<string, any> => {
  const query: Record<string, any> = {}

  for (const key in filters) {
    const filterArray = filters[key]

    if (!filterArray || filterArray.length === 0) continue

    // If there's only one filter for this field, handle it normally
    if (filterArray.length === 1) {
      const filter = filterArray[0]
      const { type, value, unit, to } = filter

      if (key === 'createdAt') {
        if (type === 'range' && filter.startDate && filter.endDate) {
          // Handle date range with startDate and endDate
          query[key] = {
            $gte: new Date(filter.startDate),
            $lte: new Date(filter.endDate),
          }
        } else {
          // Handle traditional date filters
          const now = Date.now()
          const multiplier = unit === 'hours' ? 3600 * 1000 : 24 * 3600 * 1000

          if (type === 'more') {
            query[key] = { $lt: new Date(now - value * multiplier) }
          } else if (type === 'less') {
            query[key] = { $gt: new Date(now - value * multiplier) }
          } else if (type === 'between') {
            query[key] = {
              $lt: new Date(now - value * multiplier),
              $gt: new Date(now - (to ?? value) * multiplier),
            }
          }
        }
      } else {
        if (type === 'is') {
          query[key] = value
        } else if (type === 'contains') {
          query[key] = { $regex: value, $options: 'i' }
        } else if (type === 'is_empty') {
          query[key] = { $exists: false }
        } else if (type === 'is_not_empty') {
          query[key] = { $exists: true }
        } else if (type === 'is_not') {
          query[key] = { $ne: value }
        }
      }
    }
    // If there are multiple filters for this field, use $or operator
    else {
      const conditions = filterArray.map((filter) => {
        const { type, value, unit, to } = filter
        let condition: Record<string, any> = {}

        if (key === 'createdAt') {
          if (type === 'range' && filter.startDate && filter.endDate) {
            // Handle date range with startDate and endDate
            condition[key] = {
              $gte: new Date(filter.startDate),
              $lte: new Date(filter.endDate),
            }
          } else {
            // Handle traditional date filters
            const now = Date.now()
            const multiplier = unit === 'hours' ? 3600 * 1000 : 24 * 3600 * 1000

            if (type === 'more') {
              condition[key] = { $lt: new Date(now - value * multiplier) }
            } else if (type === 'less') {
              condition[key] = { $gt: new Date(now - value * multiplier) }
            } else if (type === 'between') {
              condition[key] = {
                $lt: new Date(now - value * multiplier),
                $gt: new Date(now - (to ?? value) * multiplier),
              }
            }
          }
        } else {
          if (type === 'is') {
            condition[key] = value
          } else if (type === 'contains') {
            condition[key] = { $regex: value, $options: 'i' }
          } else if (type === 'is_empty') {
            condition[key] = { $exists: false }
          } else if (type === 'is_not_empty') {
            condition[key] = { $exists: true }
          } else if (type === 'is_not') {
            condition[key] = { $ne: value }
          }
        }

        return condition
      })

      // Add the $or condition to the query
      if (!query.$or) {
        query.$or = conditions
      } else {
        // If $or already exists, merge with existing conditions
        query.$or = [...query.$or, ...conditions]
      }
    }
  }

  return query
}
