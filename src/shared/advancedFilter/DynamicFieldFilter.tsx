import React, { useState } from 'react'
import StringFilter from './filters/StringFilter'
import DropdownFilter from './filters/DropdownFilter'
import DateFilter from './filters/DateFilter'
import * as SharedStyled from '../../styles/styled'
import { CaretSvg } from '../helpers/images'
import NumberFilter from './filters/NumberFilter'

interface FieldDefinition {
  label: string
  key: string
  type: 'string' | 'dropdown' | 'date' | 'number'
  values?: any // for dropdown
  operators?: string[]
}

interface DynamicFieldFilterProps {
  field: FieldDefinition
  onApply: (query: Record<string, any>) => void
  onReset: (key: string) => void
  onTypeChange: (key: string) => void
  onRemoveFilter: (key: string, operator: string, value?: string) => void
  appliedFilters?: Record<string, any[]>
}

const DynamicFieldFilter: React.FC<DynamicFieldFilterProps> = ({
  field,
  onApply,
  onReset,
  onTypeChange,
  onRemoveFilter,
  appliedFilters = {},
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // Get the applied filters for this field
  const fieldAppliedFilters = appliedFilters[field.key] || []

  const renderFilter = () => {
    switch (field.type) {
      case 'string':
        return (
          <StringFilter
            label={field.label}
            fieldKey={field.key}
            onApply={onApply}
            onReset={() => onReset(field.key)}
            onTypeChange={() => onTypeChange(field.key)}
            onRemoveFilter={onRemoveFilter}
            appliedFilters={fieldAppliedFilters}
          />
        )

      case 'number':
        return (
          <NumberFilter
            label={field.label}
            fieldKey={field.key}
            onApply={onApply}
            onReset={() => onReset(field.key)}
            onTypeChange={() => onTypeChange(field.key)}
            onRemoveFilter={onRemoveFilter}
            appliedFilters={fieldAppliedFilters}
          />
        )

      case 'dropdown':
        return (
          <DropdownFilter
            label={field.label}
            fieldKey={field.key}
            options={field.values || []}
            onApply={onApply}
            onReset={() => onReset(field.key)}
            onTypeChange={() => onTypeChange(field.key)}
            onRemoveFilter={onRemoveFilter}
            appliedFilters={fieldAppliedFilters}
            operators={field.operators}
          />
        )

      case 'date':
        return (
          <DateFilter
            label={field.label}
            fieldKey={field.key}
            onApply={onApply}
            onReset={() => onReset(field.key)}
            onTypeChange={() => onTypeChange(field.key)}
            onRemoveFilter={onRemoveFilter}
            appliedFilters={fieldAppliedFilters}
          />
        )

      default:
        return null
    }
  }

  // Check if this field has active filters
  const hasActiveFilters = fieldAppliedFilters.length > 0

  return (
    <SharedStyled.FlexCol
      style={{
        border: hasActiveFilters ? '1px solid #3b82f6' : '1px solid #e5e7eb',
        borderRadius: '8px',
        backgroundColor: hasActiveFilters ? '#e1eafdf5' : '#f9fafb',
        marginBottom: '8px',
        transition: 'border-color 0.3s ease',
      }}
    >
      <button
        type="button"
        onClick={() => setIsOpen((prev) => !prev)}
        style={{
          width: '100%',
          background: 'transparent',
          border: 'none',
          padding: '16px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          cursor: 'pointer',
        }}
      >
        <SharedStyled.Text fontWeight="500" textAlign="left">
          {field.label}
        </SharedStyled.Text>
        <SharedStyled.Text>
          <img
            src={CaretSvg}
            alt="caret icon"
            style={{
              transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease',
            }}
          />
        </SharedStyled.Text>
      </button>

      {isOpen && (
        <SharedStyled.FlexCol
          padding="16px"
          style={{
            borderTop: '1px solid #e5e7eb',
            backgroundColor: 'white',
          }}
        >
          {renderFilter()}
        </SharedStyled.FlexCol>
      )}
    </SharedStyled.FlexCol>
  )
}

export default DynamicFieldFilter
