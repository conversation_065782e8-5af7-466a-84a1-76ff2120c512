import styled from 'styled-components'

export const Input = styled.input`
  border: 1px solid #d1d5db;
  padding: 6px 8px;
  border-radius: 6px;
  width: 100%;
`

export const Select = styled.select`
  border: 1px solid #d1d5db;
  padding: 6px 8px;
  border-radius: 6px;
  width: 100%;
`

export const RadioWrapper = styled.label`
  display: flex;
  align-items: center;
  gap: 8px;
`

export const ErrorText = styled.p`
  color: #ef4444;
  font-size: 0.875rem;
`

export const AppliedFilterTag = styled.div`
  background-color: #e5e7eb;
  color: #374151;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 4px;
`

export const AppliedFiltersContainer = styled.div`
  /* margin-bottom: 16px; */
  display: flex;
  flex-wrap: wrap;
`

export const RemoveFilterButton = styled.button`
  margin-left: 8px;
  border: none;
  background: none;
  color: #999;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    color: #ff4d4f;
  }
`
