import React, { useState } from 'react'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from '../style'
import Button from '../../components/button/Button'

interface StringFilterProps {
  label: string
  fieldKey: string
  onApply: (query: Record<string, any>) => void
  onReset: () => void
  onTypeChange: () => void
  onRemoveFilter: (fieldKey: string, operator: string, value?: string) => void
  appliedFilters?: any[]
}

const StringFilter: React.FC<StringFilterProps> = ({
  label,
  fieldKey,
  onApply,
  onReset,
  onTypeChange,
  onRemoveFilter,
  appliedFilters = [],
}) => {
  const [type, setType] = useState<'is' | 'is_empty' | 'is_not_empty'>('is')
  const [value, setValue] = useState('')
  const [error, setError] = useState('')
  const [isApplyDisabled, setIsApplyDisabled] = useState(false)
  // Clear values when type changes
  const handleTypeChange = (newType: 'is' | 'is_empty' | 'is_not_empty') => {
    if (type !== newType) {
      setType(newType)
      setValue('')
      setError('')

      // Only disable Apply button for is_empty and is_not_empty if they're already applied
      const isAlreadyApplied = appliedFilters?.some((filter) => filter.operator === newType)
      setIsApplyDisabled(newType !== 'is' && isAlreadyApplied)
    } else {
      setType(newType)
    }
  }

  // Update the handleApply function to handle different filter types
  const handleApply = () => {
    const query: Record<string, any> = {}
    const filterValue = {
      field: fieldKey,
      operator: type,
      value: type === 'is' ? value : undefined,
    }

    // Reset previous error
    setError('')

    if (type === 'is') {
      if (!value.trim()) {
        setError('This field cannot be empty.')
        return
      }
      query[fieldKey] = filterValue
    } else if (type === 'is_empty' || type === 'is_not_empty') {
      query[fieldKey] = filterValue

      // Disable Apply button after applying is_empty or is_not_empty
      setIsApplyDisabled(true)
    }

    console.log('data', type, filterValue)
    onApply(query)

    // Reset form after applying
    if (type === 'is') {
      setValue('')
    }
  }

  const handleReset = () => {
    setType('is')
    setValue('')
    setError('')
    setIsApplyDisabled(false)
    onReset()
  }
  console.log({ appliedFilters })
  return (
    <SharedStyled.FlexCol gap="8px">
      <SharedStyled.Text fontWeight="600">{label}</SharedStyled.Text>

      {/* Display applied filters */}
      {appliedFilters && appliedFilters.length > 0 && (
        <SharedStyled.FlexCol gap="8px" margin="0 0 16px 0">
          <SharedStyled.Text fontWeight="600">Applied Filters:</SharedStyled.Text>
          <Styled.AppliedFiltersContainer>
            {appliedFilters.map((filter, index) => (
              <Styled.AppliedFilterTag key={index}>
                {filter.operator === 'is' && `Is: ${filter.value}`}
                {filter.operator === 'is_empty' && 'Is Empty'}
                {filter.operator === 'is_not_empty' && 'Is Not Empty'}
                <Styled.RemoveFilterButton onClick={() => onRemoveFilter(fieldKey, filter.operator, filter.value)}>
                  ×
                </Styled.RemoveFilterButton>
              </Styled.AppliedFilterTag>
            ))}
          </Styled.AppliedFiltersContainer>
        </SharedStyled.FlexCol>
      )}

      <SharedStyled.FlexCol gap="4px">
        <Styled.RadioWrapper>
          <input
            type="radio"
            name={`${fieldKey}-type`}
            value="is"
            checked={type === 'is'}
            onChange={() => handleTypeChange('is')}
          />
          <SharedStyled.Text>Is</SharedStyled.Text>
        </Styled.RadioWrapper>
        {type === 'is' && (
          <>
            <Styled.Input
              type="text"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="Enter text"
            />
            {error && <Styled.ErrorText>{error}</Styled.ErrorText>}
          </>
        )}
        <Styled.RadioWrapper>
          <input
            type="radio"
            name={`${fieldKey}-type`}
            value="is_empty"
            checked={type === 'is_empty'}
            onChange={() => handleTypeChange('is_empty')}
          />
          <SharedStyled.Text>Is Empty</SharedStyled.Text>
        </Styled.RadioWrapper>

        <Styled.RadioWrapper>
          <input
            type="radio"
            name={`${fieldKey}-type`}
            value="is_not_empty"
            checked={type === 'is_not_empty'}
            onChange={() => handleTypeChange('is_not_empty')}
          />
          <SharedStyled.Text>Is Not Empty</SharedStyled.Text>
        </Styled.RadioWrapper>
      </SharedStyled.FlexCol>

      <SharedStyled.FlexRow gap="8px">
        <Button disabled={isApplyDisabled} onClick={handleApply} padding="6px 12px">
          Apply
        </Button>
        <Button onClick={handleReset} className="gray" padding="6px 12px">
          Reset
        </Button>
      </SharedStyled.FlexRow>
    </SharedStyled.FlexCol>
  )
}

export default StringFilter
