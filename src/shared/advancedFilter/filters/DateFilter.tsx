import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from '../style'
import Button from '../../components/button/Button'

interface DateFilterProps {
  label: string
  fieldKey: string
  onApply: (query: Record<string, any>) => void
  onReset: () => void
  onTypeChange: () => void
  onRemoveFilter: (fieldKey: string, operator: string, value?: string) => void
  appliedFilters?: any[]
}

const units = ['days', 'weeks', 'months']

const DateFilter: React.FC<DateFilterProps> = ({
  label,
  fieldKey,
  onApply,
  onReset,
  onTypeChange,
  onRemoveFilter,
  appliedFilters = [],
}) => {
  const [type, setType] = useState<'more' | 'less' | 'range'>('more')
  const [value, setValue] = useState('')
  const [unit, setUnit] = useState('days')
  const [isApplyDisabled, setIsApplyDisabled] = useState(false)
  // For date range inputs
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')

  // Check if a specific filter type is already applied
  const isFilterTypeApplied = (filterType: string) => {
    return appliedFilters.some((filter) => filter.operator === filterType)
  }

  // Update disabled state when appliedFilters change
  useEffect(() => {
    setIsApplyDisabled(isFilterTypeApplied(type))
  }, [appliedFilters, type])

  // Clear values when type changes
  const handleTypeChange = (newType: 'more' | 'less' | 'range') => {
    if (type !== newType) {
      setType(newType)
      setValue('')
      setStartDate('')
      setEndDate('')
      // Check if the new type is already applied
      setIsApplyDisabled(isFilterTypeApplied(newType))
    } else {
      setType(newType)
    }
  }

  const handleApply = () => {
    // Validate inputs
    if (type === 'range') {
      if (!startDate || !endDate) return
    } else if (!value) {
      return
    }

    const query: Record<string, any> = {}
    let filterValue

    if (type === 'range') {
      filterValue = {
        field: fieldKey,
        operator: type,
        startDate,
        endDate,
      }
    } else {
      filterValue = {
        field: fieldKey,
        operator: type,
        value: Number(value),
        unit,
      }
    }

    query[fieldKey] = filterValue
    onApply(query)

    // Reset form after applying
    setValue('')
    setStartDate('')
    setEndDate('')
  }

  const handleReset = () => {
    setValue('')
    setStartDate('')
    setEndDate('')
    setType('more')
    setUnit('days')
    setIsApplyDisabled(false)
    onReset()
  }

  return (
    <SharedStyled.FlexCol gap="8px">
      <SharedStyled.Text fontWeight="600">{label}</SharedStyled.Text>

      {/* Display applied filters */}
      {appliedFilters && appliedFilters.length > 0 && (
        <SharedStyled.FlexCol gap="8px" margin="0 0 16px 0">
          <SharedStyled.Text fontWeight="600">Applied Filters:</SharedStyled.Text>
          <Styled.AppliedFiltersContainer>
            {appliedFilters.map((filter, index) => (
              <Styled.AppliedFilterTag key={index}>
                {filter.operator === 'more' && `More than ${filter.value} ${filter.unit} ago`}
                {filter.operator === 'less' && `Less than ${filter.value} ${filter.unit} ago`}
                {filter.operator === 'range' && `Date Range: ${filter.startDate} to ${filter.endDate}`}
                <Styled.RemoveFilterButton
                  onClick={() => onRemoveFilter(fieldKey, filter.operator, filter.value?.toString())}
                >
                  ×
                </Styled.RemoveFilterButton>
              </Styled.AppliedFilterTag>
            ))}
          </Styled.AppliedFiltersContainer>
        </SharedStyled.FlexCol>
      )}

      <SharedStyled.FlexCol gap="12px">
        {['more', 'less', 'range'].map((opt) => (
          <SharedStyled.FlexCol gap="8px" key={opt}>
            <Styled.RadioWrapper>
              <input
                type="radio"
                name={`${fieldKey}-type`}
                value={opt}
                checked={type === opt}
                onChange={() => handleTypeChange(opt as any)}
              />
              <SharedStyled.Text>
                {opt === 'more' && 'More Than'}
                {opt === 'less' && 'Less Than'}
                {opt === 'range' && 'Range'}
              </SharedStyled.Text>
            </Styled.RadioWrapper>

            {type === opt && (
              <>
                {opt === 'range' ? (
                  <SharedStyled.FlexCol gap="8px">
                    <SharedStyled.FlexRow gap="8px" alignItems="center">
                      <SharedStyled.Text>From:</SharedStyled.Text>
                      <Styled.Input type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
                    </SharedStyled.FlexRow>

                    <SharedStyled.FlexRow gap="8px" alignItems="center">
                      <SharedStyled.Text>To:</SharedStyled.Text>
                      <Styled.Input type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
                    </SharedStyled.FlexRow>
                  </SharedStyled.FlexCol>
                ) : (
                  <SharedStyled.FlexRow gap="8px">
                    <Styled.Input
                      type="number"
                      value={value}
                      onChange={(e) => setValue(e.target.value)}
                      placeholder="Enter number"
                    />
                    <Styled.Select value={unit} onChange={(e) => setUnit(e.target.value)}>
                      {units.map((u) => (
                        <option key={u} value={u}>
                          {u} ago
                        </option>
                      ))}
                    </Styled.Select>
                  </SharedStyled.FlexRow>
                )}
              </>
            )}
          </SharedStyled.FlexCol>
        ))}
      </SharedStyled.FlexCol>

      <SharedStyled.FlexRow gap="8px">
        <Button
          disabled={
            // Disable if required fields are empty
            (type === 'range' && (!startDate || !endDate)) ||
            (type !== 'range' && !value) ||
            // Or if this filter type is already applied
            isApplyDisabled
          }
          onClick={handleApply}
          padding="6px 12px"
        >
          Apply
        </Button>
        <Button onClick={handleReset} className="gray" padding="6px 12px">
          Reset
        </Button>
      </SharedStyled.FlexRow>
    </SharedStyled.FlexCol>
  )
}

export default DateFilter
