import React, { useState, useEffect } from 'react'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from '../style'
import Button from '../../components/button/Button'

interface DropdownOption {
  _id: string
  name: string
}

interface DropdownFilterProps {
  label: string
  fieldKey: string
  options: DropdownOption[] | string[]
  onApply: (query: Record<string, any>) => void
  onReset: () => void
  onTypeChange: () => void
  onRemoveFilter: (fieldKey: string, operator: string, value?: string) => void
  appliedFilters?: any[]
  operators?: string[]
}

const DropdownFilter: React.FC<DropdownFilterProps> = ({
  label,
  fieldKey,
  options,
  onApply,
  onReset,
  onTypeChange,
  onRemoveFilter,
  appliedFilters = [],
  operators,
}) => {
  const [type, setType] = useState<string>(() => {
    const applied = appliedFilters.find((f) => f.field === fieldKey)
    return applied?.operator ?? operators?.[0] ?? 'is'
  })

  const [selectedOption, setSelectedOption] = useState(() => {
    const applied = appliedFilters.find((f) => f.field === fieldKey)
    return applied?.value ?? ''
  })

  const [isApplyDisabled, setIsApplyDisabled] = useState(false)

  // Check if options are objects or strings
  const isObjectOptions = options.length > 0 && typeof options[0] !== 'string'
  const supportedOperators = operators ?? ['is', 'is_not']

  // Get display name for a value
  const getDisplayName = (value: string) => {
    if (!isObjectOptions) return value

    const option = (options as DropdownOption[]).find((opt) => opt._id === value)
    return option ? option.name : value
  }

  // Check if a specific value is already applied for the current type
  const isValueAlreadyApplied = (value: string) => {
    return appliedFilters.some((filter) => filter.operator === type && filter.value === value)
  }

  // Clear values when type changes
  const handleTypeChange = (newType: 'is' | 'is_not') => {
    if (type !== newType) {
      setType(newType)
      setSelectedOption('')
      setIsApplyDisabled(false)
    } else {
      setType(newType)
    }
  }

  const handleApply = () => {
    if (!selectedOption) return

    // Check if this value is already applied for the current type
    if (isValueAlreadyApplied(selectedOption)) {
      setIsApplyDisabled(true)
      return
    }

    const query: Record<string, any> = {}
    const filterValue = { field: fieldKey, operator: type, value: selectedOption }
    query[fieldKey] = filterValue
    onApply(query)

    // Reset form after applying
    setSelectedOption('')
  }

  const handleReset = () => {
    setType('is')
    setSelectedOption('')
    setIsApplyDisabled(false)
    onReset()
  }

  useEffect(() => {
    const applied = appliedFilters.find((f) => f.field === fieldKey)
    if (applied) {
      setType(applied.operator)
      setSelectedOption(applied.value)
    }
  }, [appliedFilters, fieldKey])

  const shouldShowDropdown = options.length > 0 && supportedOperators.includes(type)

  const formatOperatorLabel = (operator: string) => {
    return operator.replace(/_/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase()) // Capitalize each word
  }
  console.log({ appliedFilters })
  return (
    <SharedStyled.FlexCol gap="8px">
      <SharedStyled.Text fontWeight="600">{label}</SharedStyled.Text>

      {/* Display applied filters */}
      {appliedFilters && appliedFilters.length > 0 && (
        <SharedStyled.FlexCol gap="8px" margin="0 0 16px 0">
          <SharedStyled.Text fontWeight="600">Applied Filters:</SharedStyled.Text>
          <Styled.AppliedFiltersContainer>
            {appliedFilters.map((filter, index) => (
              <Styled.AppliedFilterTag key={index}>
                {`${formatOperatorLabel(filter.operator)}: ${getDisplayName(filter.value)}`}
                <Styled.RemoveFilterButton onClick={() => onRemoveFilter(fieldKey, filter.operator, filter.value)}>
                  ×
                </Styled.RemoveFilterButton>
              </Styled.AppliedFilterTag>
            ))}
          </Styled.AppliedFiltersContainer>
        </SharedStyled.FlexCol>
      )}

      <SharedStyled.FlexCol gap="4px">
        {supportedOperators.map((operator) => (
          <Styled.RadioWrapper key={operator}>
            <input
              type="radio"
              name={`${fieldKey}-type`}
              value={operator}
              checked={type === operator}
              onChange={() => handleTypeChange(operator as any)}
            />
            <SharedStyled.Text>{formatOperatorLabel(operator)}</SharedStyled.Text>
          </Styled.RadioWrapper>
        ))}
      </SharedStyled.FlexCol>

      {shouldShowDropdown && (
        <Styled.Select value={selectedOption} onChange={(e) => setSelectedOption(e.target.value)}>
          <option value="">-- Select Option --</option>
          {isObjectOptions
            ? (options as DropdownOption[]).map((opt) => (
                <option key={opt._id} value={opt._id}>
                  {opt.name}
                </option>
              ))
            : (options as string[]).map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
        </Styled.Select>
      )}

      <SharedStyled.FlexRow gap="8px">
        <Button disabled={!selectedOption || isApplyDisabled} onClick={handleApply} padding="6px 12px">
          Apply
        </Button>
        <Button onClick={handleReset} className="gray" padding="6px 12px">
          Reset
        </Button>
      </SharedStyled.FlexRow>
    </SharedStyled.FlexCol>
  )
}

export default DropdownFilter
