import React, { useState, useEffect } from 'react'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from '../style'
import Button from '../../components/button/Button'

interface NumberFilterProps {
  label: string
  fieldKey: string
  onApply: (query: Record<string, any>) => void
  onReset: () => void
  onTypeChange: () => void
  onRemoveFilter: (fieldKey: string, operator: string, value?: string) => void
  appliedFilters?: any[]
}

const NumberFilter: React.FC<NumberFilterProps> = ({
  label,
  fieldKey,
  onApply,
  onReset,
  onTypeChange,
  onRemoveFilter,
  appliedFilters = [],
}) => {
  const [type, setType] = useState<'more' | 'less' | 'range'>('more')
  const [value, setValue] = useState('')
  const [isApplyDisabled, setIsApplyDisabled] = useState(false)

  // For range inputs
  const [min, setMinValue] = useState('')
  const [max, setMaxValue] = useState('')

  // Check if a specific filter type is already applied
  const isFilterTypeApplied = (filterType: string) => {
    return appliedFilters.some((filter) => filter.operator === filterType)
  }

  // Update disabled state when appliedFilters change
  useEffect(() => {
    setIsApplyDisabled(isFilterTypeApplied(type))
  }, [appliedFilters, type])

  // Clear values when type changes
  const handleTypeChange = (newType: 'more' | 'less' | 'range') => {
    if (type !== newType) {
      setType(newType)
      setValue('')
      setMinValue('')
      setMaxValue('')
      // Check if the new type is already applied
      setIsApplyDisabled(isFilterTypeApplied(newType))
    } else {
      setType(newType)
    }
  }

  const handleApply = () => {
    // Validate inputs
    if (type === 'range') {
      if (!min || !max) return
    } else if (!value) {
      return
    }

    const query: Record<string, any> = {}
    let filterValue

    if (type === 'range') {
      filterValue = {
        field: fieldKey,
        operator: type,
        min: Number(min),
        max: Number(max),
      }
    } else {
      filterValue = {
        field: fieldKey,
        operator: type,
        value: Number(value),
      }
    }

    query[fieldKey] = filterValue
    onApply(query)

    // Reset form after applying
    setValue('')
    setMinValue('')
    setMaxValue('')
  }

  const handleReset = () => {
    setValue('')
    setMinValue('')
    setMaxValue('')
    setType('more')
    setIsApplyDisabled(false)
    onReset()
  }

  return (
    <SharedStyled.FlexCol gap="8px">
      <SharedStyled.Text fontWeight="600">{label}</SharedStyled.Text>

      {/* Display applied filters */}
      {appliedFilters && appliedFilters.length > 0 && (
        <SharedStyled.FlexCol gap="8px" margin="0 0 16px 0">
          <SharedStyled.Text fontWeight="600">Applied Filters:</SharedStyled.Text>
          <Styled.AppliedFiltersContainer>
            {appliedFilters.map((filter, index) => (
              <Styled.AppliedFilterTag key={index}>
                {filter.operator === 'more' && `More Than: ${filter.value}`}
                {filter.operator === 'less' && `Less Than: ${filter.value}`}
                {filter.operator === 'range' && `Range: ${filter.min} - ${filter.max}`}
                <Styled.RemoveFilterButton
                  onClick={() => onRemoveFilter(fieldKey, filter.operator, filter.value?.toString())}
                >
                  ×
                </Styled.RemoveFilterButton>
              </Styled.AppliedFilterTag>
            ))}
          </Styled.AppliedFiltersContainer>
        </SharedStyled.FlexCol>
      )}

      <SharedStyled.FlexCol gap="12px">
        {['more', 'less', 'range'].map((opt) => (
          <SharedStyled.FlexCol gap="8px" key={opt}>
            <Styled.RadioWrapper>
              <input
                type="radio"
                name={`${fieldKey}-type`}
                value={opt}
                checked={type === opt}
                onChange={() => handleTypeChange(opt as any)}
              />
              <SharedStyled.Text>
                {opt === 'more' && 'More Than'}
                {opt === 'less' && 'Less Than'}
                {opt === 'range' && 'Range'}
              </SharedStyled.Text>
            </Styled.RadioWrapper>

            {type === opt && (
              <>
                {type === 'range' ? (
                  <SharedStyled.FlexCol gap="8px">
                    <SharedStyled.FlexRow gap="8px" alignItems="center">
                      <SharedStyled.Text>From:</SharedStyled.Text>
                      <Styled.Input
                        type="number"
                        value={min}
                        onChange={(e) => setMinValue(e.target.value)}
                        placeholder="Min value"
                      />
                    </SharedStyled.FlexRow>
                    <SharedStyled.FlexRow gap="8px" alignItems="center">
                      <SharedStyled.Text>To:</SharedStyled.Text>
                      <Styled.Input
                        type="number"
                        value={max}
                        onChange={(e) => setMaxValue(e.target.value)}
                        placeholder="Max value"
                      />
                    </SharedStyled.FlexRow>
                  </SharedStyled.FlexCol>
                ) : (
                  <Styled.Input
                    type="number"
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                    placeholder="Enter number"
                  />
                )}
              </>
            )}
          </SharedStyled.FlexCol>
        ))}
      </SharedStyled.FlexCol>

      <SharedStyled.FlexRow gap="8px">
        <Button
          disabled={
            // Disable if required fields are empty
            (type === 'range' && (!min || !max)) ||
            (type !== 'range' && !value) ||
            // Or if this filter type is already applied
            isApplyDisabled
          }
          onClick={handleApply}
          padding="6px 12px"
        >
          Apply
        </Button>
        <Button onClick={handleReset} className="gray" padding="6px 12px">
          Reset
        </Button>
      </SharedStyled.FlexRow>
    </SharedStyled.FlexCol>
  )
}

export default NumberFilter
