import React, { useState, useEffect } from 'react'
import DynamicFieldFilter from './DynamicFieldFilter'
import { convertFiltersToMongoQuery } from './constant'
import * as SharedStyled from '../../styles/styled'
import Button from '../components/button/Button'
import { colors } from '../../styles/theme'

interface FieldDefinition {
  label: string
  key: string
  type: 'string' | 'dropdown' | 'date'
  values?: string[]
}

interface FiltersPanelProps {
  fields: FieldDefinition[]
  onFiltersApplied: (query: Record<string, any[]>) => void
  width?: string
  background?: string
  initialFilters?: Record<string, any[]>
  onResetAll?: () => void
}

const FiltersPanel: React.FC<FiltersPanelProps> = ({
  fields,
  onFiltersApplied,
  width = '30%',
  background = colors.fadedGrey,
  initialFilters = {},
  onResetAll,
}) => {
  // Changed to store arrays of filter objects for each field
  const [queryObj, setQueryObj] = useState<Record<string, any[]>>(initialFilters)

  // Update state when initialFilters changes
  useEffect(() => {
    setQueryObj(initialFilters)
  }, [initialFilters])

  const handleApply = (query: Record<string, any>) => {
    // Get the field key and value from the query
    const fieldKey = Object.keys(query)[0]
    const fieldValue = query[fieldKey]

    // Update the state with the new filter
    setQueryObj((prev) => {
      const updated = { ...prev }

      // Initialize array if it doesn't exist
      if (!updated[fieldKey]) {
        updated[fieldKey] = []
      }

      // Check if we're applying a different type of filter than what's already applied
      const existingFilters = updated[fieldKey] || []
      const newFilterType = fieldValue.operator

      // Remove any existing filters of different types for this field
      // This ensures that when switching from 'is' to 'is_empty', previous 'is' filters are removed
      const filteredExistingFilters = existingFilters.filter((filter) => {
        // Keep filters of the same type
        // For 'is' type, we can have multiple values, so keep all 'is' filters
        // For other types like 'is_empty', 'is_not_empty', we should only have one
        if (newFilterType === 'is' && filter.operator === 'is') {
          return true
        }

        // For non-'is' types, remove all existing filters of different types
        return filter.operator === newFilterType
      })

      // Add the new filter value to the filtered array
      updated[fieldKey] = [...filteredExistingFilters, fieldValue]

      // Make an immediate API call with the updated filters
      const data = convertFiltersToMongoQuery(updated)
      console.log('Individual Apply - Sending to API:', data)
      onFiltersApplied(updated)

      return updated
    })
  }

  const handleReset = (key: string) => {
    setQueryObj((prev) => {
      const updated = { ...prev }
      delete updated[key]

      // Make an immediate API call with the updated filters
      const data = convertFiltersToMongoQuery(updated)
      console.log('Reset Filter - Sending to API:', data)
      onFiltersApplied(updated)

      return updated
    })
  }

  const handleRemoveFilter = (key: string, operator: string, value?: string) => {
    setQueryObj((prev) => {
      const updated = { ...prev }
      if (!updated[key]) return prev

      const remaining = updated[key].filter((f) => {
        if (value !== undefined) {
          // If value is defined, match all 3: operator and value
          return !(f.operator == operator && f.value == value)
        }
        // If no value, just match operator
        return f.operator !== operator
      })

      if (remaining.length === 0) {
        delete updated[key]
      } else {
        updated[key] = remaining
      }

      const data = convertFiltersToMongoQuery(updated)
      console.log('Removed Filter - Sending to API:', data)
      onFiltersApplied(updated)

      return updated
    })
  }

  // Handle type change for a field - remove all existing filters for that field
  const handleTypeChange = (key: string) => {
    setQueryObj((prev) => {
      const updated = { ...prev }
      // Remove all filters for this field
      delete updated[key]

      // Make an immediate API call with the updated filters
      // const data = convertFiltersToMongoQuery(updated)
      // console.log('Type Changed - Sending to API:', data)
      // onFiltersApplied(updated)

      return updated
    })
  }

  const handleResetAll = () => {
    setQueryObj({})

    // Make an immediate API call with empty filters
    console.log('Reset All Filters - Sending to API:', {})
    onFiltersApplied({})

    // Call the onResetAll callback if provided
    if (onResetAll) {
      onResetAll()
    }
  }

  return (
    <SharedStyled.FlexCol width={width} padding="10px" background={background}>
      {fields.map((field) => (
        <DynamicFieldFilter
          key={field.key}
          field={field}
          onApply={handleApply}
          onReset={handleReset}
          onTypeChange={handleTypeChange}
          onRemoveFilter={handleRemoveFilter}
          appliedFilters={queryObj}
        />
      ))}

      <SharedStyled.FlexRow justifyContent="flex-end" gap="16px">
        <Button onClick={handleResetAll} className="gray" padding="6px 12px">
          Reset All
        </Button>
      </SharedStyled.FlexRow>
    </SharedStyled.FlexCol>
  )
}

export default FiltersPanel
